package com.spup.core.service;

import java.util.List;

import org.springframework.data.domain.Page;

import com.spup.commons.api.CommonResult;
import com.spup.core.dto.AppAdminTeamOrderListRequest;
import com.spup.core.dto.AppAppointmentTeamOrderRequest;
import com.spup.core.dto.AppTeamOrderListRequest;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;


public interface IAppAppointmentTeamOrderService extends IOrderService {
    //对外接口
    CommonResult<?> save(AppAppointmentTeamOrderRequest orderRequest, String unionid);
    CommonResult<?> getList(String unionid);
    Page<AppAppointmentTeamOrder> getTeamOrderList(AppTeamOrderListRequest queryParam);
    Page<AppAppointmentTeamOrder> getAdminTeamOrderList(AppAdminTeamOrderListRequest queryParam);
    CommonResult<?> cancel(String orderNo,String unionid);
    CommonResult<?> delete(String orderNo,String unionid);
    CommonResult<?> breaked(String orderNo,String unionid);

    CommonResult<?> getTodayTeamOrder();

    List<AppAppointmentTeamOrder> getTeamOrderByDate(String startDate, String endDate);
    CommonResult<?> listByParam(AppTeamOrderListRequest request);
    AppAppointmentTeamOrder view(Long id);
    AppAppointmentTeamOrder save(AppAppointmentTeamOrder teamOrder);
    AppAppointmentTeamOrder update(AppAppointmentTeamOrder modifyOrder);
}
