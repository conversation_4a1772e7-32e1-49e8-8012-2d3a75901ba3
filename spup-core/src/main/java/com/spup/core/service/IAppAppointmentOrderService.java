package com.spup.core.service;

import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.spup.commons.api.CommonResult;
import com.spup.core.dto.AppAppointmentOrderRequest;
import com.spup.data.entity.appointment.AppAppointmentOrder;


public interface IAppAppointmentOrderService extends IOrderService {
    //对外接口
    CommonResult<?> save(AppAppointmentOrderRequest orderRequest, String unionid) throws JsonProcessingException;

    CommonResult<?> getList(String unionid);

    CommonResult<?> cancel(String orderNo, String unionid);

    CommonResult<?> breaked(String orderNo, String unionid);

    CommonResult<?> delete(String orderNo);

    //内部接口
    List<AppAppointmentOrder> getListByUnionid(String unionid);

    AppAppointmentOrder getOrderByOrderNo(String orderNo);
}
