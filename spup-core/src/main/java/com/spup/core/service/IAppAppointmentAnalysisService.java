package com.spup.core.service;

import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;

import com.spup.core.dto.AnalysisListRequest;
import com.spup.data.entity.appointment.AppAppointmentAnalysis;

public interface IAppAppointmentAnalysisService {
    Map<String,Object> getAnaDataFromRecord(String yyyyMMdd);
    AppAppointmentAnalysis save(Map<String,Object> map);
    List<AppAppointmentAnalysis> getAnaDataByDate(String startDate, String endDate);
    Page<AppAppointmentAnalysis> listByPage(AnalysisListRequest listParam);
}
