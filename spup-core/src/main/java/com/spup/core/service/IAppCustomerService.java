package com.spup.core.service;

import org.springframework.data.domain.Page;

import com.spup.core.dto.CustomerListRequest;
import com.spup.data.entity.authority.AppCustomer;

/**
 * Admin Customer Service Interface
 * Extends BaseCustomerService with admin-specific operations
 */
public interface IAppCustomerService extends BaseCustomerService {

    /**
     * Get paginated customer list (admin-specific)
     */
    Page<AppCustomer> getPageList(CustomerListRequest param);
}
