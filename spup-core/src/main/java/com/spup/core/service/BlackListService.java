package com.spup.core.service;

import java.util.List;

import com.spup.data.entity.appointment.BlackList;

public interface BlackListService {
    List<BlackList> findInEffect();

    boolean isInBlackList(String unionid, BlackList.CategoryEnum categoryEnum);

    BlackList addBlackList(String unionid, BlackList.CategoryEnum categoryEnum);

    BlackList removeBlackList(Long id);

    boolean isBreakRule(String unionid, BlackList.CategoryEnum categoryEnum);

    BlackList save(BlackList entity);
}
