package com.spup.core.service;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.spup.commons.api.CommonResult;
import com.spup.core.dto.AppAppointmentItemOrderRequest;


public interface IAppAppointmentItemOrderService extends IOrderService {
    //对外接口
    public CommonResult<?> save(AppAppointmentItemOrderRequest orderRequest, String unionid) throws JsonProcessingException, IOException;
    public CommonResult<?> getList(String unionid);
    public CommonResult<?> cancel(String orderNo,String unionid);
    public CommonResult<?> delete(String orderNo,String unionid);
    public CommonResult<?> breaked(String orderNo,String unionid);
}
