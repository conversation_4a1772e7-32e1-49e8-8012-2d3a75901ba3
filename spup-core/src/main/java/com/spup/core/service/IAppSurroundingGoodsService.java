package com.spup.core.service;

import org.springframework.data.domain.Page;

import com.spup.core.dto.GoodsListRequest;
import com.spup.data.entity.AppSurroundingGoods;

/**
 * User Surrounding Goods Service Interface
 * Extends BaseSurroundingGoodsService with user-specific operations
 */
public interface IAppSurroundingGoodsService extends BaseSurroundingGoodsService {

    /**
     * Get paginated goods list (user-specific)
     */
    Page<AppSurroundingGoods> getListByPage(GoodsListRequest listParam);
}
