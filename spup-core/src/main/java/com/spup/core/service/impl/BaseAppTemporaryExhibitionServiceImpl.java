package com.spup.core.service.impl;

import com.spup.core.service.IAppTemporaryExhibitionService;
import org.springframework.stereotype.Service;

/**
 * Base implementation for IAppTemporaryExhibitionService
 * Contains common logic shared between modules
 * Extend this class in module-specific implementations
 */
@Service
public abstract class BaseAppTemporaryExhibitionServiceImpl implements IAppTemporaryExhibitionService {
    
    /**
     * Common initialization logic
     */
    protected void initializeCommon() {
        // Common initialization code
    }
    
    /**
     * Module-specific initialization (to be implemented by subclasses)
     */
    protected abstract void initializeModuleSpecific();
    
    /**
     * Common validation logic
     */
    protected boolean validateCommon(Object entity) {
        return entity != null;
    }
    
    /**
     * Module-specific validation (to be implemented by subclasses)
     */
    protected abstract boolean validateModuleSpecific(Object entity);
}
