package com.spup.core.service;


import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import com.spup.data.entity.appointment.AppBatch;

public interface IAppBatchService {
    AppBatch getByNo(String batchNo, Byte batchCategory);
    Map<String,List<AppBatch>> getListByDate(Byte category, String startDate, String endDate);

    List<AppBatch> getListByDate(Byte category, String date);
    List<AppBatch> getListByDate(String exhibitionNo,Byte category, String date);
    AppBatch save(AppBatch batch);
    AppBatch update(AppBatch batch);
    AppBatch updateRemaining(String batchNo,Byte batchCategory,int updateNum);
    void closeOverTime();

    int initTicket(LocalDate startDate, LocalDate endDate);

    int initTeam(LocalDate startDate,LocalDate endDate);

    int initItem(LocalDate startDate,LocalDate endDate);

    void init(LocalDate startDate, LocalDate endDate, byte category, String[][] times, int[] ticketTotal);

    void init(LocalDate startDate, LocalDate endDate, byte category, String[][] times, int[] ticketTotal , String remark);

}
