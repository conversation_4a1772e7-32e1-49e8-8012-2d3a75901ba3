package com.spup.core.service;



import java.util.List;
import java.util.Optional;

import com.spup.data.entity.authority.AppCustomerContacts;

public interface IAppCustomerContactsService {
    AppCustomerContacts save(AppCustomerContacts contacts);
    Optional<AppCustomerContacts> findById(Long contactsId);

    AppCustomerContacts modify(AppCustomerContacts contacts);
    int delete(Long contactId);
    List<AppCustomerContacts> getListByOwner(String ownerUnionid);
    List<AppCustomerContacts> getListByOwner(String yyyyMMdd,String ownerUnionid);
}
