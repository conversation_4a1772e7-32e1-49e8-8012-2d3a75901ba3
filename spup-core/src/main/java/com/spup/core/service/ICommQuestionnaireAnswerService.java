package com.spup.core.service;


import java.util.List;

import org.springframework.data.domain.Page;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.core.dto.QuestionnaireListRequest;
import com.spup.data.entity.CommQuestionnaireAnswer;

public interface ICommQuestionnaireAnswerService {
    CommQuestionnaireAnswer save(CommQuestionnaireAnswer answer, String unionid);
    List<CommQuestionnaireAnswer> getAllAnswer(Long questionnaireId, String unionid);
    List<CommQuestionnaireAnswer> getAllAnswer(Long questionnaireId, QuestionnaireListRequest listParam);
    Page<CommQuestionnaireAnswer> getPageList(QuestionnaireListRequest listParam);
    ObjectNode getAnswerDetail(Long answerId) throws JsonProcessingException;

}
