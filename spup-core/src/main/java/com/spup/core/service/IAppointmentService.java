package com.spup.core.service;

import java.time.LocalDate;
import java.util.Optional;

import com.spup.data.entity.ExhibitionInfo;
import com.spup.data.entity.RoundConfig;

public interface IAppointmentService {
    Optional<ExhibitionInfo> getExhibitionById(Long id);
    Optional<RoundConfig> getRoundByRoundDateAndExhibitionId(String exhibitionId, LocalDate date);
    Boolean isAvailableByRound(String exhibitionId, LocalDate date);
    Boolean isAvailableByHoliday(String exhibitionId, LocalDate date);

    RoundConfig.RoundStatusEnum getRoundStatus(String exhibitionId, LocalDate roundDate);
}
