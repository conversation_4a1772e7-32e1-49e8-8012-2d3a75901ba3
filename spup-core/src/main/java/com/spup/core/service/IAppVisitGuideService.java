package com.spup.core.service;

import java.util.List;

import org.springframework.data.domain.Page;

import com.spup.core.dto.VisitGuideListRequest;
import com.spup.data.entity.AppVisitGuide;

public interface IAppVisitGuideService {
    Page<AppVisitGuide> getListByPage(VisitGuideListRequest listParam);

    List<AppVisitGuide> findAllSortByCode(int limit);

    List<AppVisitGuide> findAllSortByPageViews(int limit);

    AppVisitGuide create(AppVisitGuide appVisitGuide, String openId);

    AppVisitGuide update(AppVisitGuide appVisitGuide, String openId);

    int delete(long id, String openId);

    AppVisitGuide view(long id);

    List<AppVisitGuide> getAllList();

    void read(long id);
}
