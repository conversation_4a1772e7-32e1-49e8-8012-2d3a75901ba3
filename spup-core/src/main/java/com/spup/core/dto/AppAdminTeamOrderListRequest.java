package com.spup.core.dto;


import java.time.LocalDate;

import com.spup.commons.api.PageInfo;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AppAdminTeamOrderListRequest extends PageInfo {
    private String exhibitionNo;
    private String teamName;
    private LocalDate startDate;
    private LocalDate endDate;
    private Short orderStatus;
    private AppAppointmentTeamOrder.MethodEnum method;
}
