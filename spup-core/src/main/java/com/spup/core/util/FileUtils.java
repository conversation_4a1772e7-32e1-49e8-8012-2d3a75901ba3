package com.spup.core.util;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import com.spup.commons.utils.DateTimeUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * Common File Utilities
 * Centralized file handling utilities
 */
@Slf4j
public class FileUtils {

    /**
     * Calculate MD5 hash of a file
     */
    public static String calculateMd5(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            return DigestUtils.md5DigestAsHex(inputStream);
        } catch (IOException e) {
            log.error("Error calculating MD5 for file: {}", file.getOriginalFilename(), e);
            return null;
        }
    }

    /**
     * Create directory if it doesn't exist
     */
    public static boolean createDirectoryIfNotExists(String directoryPath) {
        try {
            Path path = Paths.get(directoryPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                log.debug("Created directory: {}", directoryPath);
                return true;
            }
            return true;
        } catch (IOException e) {
            log.error("Error creating directory: {}", directoryPath, e);
            return false;
        }
    }

    /**
     * Create directory if it doesn't exist (File version)
     */
    public static boolean createDirectoryIfNotExists(File directory) {
        if (!directory.exists()) {
            boolean created = directory.mkdirs();
            if (created) {
                log.debug("Created directory: {}", directory.getAbsolutePath());
            } else {
                log.error("Failed to create directory: {}", directory.getAbsolutePath());
            }
            return created;
        }
        return true;
    }

    /**
     * Generate unique filename with timestamp
     */
    public static String generateUniqueFilename(String originalFilename) {
        String timestamp = DateTimeUtils.getCurrentDateTimeAsYyyyMmDdHhMmSs();
        return timestamp + "_" + originalFilename;
    }

    /**
     * Generate unique filename with custom prefix
     */
    public static String generateUniqueFilename(String prefix, String originalFilename) {
        String timestamp = DateTimeUtils.getCurrentDateTimeAsYyyyMmDdHhMmSs();
        return prefix + "_" + timestamp + "_" + originalFilename;
    }

    /**
     * Get file extension from filename
     */
    public static String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * Get filename without extension
     */
    public static String getFilenameWithoutExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
    }

    /**
     * Build file path with date-based directory structure
     */
    public static String buildDateBasedPath(String basePath, String section) {
        String year = DateTimeUtils.getCurrentYear();
        String month = DateTimeUtils.getCurrentMonth();
        String day = DateTimeUtils.getCurrentDay();
        
        return basePath + File.separator + section + File.separator + 
               year + File.separator + month + File.separator + day;
    }

    /**
     * Build file path with simple date directory (yyyyMMdd)
     */
    public static String buildSimpleDatePath(String basePath) {
        String dateStr = DateTimeUtils.getCurrentDateAsYyyyMmDd();
        return basePath + File.separator + dateStr;
    }

    /**
     * Save multipart file to specified path
     */
    public static boolean saveFile(MultipartFile file, String filePath) {
        try {
            File targetFile = new File(filePath);
            
            // Create parent directories if they don't exist
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                createDirectoryIfNotExists(parentDir);
            }
            
            file.transferTo(targetFile);
            log.debug("Saved file to: {}", filePath);
            return true;
        } catch (IOException e) {
            log.error("Error saving file to: {}", filePath, e);
            return false;
        }
    }

    /**
     * Check if file exists
     */
    public static boolean fileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }

    /**
     * Delete file if exists
     */
    public static boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                log.debug("Deleted file: {}", filePath);
                return true;
            }
            return false;
        } catch (IOException e) {
            log.error("Error deleting file: {}", filePath, e);
            return false;
        }
    }

    /**
     * Get file size in bytes
     */
    public static long getFileSize(String filePath) {
        try {
            return Files.size(Paths.get(filePath));
        } catch (IOException e) {
            log.error("Error getting file size: {}", filePath, e);
            return -1;
        }
    }

    /**
     * Validate file extension against allowed extensions
     */
    public static boolean isValidFileExtension(String filename, String[] allowedExtensions) {
        if (filename == null || allowedExtensions == null) {
            return false;
        }
        
        String extension = getFileExtension(filename).toLowerCase();
        for (String allowed : allowedExtensions) {
            if (allowed.toLowerCase().equals(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Validate file size
     */
    public static boolean isValidFileSize(MultipartFile file, long maxSizeInBytes) {
        return file != null && file.getSize() <= maxSizeInBytes;
    }
}
