package com.spup.core.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.spup.commons.api.CommonResult;

/**
 * Base Exception Controller Advice
 * Provides common exception handling with module-specific customization
 */
@RestControllerAdvice
public abstract class BaseExceptionControllerAdvice {
    
    private static final Logger logger = LoggerFactory.getLogger(BaseExceptionControllerAdvice.class);
    
    /**
     * Handle general exceptions
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public CommonResult<String> handleException(Exception e) {
        logger.error("Unhandled exception in {}: {}", getModuleName(), e.getMessage(), e);
        return CommonResult.failed("Internal server error: " + e.getMessage());
    }
    
    /**
     * Handle illegal argument exceptions
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<String> handleIllegalArgumentException(IllegalArgumentException e) {
        logger.warn("Invalid argument in {}: {}", getModuleName(), e.getMessage());
        return CommonResult.failed("Invalid argument: " + e.getMessage());
    }
    
    /**
     * Get module name for logging (to be implemented by subclasses)
     */
    protected abstract String getModuleName();
}
