package com.spup.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Enhanced Base Web Configuration
 * Provides common resource handlers and web MVC configuration
 */
@Configuration
public abstract class BaseWebConfig implements WebMvcConfigurer {
    
    /**
     * Add common resource handlers (Swagger, static resources)
     */
    protected void addCommonResourceHandlers(ResourceHandlerRegistry registry) {
        // Static resources
        registry.addResourceHandler("/html/**")
                .addResourceLocations("classpath:/html/");
        
        // Swagger UI resources
        registry.addResourceHandler("/swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        
        // API documentation
        registry.addResourceHandler("/doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
    }
    
    /**
     * Module-specific resource handlers (to be implemented by subclasses)
     */
    protected void addModuleSpecificResourceHandlers(ResourceHandlerRegistry registry) {
        // Default implementation - no additional handlers
    }
    
    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // Add common resource handlers
        addCommonResourceHandlers(registry);
        
        // Add module-specific resource handlers
        addModuleSpecificResourceHandlers(registry);
    }
}
