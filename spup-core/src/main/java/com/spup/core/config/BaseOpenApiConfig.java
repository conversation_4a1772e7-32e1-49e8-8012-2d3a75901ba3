package com.spup.core.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Base OpenAPI Configuration
 * Provides common Swagger/OpenAPI configuration with module-specific customization
 */
@Configuration
public abstract class BaseOpenApiConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title(getApiTitle())
                        .description(getApiDescription())
                        .version(getApiVersion())
                        .contact(new Contact()
                                .name("SPUP Development Team")
                                .email("<EMAIL>")));
    }
    
    /**
     * Get API title (to be implemented by subclasses)
     */
    protected abstract String getApiTitle();
    
    /**
     * Get API description (to be implemented by subclasses)
     */
    protected abstract String getApiDescription();
    
    /**
     * Get API version (default implementation)
     */
    protected String getApiVersion() {
        return "1.0.0";
    }
}
