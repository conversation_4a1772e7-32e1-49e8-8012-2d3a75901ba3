package com.spup.core.test.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.test.context.TestPropertySource;

/**
 * Base Test Utilities
 * Common testing utilities shared across all modules
 */
@TestPropertySource(locations = "classpath:application-test.properties")
public class BaseTestUtils {
    
    /**
     * Get configured ObjectMapper for testing
     */
    public static ObjectMapper getTestObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }
    
    /**
     * Create test data with common patterns
     */
    public static <T> T createTestData(Class<T> clazz) {
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create test data for " + clazz.getSimpleName(), e);
        }
    }
    
    /**
     * Common test validation
     */
    public static boolean isValidTestObject(Object obj) {
        return obj != null;
    }
    
    /**
     * Generate test ID
     */
    public static String generateTestId(String prefix) {
        return prefix + "_" + System.currentTimeMillis();
    }
}
