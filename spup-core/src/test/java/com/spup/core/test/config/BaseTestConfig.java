package com.spup.core.test.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * Base Test Configuration
 * Common test configuration shared across all modules
 */
@TestConfiguration
@Profile("test")
public class BaseTestConfig {
    
    /**
     * Test-specific bean configurations
     */
    @Bean
    @Primary
    public String testEnvironment() {
        return "test";
    }
}
