package com.spup.core.test.utils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Mock Data Factory for Testing
 * Generates consistent test data across modules
 */
public class MockDataFactory {
    
    private static final Random random = new Random();
    
    /**
     * Generate mock string with prefix
     */
    public static String mockString(String prefix) {
        return prefix + "_" + random.nextInt(10000);
    }
    
    /**
     * Generate mock integer
     */
    public static Integer mockInteger() {
        return random.nextInt(1000) + 1;
    }
    
    /**
     * Generate mock long
     */
    public static Long mockLong() {
        return random.nextLong();
    }
    
    /**
     * Generate mock LocalDateTime
     */
    public static LocalDateTime mockDateTime() {
        return LocalDateTime.now().plusDays(random.nextInt(30));
    }
    
    /**
     * Generate mock list
     */
    public static <T> List<T> mockList(Class<T> clazz, int size) {
        List<T> list = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            try {
                list.add(clazz.getDeclaredConstructor().newInstance());
            } catch (Exception e) {
                // Skip if can't instantiate
            }
        }
        return list;
    }
}
