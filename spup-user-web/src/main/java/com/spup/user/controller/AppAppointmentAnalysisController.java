package com.spup.user.controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.spup.commons.api.CommonResult;
import com.spup.core.dto.AppTeamOrderListRequest;
import com.spup.core.service.IAppAppointmentAnalysisService;
import com.spup.core.service.IAppAppointmentTeamOrderService;
import com.spup.data.dao.appointment.AppAppointmentTeamOrderDao;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;
import com.spup.user.dto.AppTeamOrderConfirmRequest;

import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "数据汇总")
@RestController
@RequestMapping("/analysis")
public class AppAppointmentAnalysisController {
    @Resource
    private IAppAppointmentAnalysisService iAppAppointmentAnalysisService;
    @Resource
    private AppAppointmentTeamOrderDao appAppointmentTeamOrderDao;
    @Resource
    private IAppAppointmentTeamOrderService iAppAppointmentTeamOrderService;

    //@ApiOperation(value = "获取当日数据")
    //@RequestMapping(value = "/realtime",method = RequestMethod.GET)
    public CommonResult<?> realtime(HttpServletRequest request) {
        LocalDate date = LocalDate.now();
        String yyyyMMdd = request.getParameter("queryDate");
        if(!StringUtils.hasLength(yyyyMMdd)) {
            yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        Map<String,Object> map =  iAppAppointmentAnalysisService.getAnaDataFromRecord(yyyyMMdd);
        map.put("teamOrders",appAppointmentTeamOrderDao.findByBatchDateBetween(yyyyMMdd,yyyyMMdd));
        return CommonResult.succeeded(map);
    }

    //@ApiOperation(value = "获取后续团队预约")
    //@RequestMapping(value = "/getNewTeamOrder",method = RequestMethod.GET)
    public CommonResult<?> getNewTeamOrder(HttpServletRequest request)  {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = LocalDate.now();
        endDate = endDate.plus(30, ChronoUnit.DAYS);
        String yyyyMMdd_start = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String yyyyMMdd_end = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        //System.out.println(yyyyMMdd_end);
        return CommonResult.succeeded(appAppointmentTeamOrderDao.findByBatchDateBetween(yyyyMMdd_start,yyyyMMdd_end));
    }

    /*@ApiOperation(value = "获取团队预约(此接口会按照参观日期进行升序)")
    @PostMapping(value = "/getTeamOrderList")*/
    public CommonResult<?> getTeamOrderList(@RequestBody AppTeamOrderListRequest params)  {
        //System.out.println(yyyyMMdd_end);
        return iAppAppointmentTeamOrderService.listByParam(params);
    }
    /*@ApiOperation(value = "接待确认")
    @PostMapping(value = "/confirmTeam")*/
    public CommonResult<?> confirmTeam(@RequestBody AppTeamOrderConfirmRequest confirmRequest)  {
        //System.out.println(yyyyMMdd_end);
        AppAppointmentTeamOrder teamOrder = new AppAppointmentTeamOrder();
        BeanUtils.copyProperties(confirmRequest,teamOrder);
        return CommonResult.succeeded(appAppointmentTeamOrderDao.save(teamOrder));
    }

}
