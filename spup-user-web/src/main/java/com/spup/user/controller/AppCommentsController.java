package com.spup.user.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.spup.commons.api.CommonResult;
import com.spup.core.service.IAppCommentsService;
import com.spup.data.entity.AppComments;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "留言")
@RestController
@RequestMapping("/comment")
public class AppCommentsController {
    @Autowired
    private IAppCommentsService iAppCommentsService;

    @Operation(summary = "留言")
    @RequestMapping(value = "/save",method = RequestMethod.POST)
    public CommonResult<?> getAvailableTime(@RequestBody AppComments comments , HttpServletRequest request)  {
        String unionid = (String)request.getSession().getAttribute("unionid");

        comments.setCustomer(unionid);
        return CommonResult.succeeded(iAppCommentsService.save(comments));
    }
}
