package com.spup.user.controller;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Calendar;
import java.util.HashMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.commons.api.CommonResult;
import com.spup.commons.utils.JWTUtil;
import com.spup.core.service.IAppCustomerService;
import com.spup.core.service.IAppOperateLogService;
import com.spup.data.entity.authority.AppCustomer;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "公众号登录")
@RestController
@RequestMapping("/mp")
@PropertySource("classpath:/miniprogram.properties")
/*@ConfigurationProperties(prefix = "wx.open")*/
public class AppMpLoginController {
    private static final Logger logger = LoggerFactory.getLogger(AppMpLoginController.class);

    // JWTUtil is now a static utility class - no injection needed



    @Autowired
    private IAppCustomerService iAppCustomerService;

    @Resource
    private IAppOperateLogService iAppOperateLogService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RestTemplate restTemplate;

    @Operation(summary = "公众号认证信息")
    @GetMapping(value="/mpcode2session/{code}")
    public CommonResult<?> mpcode2session (@PathVariable String code, HttpServletRequest req)  {
        String appID = "wxb971efaed01158f4";
        String appSecret = "8359f543f4c6df82c7a15c190262f28b";
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid="+appID+"&secret="+appSecret+"&code="+code+"&grant_type=authorization_code";
        ObjectNode retunObj = objectMapper.createObjectNode();
        try{
            //String responseStr = HttpRequestUtil.httpGet(url);
            ResponseEntity<String> responseEntity  = restTemplate.getForEntity(url, String.class);
            String responseStr = responseEntity.getBody();
            JsonNode resultJson = objectMapper.readTree(responseStr);
            if(resultJson.get("errcode")==null){
                String access_token = resultJson.get("access_token").asText();
             /*   Integer expires_in = resultJson.get("expires_in").numberValue().intValue();
                String refresh_token = resultJson.get("refresh_token").textValue();*/
                String openid = resultJson.get("openid").textValue();
//                String scope = resultJson.get("scope").textValue();
                String unionid = resultJson.get("unionid").textValue();

                String url2 = "https://api.weixin.qq.com/sns/userinfo?openid="+openid+"&access_token="+access_token+"&lang=zh_CN";
                //String result2 = HttpRequestUtil.httpGet(url2);
                ResponseEntity<String> responseEntity2  = restTemplate.getForEntity(url2, String.class);
                String result2 = responseEntity2.getBody();

                JsonNode jsonObject = objectMapper.readTree(result2);
                if(StringUtils.hasLength(jsonObject.get("openid").textValue())){
                    String nickname = jsonObject.get("nickname").textValue();
                    nickname = new String(nickname.getBytes(StandardCharsets.ISO_8859_1),StandardCharsets.UTF_8);
                    byte sex = jsonObject.get("sex").numberValue().byteValue();
                    String headimgurl = jsonObject.get("headimgurl").textValue();
                    //req. ().setAttribute("SESSION_KEY_WXUSERINFO", jsonObject);
                    //持久化客户信息
                    AppCustomer customer =  iAppCustomerService.save(unionid,openid,nickname,headimgurl,sex);
                    retunObj.put("unionid", unionid);
                    retunObj.put("openid", openid);
                    retunObj.put("isNew", customer!=null?1:0);
                    //retunObj.put("access_token", access_token);
                    //token, jwt
                    retunObj.put("token", JWTUtil.getToken(unionid, openid));
                    logger.info("公众号认证成功：{}",retunObj.toString());

                    try {
                        iAppOperateLogService.saveLog(req);
                    } catch (Exception e){
                        e.printStackTrace();
                    }

                    return CommonResult.succeeded(retunObj);

                }else{
                    return CommonResult.failed("获取用户信息失败");
                }


            }else{
                return CommonResult.failed("获取token失败");
            }



        }catch (Exception e){
            System.out.println("------------------getWxUserInfo Exception------------------");
            e.printStackTrace();
        }
        return CommonResult.failed("处理失败");
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        // 指定token过期时间为10秒
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);

        String token = JWT.create()
                .withHeader(new HashMap<>())  // Header
                .withClaim("unionid", "ojqzL07qjyrcO7orDG14Q4GFqBtE")  // Payload
                .withClaim("openid", "o4a585ZnGo6BGCmliLlTNR3o4b_c")
                .withExpiresAt(calendar.getTime())  // 过期时间
                .sign(Algorithm.HMAC384("<EMAIL>"));  // 签名用的secret
        System.out.println(token);

        //eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.eyJ1bmlvbmlkIjoib2pxekwwN3FqeXJjTzdvckRHMTRRNEdGcUJ0RSIsIm9wZW5pZCI6Im80YTU4NVpuR282QkdDbWxpTGxUTlIzbzRiX2MiLCJleHAiOjE3MTM1MTA0MDV9.MFGwlWyJM2s5Y9zp6ciogJrlO13WA7_gCBfxswjapTKq1Db_tO8PDqHdGclFdmaG";

        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC384("<EMAIL>")).build();
        // 解析指定的token
        DecodedJWT decodedJWT = jwtVerifier.verify(token);
        // 获取解析后的token中的payload信息
        Claim userId = decodedJWT.getClaim("unionid");
        Claim userName = decodedJWT.getClaim("openid");
        System.out.println(userId.asString());
        System.out.println(userName.asString());
        // 输出超时时间
        System.out.println(decodedJWT.getExpiresAt());

    }

}
