package com.spup.user.controller;

import com.spup.commons.api.CommonResult;
import com.spup.data.entity.appointment.AppTemporaryExhibition;
import com.spup.core.service.IAppTemporaryExhibitionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Comparator;
import java.util.List;

@Tag(name = "临展")
@RestController
@RequestMapping("/exhibition")
public class AppTemporaryExhibitionController {
    @Resource
    private IAppTemporaryExhibitionService iAppTemporaryExhibitionService;

    @Operation(summary = "临展列表")
    @GetMapping(value = "/list")
    public CommonResult<?> list() throws ParseException {
        List<AppTemporaryExhibition> exhibitionList = iAppTemporaryExhibitionService.getExhibition();
        exhibitionList.sort(Comparator.comparing(AppTemporaryExhibition::getCreateTime).reversed());
        return CommonResult.succeeded(exhibitionList);
    }

    @Operation(summary = "临展详情")
    @GetMapping(value = "/detail/{exhibitionNo}")
    public CommonResult<?> detail(@PathVariable String exhibitionNo)  {
        return CommonResult.succeeded(iAppTemporaryExhibitionService.getExhibitionDetail(exhibitionNo));
    }
}
