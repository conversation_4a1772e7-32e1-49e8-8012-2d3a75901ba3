package com.spup.user.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.spup.commons.api.CommonResult;
import com.spup.core.service.IAppVisitGuideService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "导览")
@RestController
@RequestMapping("/guide")
public class AppVisitGuideController {
    @Autowired
    private IAppVisitGuideService iAppVisitGuideService;
    @Operation(summary = "获取导览数据")
    @RequestMapping(value = "/getData",method = RequestMethod.GET)
    public CommonResult<?> getData()  {
        return CommonResult.succeeded(iAppVisitGuideService.getAllList());
    }

    @Operation(summary = "展项浏览")
    @RequestMapping(value = "/read/{id}",method = RequestMethod.GET)
    public CommonResult<?> read(@PathVariable Long id)  {
        iAppVisitGuideService.read(id);
        return CommonResult.succeeded("");
    }
}
