package com.spup.user.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.spup.commons.api.CommonResult;
import com.spup.core.dto.GoodsListRequest;
import com.spup.core.service.IAppSurroundingGoodsService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "文创商品展示")
@RestController
@RequestMapping(value = "/goods")
public class AppGoodsController {
    @Resource
    private IAppSurroundingGoodsService iAppSurroundingGoodsService;


    @Operation(summary = "文创商品列表")
    @GetMapping(value="/listByPage")
    public CommonResult<?> listByPage (GoodsListRequest param) {
        return CommonResult.succeeded(iAppSurroundingGoodsService.getListByPage(param));
    }



    @Operation(summary = "文创商品明细")
    @GetMapping(value="/viewGoods/{id}")
    public CommonResult<?> viewManageUser (@PathVariable Long id) {

        return CommonResult.succeeded(iAppSurroundingGoodsService.view(id));
    }

}
