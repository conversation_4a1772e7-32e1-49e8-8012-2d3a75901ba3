package com.spup.user.controller;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.spup.commons.api.CommonResult;
import com.spup.core.service.ICommQuestionnaireAnswerService;
import com.spup.core.service.ICommQuestionnaireService;
import com.spup.data.entity.CommQuestionnaire;
import com.spup.data.entity.CommQuestionnaireAnswer;
import com.spup.user.dto.CommQuestionnaireDTO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "问卷")
@RestController
@RequestMapping("/questionnare")
public class QuestionnareController {
    @Resource
    private ICommQuestionnaireService iCommQuestionnaireService;
    @Resource
    private ICommQuestionnaireAnswerService iCommQuestionnaireAnswerService;


    @Operation(summary = "获取问卷信息")
    @GetMapping(value = {"/get/{id}","/get"})
    public CommonResult<?> get(@PathVariable(value = "id" , required = false) Long id, HttpServletRequest request)  {
        String unionid = (String)request.getSession().getAttribute("unionid");
        //String openid = (String)request.getSession().getAttribute("openid");
        if(id == null){
            id = 1L;
        }
        List<CommQuestionnaireAnswer> allAnswer = iCommQuestionnaireAnswerService.getAllAnswer(id, unionid);
        //todo 判断是否可以提交
        LocalDate now = LocalDate.now();
        allAnswer = allAnswer.stream()
                .filter(commQuestionnaireAnswer -> commQuestionnaireAnswer.getCreateTime().toLocalDate().compareTo(now)==0)
                .collect(Collectors.toList());
        int submit = 0;
        String message = "";

        if(!CollectionUtils.isEmpty(allAnswer)){
            submit = allAnswer.size();
            message = "您已提交过此问卷";
        }
        CommQuestionnaire questionnaire = iCommQuestionnaireService.getQuestionnaireById(id);

        CommQuestionnaireDTO dto = new CommQuestionnaireDTO();
        BeanUtils.copyProperties(questionnaire,dto);
        dto.setHasSubmit(submit);
        dto.setMessage(message);
        return CommonResult.succeeded(dto);
//        return CommonResult.succeeded(objectNode);
    }

    @Operation(summary = "问卷提交")
    @PostMapping(value = "/save")
    public CommonResult<?> save(@RequestBody CommQuestionnaireAnswer answer, HttpServletRequest request)  {
        String unionid = (String)request.getSession().getAttribute("unionid");
        //String openid = (String)request.getSession().getAttribute("openid");
        CommQuestionnaireAnswer returnCommQuestionnaireAnswer = iCommQuestionnaireAnswerService.save(answer,unionid);
        return CommonResult.succeeded(returnCommQuestionnaireAnswer);

    }

}
