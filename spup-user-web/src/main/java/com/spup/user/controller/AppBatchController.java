package com.spup.user.controller;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.spup.commons.api.CommonResult;
import com.spup.commons.utils.DateTimeUtil;
import com.spup.core.dto.AvailableDayResponse;
import com.spup.data.entity.RoundConfig.RoundStatusEnum;
import com.spup.data.entity.appointment.AppBatch;
// import com.spup.data.entity.appointment.AppWorkday;
import com.spup.data.entity.sys.AppConfig;
import com.spup.enums.BatchCategoryEnum;
import com.spup.enums.OrderCategoryEnum;
import com.spup.user.service.IAppConfigService;
// import com.spup.user.service.IAppWorkdayService;
import com.spup.user.service.IAppointmentService;
import com.spup.user.service.appointment.IAppBatchService;
import com.spup.user.utils.AppointHelper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Tag(name = "场次查询")
@RestController
@RequestMapping("/appointment")
public class AppBatchController {

    @Resource
    private IAppBatchService appBatchService;

    // @Resource
    // private IAppWorkdayService appWorkdayService;

    @Resource
    private IAppConfigService configService;
    @Resource
    private IAppointmentService appointmentService;


    // private int week = 1;

    @Operation(summary = "获取预约时间")
    @RequestMapping(value = "/getTime/{category}", method = RequestMethod.GET)
    public CommonResult<?> getTime(@PathVariable Number category) throws ParseException {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(3);
        // Calendar start_c = Calendar.getInstance();

        // Calendar end_c = Calendar.getInstance();
        // end_c.add(Calendar.DAY_OF_MONTH, 3);

        // int start_week = start_c.get(Calendar.DAY_OF_WEEK);
        if (category.byteValue() == OrderCategoryEnum.TEAM.getCode().byteValue()) {
            // start_c.add(Calendar.DAY_OF_MONTH, 7);
            startDate = startDate.plusDays(7);
            // end_c.add(Calendar.DAY_OF_MONTH, 7);
            endDate = endDate.plusDays(7);
        }

        String startDateString = startDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        // DateTimeUtil.getTime(DateTimeUtil.PATTERN_7, start_c);
        String endDateString = endDate.format(DateTimeFormatter.BASIC_ISO_DATE);
        // DateTimeUtil.getTime(DateTimeUtil.PATTERN_7, end_c);

        return CommonResult.succeeded(appBatchService.getListByDate(category.byteValue(), startDateString, endDateString));
    }

    @Operation(summary = "获取可预约日期范围")
    @RequestMapping(value = "/getAvailableTime/{category}", method = RequestMethod.GET)
    public CommonResult<?> getAvailableTime(@PathVariable Number category) {
        log.info("/getAvailableTime/category:" + category);
        Map<String, Object> configsByGroup = configService.getConfigsByGroup("appointment.category." + category);
        
        String exhibitionId = "team";
        if (category.intValue() == BatchCategoryEnum.TICKET.getCode().intValue()) {
            exhibitionId = "ticket";
        }

        List<AvailableDayResponse> availableDates = getAvailableDatesList((byte)category.intValue(),exhibitionId);
        // TODO configByGroup 中没有offset.day
        // 默认查询30天数据，然后再进行筛选
        // LocalDate date = LocalDate.now();
        // LocalDate endDate = date.plusDays(Integer.parseInt((String) configsByGroup.get("offset.day"))
        //         + Integer.parseInt((String) configsByGroup.get("appointment.days")));
        // date = date.plusDays(Integer.parseInt((String) configsByGroup.get("offset.day")));

        // String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // String yyyyMMdd_end = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // List<AppWorkday> listByDate = iAppWorkdayService.getListByDate(yyyyMMdd, yyyyMMdd_end);
        // for (int i = 0; i < listByDate.size(); i++) {
        //     AppWorkday workDay = listByDate.get(i);
        //     String day = workDay.getDay();
        //     LocalDate dateOfDay = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyyMMdd"));

        //     // 判断场馆是否是闭馆
        //     int status = workDay.getIsWorkday();
        //     System.out.println(workDay.getDay()+" status:"+status);
        //     if (status == WorkdayEnum.OPEN_DAY.getCode()) {
        //         workDay.setDayRemark("");
        //         //判断日期是否已经设置过规则，设置过规则的，按规则处理
        //         workDay.setIsWorkday(iAppWorkdayService.getDayStatus(workDay,exhibitionId));
        //         if(workDay.getIsWorkday()==WorkdayEnum.REST_DAY.getCode()){
        //             workDay.setDayRemark("闭馆");
        //         }
        //     } else {
        //         //判断是否是节假日或者周一
        //         Boolean availableByHoliday = iAppointmentService.isAvailableByHoliday(exhibitionId, dateOfDay);
        //         Boolean availableByHoliday2 = iAppointmentService.isAvailableByHoliday("all", dateOfDay);
        //         if (!availableByHoliday || !availableByHoliday2) {
        //             if (!StringUtils.hasLength(workDay.getDayRemark())) {
        //                 workDay.setDayRemark("闭馆");
        //             }
        //         } else { //如果不是节假日或者周一，所有的关闭状态，显示已约满
        //             if (!StringUtils.hasLength(workDay.getDayRemark())) {
        //                 workDay.setDayRemark("已约满");
        //             }
        //         }
        //     }
        // }
        Map<String, Object> result = new HashMap<>();
        result.put("pageShowDays", configsByGroup.get("appointment.default_show_days"));
        result.put("dayList", availableDates);
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "获取可预约日期范围(针对临展)")
    @RequestMapping(value = "/getAvailableTime/{exhibitionNo}/{category}", method = RequestMethod.GET)
    public CommonResult<?> getAvailableTime(@PathVariable String exhibitionNo, @PathVariable Number category) {
        log.info("/getAvailableTime/category/type:" + exhibitionNo);
        
        Map<String, Object> configsByGroup = configService
                .getConfigsByGroup("appointment.category.16." + exhibitionNo);
        
        List<AvailableDayResponse> availableDates = getAvailableDatesList((byte)category,exhibitionNo);
        // // 默认查询30天数据，然后再进行筛选
        // LocalDate date = LocalDate.now();
        // LocalDate endDate = date.plusDays(Integer.parseInt((String) configsByGroup.get("offset.day"))
        //         + Integer.parseInt((String) configsByGroup.get("appointment.days")));
        // date = date.plusDays(Integer.parseInt((String) configsByGroup.get("offset.day")));

        // String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // String yyyyMMdd_end = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // List<AppWorkday> listByDate = iAppWorkdayService.getListByDate(yyyyMMdd, yyyyMMdd_end);
        // for (int i = 0; i < listByDate.size(); i++) {
        //     AppWorkday workDay = listByDate.get(i);
        //     String day = workDay.getDay();
        //     workDay.setIsWorkday(1);
        //     LocalDate dateOfDay = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyyMMdd"));

        //     // 判断场馆是否是闭馆
        //     int status = workDay.getIsWorkday();
        //     System.out.println(workDay.getDay()+" status:"+status);
        //     if (status == WorkdayEnum.OPEN_DAY.getCode()) {
        //         workDay.setDayRemark("");
        //         //判断日期是否已经设置过规则，设置过规则的，按规则处理
        //         workDay.setIsWorkday(iAppWorkdayService.getDayStatus(workDay,exhibitionNo));
        //         if(workDay.getIsWorkday()==WorkdayEnum.REST_DAY.getCode()){
        //             workDay.setDayRemark("闭馆");
        //         }
        //     } else {
        //         //判断是否是节假日或者周一
        //         Boolean availableByHoliday = iAppointmentService.isAvailableByHoliday(exhibitionNo, dateOfDay);
        //         if (!availableByHoliday ) {
        //             if (!StringUtils.hasLength(workDay.getDayRemark())) {
        //                 workDay.setDayRemark("闭馆");
        //             }
        //         } else { //如果不是配置中的假日，所有的关闭状态，显示已约满
        //             if (!StringUtils.hasLength(workDay.getDayRemark())) {
        //                 workDay.setDayRemark("已约满");
        //             }
        //         }
        //     }
        // }

        Map<String, Object> result = new HashMap<>();
        result.put("pageShowDays", configsByGroup.get("appointment.default_show_days"));
        result.put("dayList", availableDates);
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "获取选择日期预约情况", description = "category：1门票,2团队,4展项；date格式：yyyyMMdd")
    @RequestMapping(value = "/getDetailByDate/{category}/{date}", method = RequestMethod.GET)
    public CommonResult<List<AppBatch>> getDetailByDate(@PathVariable Byte category, @PathVariable String date) {
        List<AppBatch> listByDate = appBatchService.getListByDate(category, date);

        // Optional<AppWorkday> workdayOpt = appWorkdayService.getByDate(date);
        // AppWorkday workday = workdayOpt.orElse(null);

        String exhibitionId = "team";
        if (category.intValue() == BatchCategoryEnum.TICKET.getCode().intValue()) {
            exhibitionId = "ticket";
        }
        if (category.intValue() == BatchCategoryEnum.ITEM_FYPD.getCode().intValue()) {
            exhibitionId = "fypd";
        }
        RoundStatusEnum status = appointmentService.getRoundStatus(exhibitionId, LocalDate.parse(date, DateTimeFormatter.BASIC_ISO_DATE));
        // if (status == null ) {
        //     continue;
        // }
        // if(status == RoundStatusEnum.open) {
        //     availableDay.setIsWorkday(1);
        //     availableDay.setDayRemark("");
        // } else {
        //     availableDay.setIsWorkday(0);
        //     availableDay.setDayRemark("已约满");
        // }
        // int status = iAppWorkdayService.getDayStatus(workday, exhibitionId);
        listByDate.stream()
                .sorted(Comparator.comparing(AppBatch::getBatchStartTime))
                .forEach(batch -> {
                    if (status == RoundStatusEnum.close) {
                        batch.setBatchStatus((byte) 0);
                    }
                });
        return CommonResult.succeeded(listByDate);
    }

    @Operation(summary = "获取选择日期预约情况", description = "category：16临展；date格式：yyyyMMdd")
    @RequestMapping(value = "/getDetailByDate/{category}/{exhibitionNo}/{dateStr}", method = RequestMethod.GET)
    public CommonResult<List<AppBatch>> getDetailByDate(@PathVariable Byte category, @PathVariable String exhibitionNo,
            @PathVariable String dateStr) {

        // if (exhibitionNo.equals("fypd") && "20250524".equals(dateStr) || "20250525".equals(dateStr)) {
        //     return CommonResult.succeeded(getAvailableTimeSpanOf2AnniversaryOfFypd(exhibitionNo, category, dateStr));
        // }
        List<AppBatch> listByDate = appBatchService.getListByDate(exhibitionNo, category, dateStr);
        RoundStatusEnum status = appointmentService.getRoundStatus(exhibitionNo, LocalDate.parse(dateStr,DateTimeFormatter.BASIC_ISO_DATE));

        // AppWorkday workday = new AppWorkday();
        // workday.setDay(dateStr);
        // workday.setIsWorkday(1);
        // int status = iAppWorkdayService.getDayStatus(workday, exhibitionNo);
        listByDate.stream()
                .sorted(Comparator.comparing(AppBatch::getBatchStartTime))
                .forEach(batch -> {
                    if (status == RoundStatusEnum.close) {
                        batch.setBatchStatus((byte) 0);
                    }
                });
        return CommonResult.succeeded(listByDate);
    }

    private List<AvailableDayResponse> getAvailableDatesList(Byte category, String exhibitionId) {
        List<AvailableDayResponse> availableDates = AppointHelper.getAvailableDays(category);
        for(int i=0; i<availableDates.size(); i++) {
            AvailableDayResponse availableDay = availableDates.get(i);
            RoundStatusEnum status = appointmentService.getRoundStatus(exhibitionId, LocalDate.parse(availableDay.getDay(), DateTimeFormatter.BASIC_ISO_DATE));
            if (status == null ) {
                continue;
            }
            if(status == RoundStatusEnum.open) {
                availableDay.setIsWorkday(1);
                availableDay.setDayRemark("");
            } else {
                availableDay.setIsWorkday(0);
                availableDay.setDayRemark("已约满");
            }
        }
        return availableDates;
    }

    // private List<AppBatch> getAvailableTimeSpanOf2AnniversaryOfFypd(String exhibitionNo, Byte category,
    //         String dateStr) {
    //     List<AppBatch> listByDate = appBatchService.getListByDate(exhibitionNo, category, dateStr);
    //     // LocalTime startDayTime = LocalTime.of(9, 30, 00);
    //     // LocalTime endDayTime = LocalTime.of(15, 30, 00);
    //     LocalTime midDayTime = LocalTime.of( 12, 30, 10);
    //     //  LocalDateTime dateTime = LocalDateTime.now();
    //     //LocalTime dateTime = LocalTime.of(14,35,00);
    //     LocalTime dateTime = LocalTime.now();

    //     // if not in the available time span, return null
    //     /*if (dateTime.isBefore(startDayTime) || dateTime.isAfter(endDayTime)) {
    //         return Collections.emptyList();
    //     }*/
    //     if (dateTime.isBefore(midDayTime)) {
    //         // before mid dayu
    //         return listByDate.stream().filter(appbatch -> {
    //             String timeStr = appbatch.getBatchStartTime();
    //             return (timeStr.startsWith("10") || timeStr.startsWith("11") || timeStr.startsWith("12"));
    //         }).collect(Collectors.toList());
    //     }

    //     if (dateTime.isAfter(midDayTime)) {
    //         return listByDate.stream().filter(appbatch -> {
    //             String timeStr = appbatch.getBatchStartTime();
    //             return (timeStr.startsWith("13") || timeStr.startsWith("14") || timeStr.startsWith("15"));
    //         }).collect(Collectors.toList());
    //     }
    //     return Collections.emptyList();
    // }

    @RequestMapping(value = "/initTicket/{startDate}_{endDate}")
    public CommonResult<?> initTicket(@PathVariable String startDate, @PathVariable String endDate) {
        LocalDate startDateForLocal = LocalDate.parse(startDate, DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate endDateForLocal = LocalDate.parse(endDate, DateTimeFormatter.BASIC_ISO_DATE);
        return CommonResult.succeeded(appBatchService.initTicket(startDateForLocal, endDateForLocal));
    }

    @RequestMapping(value = "/initTeam/{startDate}_{endDate}")
    public CommonResult<?> initTeam(@PathVariable String startDate, @PathVariable String endDate) {
        LocalDate startDateForLocal = DateTimeUtil.parseBasicIsoDate(startDate);
        LocalDate endDateForLocal = LocalDate.parse(endDate, DateTimeFormatter.BASIC_ISO_DATE);
        return CommonResult.succeeded(appBatchService.initTeam(startDateForLocal, endDateForLocal));
    }

    @RequestMapping(value = "/initItem/{startDate}_{endDate}")
    public CommonResult<?> initItem(@PathVariable String startDate, @PathVariable String endDate) {
        LocalDate startDateForLocal = LocalDate.parse(startDate, DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate endDateForLocal = LocalDate.parse(endDate, DateTimeFormatter.BASIC_ISO_DATE);
        return CommonResult.succeeded(appBatchService.initItem(startDateForLocal, endDateForLocal));
    }

    @RequestMapping(value = "/config")
    public CommonResult<?> config() {
        return CommonResult.succeeded(configService.getAll());
    }
    @RequestMapping(value = "/config/set/{id}/{value}")
    public CommonResult<?> updateConfig(@PathVariable Long id,
                                        @PathVariable String value) {
        Optional<AppConfig> configOpt = configService.findById(id);
        if(!configOpt.isPresent()){
            return CommonResult.failed("未找到");
        }
        AppConfig config = configOpt.get();
        config.setRuleValue(value);
        return CommonResult.succeeded(configService.save(config));
    }
}
