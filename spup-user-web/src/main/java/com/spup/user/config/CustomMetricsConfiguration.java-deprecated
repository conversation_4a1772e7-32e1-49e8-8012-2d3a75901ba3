package com.spup.user.config;

import org.springframework.context.annotation.Bean;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

/**
 * Custom metrics configuration to replace the missing MetricsAutoConfiguration
 */
// @Configuration
public class CustomMetricsConfiguration {

    /**
     * Provides a simple meter registry for metrics
     * This is a minimal implementation to avoid the dependency on Spring Boot Actuator
     */
    @Bean
    public MeterRegistry meterRegistry() {
        return new SimpleMeterRegistry();
    }
}
