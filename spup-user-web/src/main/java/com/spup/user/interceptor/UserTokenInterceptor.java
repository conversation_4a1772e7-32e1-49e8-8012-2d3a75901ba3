package com.spup.user.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;

import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.spup.commons.api.CommonResult;
import com.spup.commons.api.ResultCodeEnum;
import com.spup.commons.utils.JWTUtil;
import com.spup.core.interceptor.BaseTokenInterceptor;
import com.spup.core.service.IAppOperateLogService;

import lombok.extern.slf4j.Slf4j;

/**
 * User Token Interceptor
 * Extends BaseTokenInterceptor with user-specific validation logic
 */
@Slf4j
@Component
public class UserTokenInterceptor extends BaseTokenInterceptor {

    // JWTUtil is now a static utility class - no injection needed
    
    @Autowired
    private IAppOperateLogService iAppOperateLogService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Provide ObjectMapper for base class
     */
    @Override
    protected ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    /**
     * User-specific development mode detection
     */
    @Override
    protected boolean isDevelopmentMode(HttpServletRequest request) {
        return request.getServerName().contains("localhost");
    }

    /**
     * Set default user session for development
     */
    @Override
    protected void setDefaultDevelopmentSession(HttpServletRequest request) {
        // Default user unionid for development
        // request.getSession().setAttribute("unionid", "ojqzL0-hOlek3HMyLjhvKjTfDnnA");
        request.getSession().setAttribute("unionid", "ojqzL0y-kslTWHDe7XGSKgo88Tc8");
        
        log.info("Set default user development session");
    }

    /**
     * User-specific token validation
     */
    @Override
    protected boolean validateTokenAndSetSession(String token, 
                                                HttpServletRequest request, 
                                                HttpServletResponse response) throws Exception {
        if (ifTestServer()) {
            setDefaultDevelopmentSession(request);
            return true;
        }
        
        try {
            // Decode JWT token
            DecodedJWT decodedJWT = JWTUtil.decodeToken(token);

            // Extract claims
            Claim unionidClaim = decodedJWT.getClaim("unionid");
            Claim openidClaim = decodedJWT.getClaim("openid");

            if (unionidClaim == null || unionidClaim.asString() == null) {
                log.warn("Token missing unionid claim");
                writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.JWT_TOKEN_FAILED));
                return false;
            }

            String unionid = unionidClaim.asString();
            String openid = openidClaim != null ? openidClaim.asString() : null;

            log.debug("User token validation for unionid: {}", unionid);

            // Update session if needed
            String sessionUnionid = (String) request.getSession().getAttribute("unionid");
            if (!unionid.equals(sessionUnionid)) {
                request.getSession().setAttribute("unionid", unionid);
                if (openid != null) {
                    request.getSession().setAttribute("openid", openid);
                }
            }

            // Set additional user session attributes
            setAdditionalSessionAttributes(decodedJWT, request);

            return true;

        } catch (Exception e) {
            log.error("User token validation failed:", e);
            writeErrorResponse(response, getUserErrorResult(e));
            return false;
        }
    }

    @org.springframework.beans.factory.annotation.Value("${spring.profiles.active:}")
    private String activeProfile;

    protected Boolean ifTestServer() {
        log.info("activeProfile: {}", activeProfile);
        return "online".equals(activeProfile) || "dev".equals(activeProfile);
    }

    /**
     * Set additional user-specific session attributes
     */
    @Override
    protected void setAdditionalSessionAttributes(DecodedJWT decodedJWT, HttpServletRequest request) {
        // Add user-specific session attributes if needed
        Claim openidClaim = decodedJWT.getClaim("openid");
        if (openidClaim != null && openidClaim.asString() != null) {
            request.getSession().setAttribute("openid", openidClaim.asString());
        }
    }

    /**
     * Post-handle processing for user requests
     */
    @Override
    public void postHandle(@NonNull HttpServletRequest request, 
                          @NonNull HttpServletResponse response,
                          @NonNull Object handler,
                          @Nullable ModelAndView modelAndView) {
        // User-specific post-processing if needed
    }

    /**
     * After completion processing for user requests
     */
    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, 
                               @NonNull HttpServletResponse response,
                               @NonNull Object handler,
                               @Nullable Exception ex) {
        try {
            log.info("User Token Interceptor, after completion");
            // Save operation log for user requests
            iAppOperateLogService.saveLog(request);
        } catch (Exception e) {
            log.error("Error saving operation log:", e);
        }
    }

    /**
     * Get user-specific error result based on exception type
     */
    private CommonResult<?> getUserErrorResult(Exception e) {
        if (e instanceof com.auth0.jwt.exceptions.SignatureVerificationException) {
            log.warn("Invalid JWT signature");
            return CommonResult.failed(ResultCodeEnum.GET_TOKEN_KEY_FAILED);
        } else if (e instanceof com.auth0.jwt.exceptions.TokenExpiredException) {
            log.warn("JWT token expired");
            return CommonResult.failed(ResultCodeEnum.AUTHORIZED_FAILED);
        } else if (e instanceof com.auth0.jwt.exceptions.AlgorithmMismatchException) {
            log.warn("JWT algorithm mismatch");
            return CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        } else {
            log.warn("JWT token validation failed: {}", e.getMessage());
            return CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        }
    }
}
