package com.spup.user.task;


import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.spup.data.entity.RoundConfig.RoundStatusEnum;
import com.spup.data.entity.appointment.AppBatch;
// import com.spup.data.entity.appointment.AppWorkday;
import com.spup.enums.BatchStatusEnum;
import com.spup.enums.OrderCategoryEnum;
import com.spup.user.controller.AppBatchController;
import com.spup.user.service.IAppConfigService;
// import com.spup.user.service.IAppWorkdayService;
import com.spup.user.service.IAppointmentService;
import com.spup.user.service.appointment.IAppBatchService;


@Configuration
@EnableScheduling
public class OpenBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Resource
    private IAppBatchService iAppBatchService;
    // @Resource
    // private IAppWorkdayService iAppWorkdayService;
    @Resource
    private IAppConfigService iAppConfigService;
    @Resource
    private IAppointmentService appointmentService;

    //3.添加定时任务
    @Scheduled(cron = "0 0 9 * * ?")
    protected void configureTasks() {
        logger.info("执行定时开启门票场次");

        byte category = OrderCategoryEnum.TICKET.getCode();

        Map<String, Object> configsByGroup = iAppConfigService.getConfigsByGroup("appointment.category." + category);

        int offsetDay = Integer.parseInt((String) configsByGroup.get("offset.day"));
        int showDays = Integer.parseInt((String) configsByGroup.get("appointment.default_show_days"));
        // int appointmentDays = Integer.parseInt((String) configsByGroup.get("appointment.days"));
        // int validDays = Integer.parseInt((String) configsByGroup.get("appointment.valid_days"));

        // Calendar now = Calendar.getInstance();
        // now.add(Calendar.DAY_OF_MONTH, offsetDay);
        // Calendar end = Calendar.getInstance();
        // end.add(Calendar.DAY_OF_MONTH, offsetDay);
        // end.add(Calendar.DAY_OF_MONTH, showDays-1);
        LocalDate startDate = LocalDate.now().plusDays(offsetDay);
        LocalDate endDate = startDate.plusDays(showDays);

        while(startDate.isBefore(endDate)) {
            String yyyyMMdd = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            RoundStatusEnum status = appointmentService.getRoundStatus("ticket", startDate);
            if(status == RoundStatusEnum.close) {
                startDate = startDate.plusDays(1);
                continue;
            }
            Map<String, List<AppBatch>> listByDate = iAppBatchService.getListByDate(category, yyyyMMdd, yyyyMMdd);
            List<AppBatch> batchs = listByDate.get(yyyyMMdd);
            for (int i = 0; i < batchs.size() ; i++) {
                AppBatch batch = batchs.get(i);
                if(batch.getBatchStatus().byteValue() == BatchStatusEnum.CLOSED.getCode()) {
                    batch.setBatchStatus(BatchStatusEnum.RUNNING.getCode());
                    iAppBatchService.update(batch);
                }
            }
            startDate = startDate.plusDays( 1);
        }

        // int validDaysIndex = 0;
        // while (!now.after(end)){
        //     Instant instant = now.getTime().toInstant();
        //     ZoneId zoneId = ZoneId.systemDefault();

        //     LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();

        //     String yyyyMMdd = localDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        //     // boolean isWorkDay = iAppWorkdayService.isWorkDay(yyyyMMdd);

        //     if(isWorkDay){ //后续根据假期情况来判断
        //         // Optional<AppWorkday> workDayOpt = iAppWorkdayService.getByDate(yyyyMMdd);
        //         // int ticketStatus = iAppWorkdayService.getDayStatus(workDayOpt.orElse(null), "ticket");
        //         if(ticketStatus == 1){
        //             if(validDaysIndex>=validDays){
        //                 break;
        //             }
        //             validDaysIndex++;
        //             Map<String, List<AppBatch>> listByDate = iAppBatchService.getListByDate(category, yyyyMMdd, yyyyMMdd);
        //             List<AppBatch> batchs = listByDate.get(yyyyMMdd);
        //             for (int i = 0; i < batchs.size() ; i++) {
        //                 AppBatch batch = batchs.get(i);
        //                 if(batch.getBatchStatus().byteValue() == BatchStatusEnum.CLOSED.getCode()) {
        //                     batch.setBatchStatus(BatchStatusEnum.RUNNING.getCode());
        //                     iAppBatchService.update(batch);
        //                 }
        //             }
        //         }

        //     }
        //     now.add(Calendar.DAY_OF_MONTH, 1);
    }
}
