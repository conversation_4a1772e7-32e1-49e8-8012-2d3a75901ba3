package com.spup.user.task;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.spup.core.service.IAppAppointmentOrderService;
import com.spup.data.dao.appointment.AppAppointmentOrderDao;
import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.enums.OrderStatusEnum;
import com.spup.user.controller.AppBatchController;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class CloseOrderTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Resource
    private IAppAppointmentOrderService iAppAppointmentOrderService;
    @Resource
    private AppAppointmentOrderDao appAppointmentOrderDao;
    //3.添加定时任务
    @Scheduled(cron = "0 3 13,16 * * ?")
    protected void configureTasks() {
        logger.info("执行定时清理未核销的订单");
        LocalDateTime now = LocalDateTime.now();
        String today = now.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        String hhmm = now.toLocalTime().format(DateTimeFormatter.ofPattern("HHmm"));
        List<AppAppointmentOrder> noCheckoutOrders = getNoCheckoutOrders(today);
        for (int i = 0; i < noCheckoutOrders.size(); i++) {
            AppAppointmentOrder order = noCheckoutOrders.get(i);
            if(order.getBatchEndTime().compareTo(hhmm)<0) {
                iAppAppointmentOrderService.breaked(order.getOrderNo(), "CloseOrderTask");
            }
        }
    }

    private List<AppAppointmentOrder> getNoCheckoutOrders(String yyyymmdd){
        List<AppAppointmentOrder> byBatchDateBetween = appAppointmentOrderDao.findByBatchDateBetween(yyyymmdd, yyyymmdd);
        byBatchDateBetween =
                byBatchDateBetween.stream()
                        .filter(order -> order.getOrderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode())
                        .collect(Collectors.toList());

        return byBatchDateBetween;
    }
}