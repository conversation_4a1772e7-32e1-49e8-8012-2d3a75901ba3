package com.spup.user.task;


import com.spup.user.controller.AppBatchController;
import com.spup.core.service.IAppAppointmentTeamOrderService;
import com.spup.data.dao.appointment.AppAppointmentTeamOrderDao;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;
import com.spup.enums.OrderCategoryEnum;
import com.spup.enums.OrderStatusEnum;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class CloseTeamOrderTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Resource
    private IAppAppointmentTeamOrderService iAppAppointmentTeamOrderService;
    @Resource
    private AppAppointmentTeamOrderDao appAppointmentTeamOrderDao;
    //3.添加定时任务
    @Scheduled(cron = "0 0 17 * * ?")
    protected void configureTasks() {
        logger.info("执行定时清理未核销的团队订单");
        LocalDateTime now = LocalDateTime.now();
        String today = now.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        String hhmm = now.toLocalTime().format(DateTimeFormatter.ofPattern("HHmm"));
        List<AppAppointmentTeamOrder> noCheckoutOrders = getNoCheckoutOrders(today,OrderCategoryEnum.TEAM.getCode());
        noCheckoutOrders.addAll(getNoCheckoutOrders(today, OrderCategoryEnum.EXHIBITION_TEAM.getCode()));

        for (int i = 0; i < noCheckoutOrders.size(); i++) {
            AppAppointmentTeamOrder order = noCheckoutOrders.get(i);
            if(order.getBatchEndTime().compareTo(hhmm)<0) {
                iAppAppointmentTeamOrderService.breaked(order.getOrderNo(), "CloseTeamOrderTask");
            }
        }
    }

    private List<AppAppointmentTeamOrder> getNoCheckoutOrders(String yyyymmdd, Byte category){
        List<AppAppointmentTeamOrder> byBatchDateBetween = appAppointmentTeamOrderDao.findByBatchDateBetween(yyyymmdd, yyyymmdd);
        byBatchDateBetween =
                byBatchDateBetween.stream()
                        .filter(order -> order.getOrderCategory().byteValue() == category)
                        .filter(order -> order.getOrderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode())
                        .collect(Collectors.toList());

        return byBatchDateBetween;
    }
}