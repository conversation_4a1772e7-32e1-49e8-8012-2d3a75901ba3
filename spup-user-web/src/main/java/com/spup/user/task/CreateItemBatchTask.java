package com.spup.user.task;

import java.time.LocalDate;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.spup.core.service.IAppBatchService;
import com.spup.user.controller.AppBatchController;
import com.spup.user.service.UserCounterService;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class CreateItemBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Autowired
    private IAppBatchService iAppBatchService;
    @Resource
    UserCounterService userCounterService;
    //3.添加定时任务,这里的定时时间一定要在Release Item4SeatTask之前
    @Scheduled(cron = "0 5 0 * * ?")
    public void configureTasks() {
        logger.info("执行创建展项场次任务");
        LocalDate date = LocalDate.now().plusDays(1);
        iAppBatchService.initItem(date, date);
        userCounterService.initMap();
        userCounterService.loadHasBook();
    }
}
