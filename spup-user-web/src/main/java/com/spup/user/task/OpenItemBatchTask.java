package com.spup.user.task;


import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.spup.core.service.IAppBatchService;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.enums.BatchCategoryEnum;
import com.spup.user.controller.AppBatchController;


@Configuration
@EnableScheduling
public class OpenItemBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(AppBatchController.class);

    @Autowired
    private IAppBatchService iAppBatchService;
    //3.添加定时任务
    private void run() {
        logger.info("执行定时开启展项场次");
        LocalDateTime now = LocalDateTime.now();
        LocalTime nowTime = now.toLocalTime();
        LocalTime midDayTime = LocalTime.of( 12, 59, 59);
        //这里的end时间范围，需要跟上面的定时任务相匹配
        String start = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        String hhmm = midDayTime.format(DateTimeFormatter.ofPattern("HHmm"));

        //飞阅浦东固定场次
        Map<String, List<AppBatch>> listByDateForFYPD = iAppBatchService.getListByDate(BatchCategoryEnum.ITEM_FYPD.getCode(), start, start);
        List<AppBatch> batchsForFYPD = listByDateForFYPD.get(start);
        //飞阅浦东临时场次
        Map<String, List<AppBatch>> listByDateForTempFYPD = iAppBatchService.getListByDate(BatchCategoryEnum.TEMP_ITEM_FYPD.getCode(), start, start);
        List<AppBatch> tempFYPDBatchList = listByDateForTempFYPD.get(start);
        if(tempFYPDBatchList!=null) {
            batchsForFYPD.addAll(tempFYPDBatchList);
        }
        for (int i = 0; i < batchsForFYPD.size() ; i++) {
            AppBatch batch = batchsForFYPD.get(i);
            if(nowTime.isBefore(midDayTime) && batch.getBatchStartTime().compareTo(hhmm)<0 //上午
                || nowTime.isAfter(midDayTime) && batch.getBatchStartTime().compareTo(hhmm)>=0){ //下午
                batch.setBatchStatus((byte)1);
                iAppBatchService.update(batch);
            }

        }

    }

    @Scheduled(cron = "0 30 9 * * ?")
    public void executeAt930() {
        System.out.println("execute 9:30 task: " + System.currentTimeMillis());
        // 你的业务逻辑
        run();
    }
    // 每天 13:00 执行
    @Scheduled(cron = "0 0 13 * * ?") // 稍微延后一秒，以保证nowTime.isAfter(midDayTime)生效
    public void executeAt1300() {
        System.out.println("execute 13:00 task: " + System.currentTimeMillis());
        //你的业务逻辑
        run();
    }
}