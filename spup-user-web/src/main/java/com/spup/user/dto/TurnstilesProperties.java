package com.spup.user.dto;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

@Configuration
@ConfigurationProperties(prefix = "turnstiles")
@Getter
@Setter
public class TurnstilesProperties {
    
    private List<Supplier> suppliers;
    
    @Getter
    @Setter
    public static class Supplier {
        private String name;
        private String signKey;
    }
}