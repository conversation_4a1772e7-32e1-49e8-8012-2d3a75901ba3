package com.spup.user.javaConfig;

import com.spup.core.config.BaseWebConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

/**
 * User Module Web Configuration
 * Extends BaseWebConfig with user-specific settings
 */
@Configuration
public class WebConfig extends BaseWebConfig {
    
    @Override
    protected void addModuleSpecificResourceHandlers(ResourceHandlerRegistry registry) {
        // Add user-specific resource handlers here if needed
        // Example: registry.addResourceHandler("/user-assets/**").addResourceLocations("classpath:/user-assets/");
    }
}
