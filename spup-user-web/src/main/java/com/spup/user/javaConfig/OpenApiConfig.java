package com.spup.user.javaConfig;

import com.spup.core.config.BaseOpenApiConfig;
import org.springframework.context.annotation.Configuration;

/**
 * User Module OpenAPI Configuration
 * Extends BaseOpenApiConfig with user-specific settings
 */
@Configuration
public class OpenApiConfig extends BaseOpenApiConfig {
    
    @Override
    protected String getApiTitle() {
        return "浦东规划馆用户端API";
    }
    
    @Override
    protected String getApiDescription() {
        return "用户端接口文档详情信息 - 基于SpringDoc OpenAPI 3";
    }
}
