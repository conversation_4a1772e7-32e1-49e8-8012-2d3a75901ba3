package com.spup.user.service.appointment.impl;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.spup.commons.api.CommonResult;
import com.spup.commons.utils.NumberGenerator;
import com.spup.data.dao.appointment.AppAppointmentOrderTemporaryExhibitionDao;
import com.spup.data.dao.appointment.AppAppointmentSuborderTemporaryExhibitionDao;
import com.spup.data.entity.appointment.AppAppointmentOrderTemporaryExhibition;
import com.spup.data.entity.appointment.AppAppointmentSuborderTemporaryExhibition;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.core.dto.AppAppointmentOrderTemporaryExhibitionRequest;
import com.spup.core.dto.AppAppointmentOrderTemporaryExhibitionResponse;
import com.spup.user.dto.OrderRequest;
import com.spup.enums.BatchStatusEnum;
import com.spup.enums.OrderCategoryEnum;
import com.spup.enums.OrderStatusEnum;
import com.spup.user.service.appointment.IAppAppointmentOrderTemporaryExhibitionService;
import com.spup.user.service.appointment.IAppBatchService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AppAppointmentOrderTemporaryExhibitionServiceImpl implements IAppAppointmentOrderTemporaryExhibitionService {
    @Resource
    private IAppBatchService iAppBatchService;
    @Resource
    private AppAppointmentOrderTemporaryExhibitionDao appAppointmentOrderTemporaryExhibitionDao;
    @Resource
    private AppAppointmentSuborderTemporaryExhibitionDao appAppointmentSuborderTemporaryExhibitionDao;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public CommonResult<?> save(AppAppointmentOrderTemporaryExhibitionRequest orderRequest,String unionid) throws JsonProcessingException, JsonMappingException {
        String batchNo = orderRequest.getBatchNo();
        String contacts = orderRequest.getContacts();
        //数据校验
        //场次校验
        if(batchNo == null || batchNo.isEmpty()){
            return CommonResult.failed("场次为空");
        }
        AppBatch batch = iAppBatchService.getByNo(batchNo, OrderCategoryEnum.EXHIBITION.getCode());
        if(batch==null){
            return CommonResult.failed("场次不正确");
        }
        if(batch.getBatchStatus() == BatchStatusEnum.CLOSED.getCode()){
            return CommonResult.failed("场次已关闭");
        }

        //预约人格式校验
        ArrayNode personArray = (ArrayNode)objectMapper.readTree(contacts);
        int personNum = personArray.size();
        if(personNum==0){
            return CommonResult.failed("预约人为空");
        } else if(personNum>5){
            return CommonResult.failed("预约人最多预约5人");
        }
        //核实预约人人数与剩余票数的关系
        if(batch.getTicketRemaining()<personNum){
            return CommonResult.failed("名额不足");
        }
        //校验已预约人
        String batchDate = batch.getBatchDate();
        List<AppAppointmentSuborderTemporaryExhibition> subordersByUnionid = appAppointmentSuborderTemporaryExhibitionDao.findByOnwerUnionid(unionid);
        List<AppAppointmentSuborderTemporaryExhibition> todaySuborders = subordersByUnionid.stream().filter((suborder) -> {
            return suborder.getBatchDate().equals(batchDate);
        }).collect(Collectors.toList());;
        if(personNum+todaySuborders.size()>5){
            return CommonResult.failed("您已预约了"+todaySuborders.size()+"人, 每日最多可预约5人");
        }

        /*
        *
        * 数据处理
        */

        DecimalFormat df = new DecimalFormat("00");
        String orderNo = NumberGenerator.getOrderNo();
        AppAppointmentOrderTemporaryExhibition order = new AppAppointmentOrderTemporaryExhibition();
        order.setOrderNo(orderNo);
        order.setExhibitionNo(orderRequest.getExhibitionNo());
        order.setBatchNo(batch.getBatchNo());
        order.setBatchDate(batch.getBatchDate());
        order.setBatchStartTime(batch.getBatchStartTime());
        order.setBatchEndTime(batch.getBatchEndTime());
        order.setOwnerUnionid(unionid);
        order.setOrderStatus(OrderStatusEnum.SUCCESS.getCode());

        appAppointmentOrderTemporaryExhibitionDao.save(order);
        for(int i=0;i<personArray.size();i++){
            JsonNode jsonNode = personArray.get(i);
            AppAppointmentSuborderTemporaryExhibition suborder = new AppAppointmentSuborderTemporaryExhibition();

            suborder.setOrderNo(order.getOrderNo());
            suborder.setSuborderNo(order.getOrderNo()+df.format((i+1)));

            suborder.setBatchNo(order.getBatchNo());
            suborder.setBatchDate(order.getBatchDate());
            suborder.setBatchStartTime(order.getBatchStartTime());
            suborder.setBatchEndTime(order.getBatchEndTime());

            suborder.setOnwerUnionid(order.getOwnerUnionid());
            suborder.setContactsName(jsonNode.get("name").asText());
            suborder.setContactsPhone(jsonNode.get("phone").asText());
            suborder.setContactsIdcardCategory(jsonNode.get("idcardCategory").numberValue().byteValue());
            suborder.setContactsIdcardNo(jsonNode.get("idcardNo").asText(""));

            suborder.setSuborderStatus(OrderStatusEnum.SUCCESS.getCode());

            appAppointmentSuborderTemporaryExhibitionDao.save(suborder);
        }
        iAppBatchService.updateRemaining(batchNo,OrderCategoryEnum.EXHIBITION.getCode(),-1*personNum);
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> getList(String unionid) {

        //appAppointmentOrderExample.setOrderByClause(" create_time desc ");
        List<AppAppointmentOrderTemporaryExhibition> appAppointmentOrders = appAppointmentOrderTemporaryExhibitionDao.findByOwnerUnionid(unionid);
        List<AppAppointmentOrderTemporaryExhibitionResponse> orderResponses = new ArrayList<>();
        for (AppAppointmentOrderTemporaryExhibition order: appAppointmentOrders) {
            AppAppointmentOrderTemporaryExhibitionResponse orderResponse = new AppAppointmentOrderTemporaryExhibitionResponse();

            BeanUtils.copyProperties(order,orderResponse);

            List<AppAppointmentSuborderTemporaryExhibition> appAppointmentSuborders = appAppointmentSuborderTemporaryExhibitionDao.findByOrderNo(orderResponse.getOrderNo());

            orderResponse.setSuborders(appAppointmentSuborders);
            orderResponses.add(orderResponse);
        }

        return CommonResult.succeeded(orderResponses);
    }

    @Override
    public CommonResult<?> cancel(String orderNo,String unionid) {
        /*AppAppointmentOrderTemporaryExhibitionExample appAppointmentOrderExample = new AppAppointmentOrderTemporaryExhibitionExample();
        appAppointmentOrderExample.createCriteria()
                .andOrderNoEqualTo(orderNo)
                .andOrderStatusEqualTo(OrderStatusEnum.SUCCESS.getCode());

        AppAppointmentOrderTemporaryExhibition order = new AppAppointmentOrderTemporaryExhibition();
        order.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
        order.setUpdateBy(unionid);

        int update = appAppointmentOrderTemporaryExhibitionDao.updateByExampleSelective(order,appAppointmentOrderExample);
        if(update>0) {
            AppAppointmentSuborderTemporaryExhibitionExample suborderExample = new AppAppointmentSuborderTemporaryExhibitionExample();
            suborderExample.createCriteria()
                    .andOrderNoEqualTo(orderNo)
                    .andSuborderStatusEqualTo(OrderStatusEnum.SUCCESS.getCode());

            AppAppointmentSuborderTemporaryExhibition suborder = new AppAppointmentSuborderTemporaryExhibition();
            suborder.setSuborderStatus(OrderStatusEnum.CANCELED.getCode());
            suborder.setUpdateBy(unionid);

            update = appAppointmentSuborderTemporaryExhibitionDao.updateByExampleSelective(suborder, suborderExample);

            AppAppointmentOrderTemporaryExhibition appAppointmentOrder = getOrderByOrderNo(orderNo);

            iAppBatchService.updateRemaining(appAppointmentOrder.getBatchNo(), OrderCategoryEnum.EXHIBITION.getCode(), update);
        }*/
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> breaked(String orderNo,String unionid) {
        /*AppAppointmentOrderTemporaryExhibitionExample appAppointmentOrderExample = new AppAppointmentOrderTemporaryExhibitionExample();
        appAppointmentOrderExample.createCriteria()
                .andOrderNoEqualTo(orderNo)
                .andOrderStatusEqualTo(OrderStatusEnum.SUCCESS.getCode());

        AppAppointmentOrderTemporaryExhibition order = new AppAppointmentOrderTemporaryExhibition();
        order.setOrderStatus(OrderStatusEnum.BREAKED.getCode());
        order.setUpdateBy(unionid);

        int update = appAppointmentOrderTemporaryExhibitionDao.updateByExampleSelective(order,appAppointmentOrderExample);
        if(update>0) {
            AppAppointmentSuborderTemporaryExhibitionExample suborderExample = new AppAppointmentSuborderTemporaryExhibitionExample();
            suborderExample.createCriteria()
                    .andOrderNoEqualTo(orderNo)
                    .andSuborderStatusEqualTo(OrderStatusEnum.SUCCESS.getCode());

            AppAppointmentSuborderTemporaryExhibition suborder = new AppAppointmentSuborderTemporaryExhibition();
            suborder.setSuborderStatus(OrderStatusEnum.BREAKED.getCode());
            suborder.setUpdateBy(unionid);

            update = appAppointmentSuborderTemporaryExhibitionDao.updateByExampleSelective(suborder, suborderExample);

        }*/
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> delete(String orderNo) {
        /*AppAppointmentOrderTemporaryExhibitionExample appAppointmentOrderExample = new AppAppointmentOrderTemporaryExhibitionExample();
        appAppointmentOrderExample.createCriteria()
                .andOrderNoEqualTo(orderNo);

        AppAppointmentOrderTemporaryExhibition order = new AppAppointmentOrderTemporaryExhibition();
        order.setDeleted((byte)1);

        appAppointmentOrderTemporaryExhibitionDao.updateByExampleSelective(order,appAppointmentOrderExample);

        AppAppointmentSuborderTemporaryExhibitionExample suborderExample = new AppAppointmentSuborderTemporaryExhibitionExample();
        suborderExample.createCriteria()
                .andOrderNoEqualTo(orderNo);

        AppAppointmentSuborderTemporaryExhibition suborder = new AppAppointmentSuborderTemporaryExhibition();
        suborder.setDeleted((byte)1);

        appAppointmentSuborderTemporaryExhibitionDao.updateByExampleSelective(suborder,suborderExample);
*/
        return CommonResult.succeeded("");
    }



    @Override
    public List<AppAppointmentOrderTemporaryExhibition> getListByUnionid(String unionid) {
        return null;
    }

    @Override
    public AppAppointmentOrderTemporaryExhibition getOrderByOrderNo(String orderNo) {
        Optional<AppAppointmentOrderTemporaryExhibition> exhibitionOptional =  appAppointmentOrderTemporaryExhibitionDao.getByOrderNo(orderNo);
        if(!exhibitionOptional.isPresent()){
            return null;
        }
        return exhibitionOptional.get();
    }


    @Override
    public CommonResult<?> checkout(OrderRequest orderRequest, String unionid) {
        String subOrderId = orderRequest.getSubOrderId();
        Optional<AppAppointmentSuborderTemporaryExhibition> exhibitionOptional = appAppointmentSuborderTemporaryExhibitionDao.getBySuborderNo(subOrderId);
        if(!exhibitionOptional.isPresent()){
            return CommonResult.failed("未找到该展项");
        }
        AppAppointmentSuborderTemporaryExhibition exhibition = exhibitionOptional.get();
        String batchDate = exhibition.getBatchDate();
        String startTime = exhibition.getBatchStartTime();
        String endTime = exhibition.getBatchEndTime();
        LocalDateTime now = LocalDateTime.now();
        String today = now.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        String hhmm = now.toLocalTime().format( DateTimeFormatter.ofPattern("HHmm"));

        if(!today.equals(batchDate)){
            return CommonResult.failed("非今日预约");
        }
        if(hhmm.compareTo(startTime)<0 || hhmm.compareTo(endTime)>=0){
            System.out.println("开始时间："+startTime);
            System.out.println("结束时间："+endTime);
            System.out.println("当前时间："+hhmm);
            return CommonResult.failed("非本时段预约");
        }
        if(exhibition.getSuborderStatus() == OrderStatusEnum.CANCELED.getCode()){
            return CommonResult.failed("该预约已取消");
        }
        if(exhibition.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode()){
            return CommonResult.failed("该预约已核销");
        }

        exhibition.setSuborderStatus(OrderStatusEnum.FINISHED.getCode());
        exhibition.setUpdateTime(LocalDateTime.now());
        appAppointmentSuborderTemporaryExhibitionDao.save(exhibition);
        List<AppAppointmentSuborderTemporaryExhibition> exhibitions = appAppointmentSuborderTemporaryExhibitionDao.findByOrderNo(exhibition.getOrderNo());
        boolean isAllCheckout = exhibitions.stream().allMatch( e -> e.getSuborderStatus().shortValue() == OrderStatusEnum.FINISHED.getCode());
        if(isAllCheckout){
            Optional<AppAppointmentOrderTemporaryExhibition> exhibitionOptional1 = appAppointmentOrderTemporaryExhibitionDao.getByOrderNo(exhibition.getOrderNo());
            AppAppointmentOrderTemporaryExhibition order = exhibitionOptional1.get();
            order.setOrderStatus(OrderStatusEnum.FINISHED.getCode());
            appAppointmentOrderTemporaryExhibitionDao.save(order);
        }
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> getSuborderDetail(String subOrderId) {
        Optional<AppAppointmentSuborderTemporaryExhibition> exhibitionOptional = appAppointmentSuborderTemporaryExhibitionDao.getBySuborderNo(subOrderId);
        if(!exhibitionOptional.isPresent()){
            return CommonResult.failed("未找到该展览");
        }
        return CommonResult.succeeded(exhibitionOptional.get());
    }

}
