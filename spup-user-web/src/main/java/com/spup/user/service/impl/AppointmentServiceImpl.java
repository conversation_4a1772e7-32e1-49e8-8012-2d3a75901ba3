package com.spup.user.service.impl;

import com.spup.data.dao.ExhibitionDao;
import com.spup.data.dao.RoundDao;
import com.spup.data.entity.ExhibitionInfo;
import com.spup.data.entity.RoundConfig;
import com.spup.core.config.BaseAppointmentConfig;
import com.spup.core.config.AppointmentConfigDetail;
import com.spup.user.service.IAppointmentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Optional;

@Service
public class AppointmentServiceImpl implements IAppointmentService {
    @Resource
    RoundDao roundDao;
    @Resource
    ExhibitionDao exhibitionDao;
    @Resource
    BaseAppointmentConfig appointmentConfig;

    @Override
    public Optional<ExhibitionInfo> getExhibitionById(Long id) {
        return exhibitionDao.findById(id);
    }

    @Override
    public Optional<RoundConfig> getRoundByRoundDateAndExhibitionId(String exhibitionId, LocalDate roundDate) {
        return roundDao.findByExhibitionIdAndRoundDate(exhibitionId, roundDate);
    }


    @Override
    public RoundConfig.RoundStatusEnum getRoundStatus(String exhibitionId, LocalDate roundDate) {
        Optional<RoundConfig> result = getRoundByRoundDateAndExhibitionId(exhibitionId, roundDate);
        if (!result.isPresent()) {
            return null;
        }
        return result.get().getRoundStatus();
    }

    @Override
    public Boolean isAvailableByRound(String exhibitionId, LocalDate roundDate) {
        Optional<RoundConfig> result = getRoundByRoundDateAndExhibitionId(exhibitionId, roundDate);
        if (!result.isPresent()) {
            return null;
        }
        RoundConfig roundConfig = result.get();
        if (roundConfig.getRoundStatus() == RoundConfig.RoundStatusEnum.open) {
            return true;
        }
        if (roundConfig.getRoundStatus() == RoundConfig.RoundStatusEnum.close) {
            return false;
        }
        return null;
    }

    @Override
    public Boolean  isAvailableByHoliday(String exhibitionId, LocalDate roundDate) {
        AppointmentConfigDetail config = appointmentConfig.getConfig(exhibitionId);
        if(config!=null) {
            if (config.getWeekdayList().stream().anyMatch(dayOfWeek -> dayOfWeek == roundDate.getDayOfWeek())) {
                return false;
            }
            if (config.getHolidayList().stream().anyMatch(roundDate::equals)) {
                return false;
            }
        }
        return true;
    }
}
