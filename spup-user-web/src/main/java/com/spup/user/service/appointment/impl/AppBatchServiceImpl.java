package com.spup.user.service.appointment.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.spup.core.service.IAppBatchService;
import com.spup.data.dao.appointment.AppBatchDao;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.enums.BatchCategoryEnum;
import com.spup.enums.BatchStatusEnum;
import com.spup.enums.OrderCategoryEnum;

@Service
public class AppBatchServiceImpl implements IAppBatchService {

    @Resource
    private AppBatchDao appBatchDao;

    private static final Object lock = new Object();

    @Override
    public AppBatch getByNo(String batchNo, Byte batchCategory) {
        Optional<AppBatch> byBatchNoAndBatchCategory = appBatchDao.getByBatchNoAndBatchCategory(batchNo, batchCategory);
        if (!byBatchNoAndBatchCategory.isPresent()) {
            return null;
        }
        return byBatchNoAndBatchCategory.get();
    }

    @Override
    public Map<String, List<AppBatch>> getListByDate(Byte category, String startDate, String endDate) {
        List<AppBatch> batches = appBatchDao.findByBatchCategoryAndBatchDateBetween(category, startDate, endDate);
        batches.sort(Comparator.comparing(AppBatch::getBatchStartTime));
        Map<String, List<AppBatch>> batchsMap = new LinkedHashMap<>();
        for (AppBatch batch : batches) {
            String date = batch.getBatchDate();
            List<AppBatch> batchList = batchsMap.get(date);
            if (batchList == null) {
                batchList = new ArrayList<>();
                batchsMap.put(date, batchList);
            }
            batchList.add(batch);
        }
        return batchsMap;
    }

    @Override
    public List<AppBatch> getListByDate(Byte category, String date) {
        // appBatchExample.setOrderByClause( " batch_date asc, batch_start_time asc " );
        List<AppBatch> batches = appBatchDao.findByBatchCategoryAndBatchDateBetween(category, date, date);

        return batches;
    }

    @Override
    public List<AppBatch> getListByDate(String exhibitionNo, Byte category, String date) {
        List<AppBatch> listByDate = getListByDate(category, date);
        listByDate = listByDate.stream()
                .filter(appBatch -> !StringUtils.hasLength(appBatch.getBatchRemark())
                        || exhibitionNo.equals(appBatch.getBatchRemark()))
                .collect(Collectors.toList());
        return listByDate;
    }

    @Override
    public AppBatch save(AppBatch batch) {
        return appBatchDao.save(batch);
    }

    @Override
    public AppBatch update(AppBatch batch) {
        return appBatchDao.save(batch);
    }

    @Override
    public AppBatch updateRemaining(String batchNo, Byte batchCategory, int updateNum) {
        synchronized (lock) {
            AppBatch batch2 = getByNo(batchNo, batchCategory);
            if (batch2 == null) {
                return null;
            }
            appBatchDao.updateSubmitNumber(updateNum, batch2.getId(), updateNum, updateNum);
            batch2.setTicketRemaining(
                    batch2.getTicketRemaining() + updateNum > batch2.getTicketTotal() ? batch2.getTicketTotal()
                            : (batch2.getTicketRemaining() + updateNum));
            return batch2;
        }
    }

    @Override
    public void closeOverTime() {

        String yyyyMMdd = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String HHmm = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmm"));

        List<AppBatch> appBatchList = new ArrayList<>();// appBatchDao.findByBatchDateLessThan(yyyyMMdd);
        appBatchList.addAll(appBatchDao.findByBatchDateAndBatchEndTimeLessThanEqual(yyyyMMdd, HHmm));

        appBatchList.forEach(batch -> {
            if (batch.getBatchStatus().byteValue() == (byte) 1) {
                batch.setBatchStatus((byte) 0);
                appBatchDao.save(batch);
            }
        });
    }

    @Override
    public int initTicket(LocalDate startDate, LocalDate endDate) {
        String[][] times = { { "0930", "1300", "*" }, { "1300", "1600", "*" } };
        int[] ticketTotal = { 250, 250, 250, 250, 250, 400, 400 };
        init(startDate, endDate, OrderCategoryEnum.TICKET.getCode(), times, ticketTotal);
        return 0;
    }

    @Override
    public int initTeam(LocalDate startDate, LocalDate endDate) {
        String[][] times = { { "0915", "1015","*"}, { "1030", "1130","*"}, { "1300", "1400","*"},
                { "1400", "1500","*"} ,{"1500", "1600","*"} };
        int ticketTotal = 1;

        Object[][] excludeTimes = {{4,"1300"},{5,"1300"}};
        initByTimes(startDate, endDate, OrderCategoryEnum.TEAM.getCode(), times, ticketTotal,excludeTimes);
        return 0;
    }

    @Override
    public void init(LocalDate startDate, LocalDate endDate, byte category, String[][] times, int[] ticketTotal) {
        init(startDate, endDate, category, times, ticketTotal, null);
    }

    @Override
    public void init(LocalDate startDate, LocalDate endDate, byte category, String[][] times, int[] ticketTotal,
            String remark) {
        while (!startDate.isAfter(endDate)) {
            String startDateT = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            // 删除原有的
            appBatchDao.deleteAll(appBatchDao.findByBatchCategoryAndBatchDateBetween(category, startDateT, startDateT));

            int week = startDate.getDayOfWeek().getValue();

            for (int i = 0; i < times.length; i++) {
                // not "*" , get the number string of the weekday
                if (!"*".equals(times[i][2])) {
                    String weekStr = times[i][2];
                    // if the weekday number string not equals the current weekday, continue
                    if (!String.valueOf(week).equals(weekStr)) {
                        continue;
                    }
                }
                // if set the weekday, then create a new
                AppBatch batch = new AppBatch();
                batch.setBatchNo(startDateT + times[i][0]);
                batch.setBatchDate(startDateT);
                batch.setBatchStartTime(times[i][0]);
                batch.setBatchEndTime(times[i][1]);
                // use the ticketTotal array to get the ticket total number
                batch.setTicketTotal(ticketTotal[week - 1]);
                batch.setTicketRemaining(ticketTotal[week - 1]);
                batch.setBatchRemark(remark);
                batch.setBatchCategory(category);
                // default close
                // TODO
                batch.setBatchStatus(BatchStatusEnum.CLOSED.getCode());

                appBatchDao.save(batch);
            }
            startDate = startDate.plusDays(1);
        }

    }

    public void initByTimes(LocalDate startDate, LocalDate endDate, byte category, String[][] times, int ticketTotal,Object[][] excludeTimes) {
        while (!startDate.isAfter(endDate)) {
            String startDateT = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            // 删除原有的
            appBatchDao.deleteAll(appBatchDao.findByBatchCategoryAndBatchDateBetween(category, startDateT, startDateT));

            int week = startDate.getDayOfWeek().getValue();

            for (int i = 0; i < times.length; i++) {
                if (!"*".equals(times[i][2])) {
                    String weekStr = times[i][2];
                    if (!String.valueOf(week).equals(weekStr)) {
                        continue;
                    }
                }
                AppBatch batch = new AppBatch();
                batch.setBatchNo(startDateT + times[i][0]);
                batch.setBatchDate(startDateT);
                batch.setBatchStartTime(times[i][0]);
                batch.setBatchEndTime(times[i][1]);
                batch.setTicketTotal(ticketTotal);
                batch.setTicketRemaining(ticketTotal);

                for (int j = 0; j < excludeTimes.length; j++) {
                    int excludeWeek = (int)excludeTimes[j][0];
                    String startTime = (String)excludeTimes[j][1];
                    if(week == excludeWeek && batch.getBatchStartTime().equals(startTime)){
                        batch.setTicketTotal(0);
                        batch.setTicketRemaining(0);
                    }
                }

                batch.setBatchCategory(category);
                batch.setBatchStatus(BatchStatusEnum.CLOSED.getCode());

                appBatchDao.save(batch);
            }
            startDate = startDate.plusDays(1);
        }

    }

    @Override
    public int initItem(LocalDate startDate, LocalDate endDate) {
        // 10:00；11:00；14:00；15:00
        String[][] times = { { "1000", "1000", "*" }, { "1100", "1100", "*" }, { "1400", "1400", "*" },
                { "1500", "1500", "*" } };
        String[][] anniversaryTimes = {
                { "1000", "1000", "*" }, { "1010", "1010", "*" }, { "1020", "1020", "*" }, { "1030", "1030", "*" },
                { "1100", "1100", "*" }, { "1110", "1110", "*" }, { "1120", "1120", "*" }, { "1130", "1130", "*" },
                { "1200", "1200", "*" }, { "1210", "1210", "*" }, { "1220", "1220", "*" }, { "1230", "1230", "*" },
                { "1300", "1300", "*" }, { "1310", "1310", "*" }, { "1320", "1320", "*" }, { "1330", "1330", "*" },
                { "1400", "1400", "*" }, { "1410", "1410", "*" }, { "1420", "1420", "*" }, { "1430", "1430", "*" },
                { "1500", "1500", "*" }, { "1510", "1510", "*" }, { "1520", "1520", "*" }, { "1530", "1530", "*" },
        };

        // size 7 represent the 7 days in a week
        int[] ticketTotal = { 7, 7, 7, 7, 7, 7, 7 };
        if (startDate.isEqual(LocalDate.of(2025, 05, 24)) || startDate.isEqual(LocalDate.of(2025, 05, 25))) {
            init(startDate, endDate, BatchCategoryEnum.ITEM_FYPD.getCode(), anniversaryTimes, ticketTotal);
            return 0;
        }
        init(startDate, endDate, BatchCategoryEnum.ITEM_FYPD.getCode(), times, ticketTotal);
        return 0;
    }

    public static void main(String[] args) {
        LocalDate now = LocalDate.now();
        System.out.println("Day of week -> " + now.getDayOfWeek().getValue());

    }
}
