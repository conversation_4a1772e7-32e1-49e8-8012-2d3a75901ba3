package com.spup.user.service.impl;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import com.spup.core.dto.CustomerListRequest;
import com.spup.core.service.IAppCustomerService;
import com.spup.core.service.impl.BaseCustomerServiceImpl;
import com.spup.data.entity.authority.AppCustomer;

/**
 * User Customer Service Implementation
 * Extends BaseCustomerServiceImpl with user-specific operations
 */
@Service
public class AppCustomerServiceImpl extends BaseCustomerServiceImpl implements IAppCustomerService {

    /**
     * User-specific: Set additional customer fields during full save
     */
    @Override
    protected void setAdditionalCustomerFields(AppCustomer customer, String unionid, String openid,
                                             String userName, String avatar, byte gender) {
        // Set user-specific fields
        customer.setUserGender(gender);
        customer.setUserAvatarSrc(avatar);
        customer.setUserName(userName);
    }

    @Override
    public Page<AppCustomer> getPageList(CustomerListRequest param) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getPageList'");
    }
}
