package com.spup.user.service.impl;

import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import com.spup.core.dto.QuestionnaireListRequest;
import com.spup.core.service.ICommQuestionnaireService;
import com.spup.data.dao.CommQuestionnaireDao;
import com.spup.data.entity.CommQuestionnaire;

@Service
public class CommQuestionnaireServiceImpl implements ICommQuestionnaireService {
    @Resource
    private CommQuestionnaireDao commQuestionnaireDao;
    public CommQuestionnaire getQuestionnaireById(Long id){
        Optional<CommQuestionnaire> questionnaireOptional = commQuestionnaireDao.findById(id);
        if(!questionnaireOptional.isPresent()){
            return null;
        }
        return questionnaireOptional.get();
    }
    @Override
    public Page<CommQuestionnaire> getPageList(QuestionnaireListRequest listParam) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getPageList'");
    }



}
