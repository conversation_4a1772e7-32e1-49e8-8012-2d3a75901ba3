package com.spup.user.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.Predicate;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import com.spup.core.dto.GoodsListRequest;
import com.spup.core.service.IAppSurroundingGoodsService;
import com.spup.core.service.impl.BaseSurroundingGoodsServiceImpl;
import com.spup.data.entity.AppSurroundingGoods;

/**
 * User Surrounding Goods Service Implementation
 * Extends BaseSurroundingGoodsServiceImpl with user-specific operations
 */
@Service
public class AppSurroundingGoodsServiceImpl extends BaseSurroundingGoodsServiceImpl implements IAppSurroundingGoodsService {
    @Override
    public Page<AppSurroundingGoods> getListByPage(GoodsListRequest listParam) {
        //调用分页插件
        Pageable pageable = PageRequest.of(listParam.getPageNum()-1, listParam.getPageSize());
        //从数据库查询
        Specification<AppSurroundingGoods> spec = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            if(listParam.getGoodsName() != null && !listParam.getGoodsName().isEmpty()){
                list.add(cb.like(root.get("goodsName"),listParam.getGoodsName()));//筛选
            }
            if(listParam.getGoodsCategory() != null && !listParam.getGoodsCategory().isEmpty()){
                list.add(cb.equal(root.get("goodsCategory"),listParam.getGoodsCategory()));//筛选
            }
            if(listParam.getGoodsStatus()!=null){
                list.add(cb.equal(root.get("goodsStatus"),listParam.getGoodsStatus()));//筛选
            }
            query.orderBy(cb.desc(root.get("id")));//排序
            Predicate[] arr = new Predicate[list.size()];
            return cb.and(list.toArray(arr));
        };

        Page<AppSurroundingGoods> all = appSurroundingGoodsDao.findAll(spec, pageable);

        return all;
    }

    // view method is inherited from BaseSurroundingGoodsServiceImpl
}
