package com.spup.user.service;

import com.spup.data.entity.ExhibitionInfo;
import com.spup.data.entity.RoundConfig;

import java.time.LocalDate;
import java.util.Optional;

public interface IAppointmentService {
    Optional<ExhibitionInfo> getExhibitionById(Long id);
    Optional<RoundConfig> getRoundByRoundDateAndExhibitionId(String exhibitionId, LocalDate date);
    Boolean isAvailableByRound(String exhibitionId, LocalDate date);
    Boolean isAvailableByHoliday(String exhibitionId, LocalDate date);

    RoundConfig.RoundStatusEnum getRoundStatus(String exhibitionId, LocalDate roundDate);
}
