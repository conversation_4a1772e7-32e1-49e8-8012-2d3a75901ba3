package com.spup.user.service.impl;


import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.spup.core.dto.VisitGuideListRequest;
import com.spup.core.service.IAppVisitGuideService;
import com.spup.data.dao.AppVisitGuideDao;
import com.spup.data.entity.AppVisitGuide;

@Service
public class AppVisitGuideServiceImpl implements IAppVisitGuideService {
    @Resource
    private AppVisitGuideDao appVisitGuideDao;
    @Override
    public List<AppVisitGuide> getAllList() {

        List<AppVisitGuide> appVisitGuideList = appVisitGuideDao.findAll(Sort.by("sortCode").ascending());
        return appVisitGuideList;
    }

    @Override
    public AppVisitGuide view(long id) {
        return null;
    }

    @Override
    public void read(long id) {
        Optional<AppVisitGuide> guideOpt = appVisitGuideDao.findById(id);
        if(!guideOpt.isPresent()){
            return;
        }
        AppVisitGuide appVisitGuide = guideOpt.get();
        appVisitGuide.setPageViews(appVisitGuide.getPageViews()+1);
        appVisitGuideDao.save(appVisitGuide);
    }

    @Override
    public Page<AppVisitGuide> getListByPage(VisitGuideListRequest listParam) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getListByPage'");
    }

    @Override
    public List<AppVisitGuide> findAllSortByCode(int limit) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'findAllSortByCode'");
    }

    @Override
    public List<AppVisitGuide> findAllSortByPageViews(int limit) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'findAllSortByPageViews'");
    }

    @Override
    public AppVisitGuide create(AppVisitGuide appVisitGuide, String openId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'create'");
    }

    @Override
    public AppVisitGuide update(AppVisitGuide appVisitGuide, String openId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'update'");
    }

    @Override
    public int delete(long id, String openId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'delete'");
    }
}
