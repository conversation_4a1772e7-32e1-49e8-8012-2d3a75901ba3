// package com.spup.user.service.impl;


// import java.time.LocalDate;
// import java.time.format.DateTimeFormatter;
// import java.time.temporal.ChronoUnit;
// import java.util.Date;
// import java.util.List;
// import java.util.Optional;

// import javax.annotation.Resource;

// import org.springframework.stereotype.Service;

// // import com.spup.data.dao.appointment.AppWorkdayDao;
// import com.spup.data.entity.appointment.AppWorkday;
// import com.spup.enums.WorkdayEnum;
// import com.spup.user.service.IAppWorkdayService;
// import com.spup.user.service.IAppointmentService;

// @Service
// public class AppWorkdayServiceImpl implements IAppWorkdayService {

//     @Resource
//     private AppWorkdayDao appWorkdayDao;
//     @Resource
//     private IAppointmentService iAppointmentService;

//     @Override
//     public List<AppWorkday> getListByDate(String startDate, String endDate) {
//         return appWorkdayDao.findByDayBetween(startDate,endDate);
//     }

//     @Override
//     public  Optional<AppWorkday>  getByDate(String date) {
//         return appWorkdayDao.getByDay(date);
//     }

//     @Override
//     public boolean isWorkDay(String day) {
//         Optional<AppWorkday> workdayOptional = appWorkdayDao.getByDay(day);
//         if(!workdayOptional.isPresent()){
//             return false;
//         }
//         AppWorkday workday = workdayOptional.get();
//         boolean isWorkDay =   workday.getIsWorkday().byteValue() == WorkdayEnum.OPEN_DAY.getCode();
//         return isWorkDay;
//     }

//     @Override
//     public int insert(Date day) {
//         // TODO

//         return 0;
//     }

//     @Override
//     public LocalDate getDate(LocalDate date, int plusDays,byte dayType) {
//         int hasPlusDays = 0;
//         while( hasPlusDays < plusDays ) {
//             String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//             Optional<AppWorkday> workdayOptional = appWorkdayDao.getByDay(yyyyMMdd);
//             AppWorkday byDate = workdayOptional.get();
//             if( byDate.getIsWorkday().byteValue() == dayType ) {
//                 hasPlusDays++;
//             }
//             date = date.plus(1, ChronoUnit.DAYS);
//         }

//         return date;
//     }

//     @Override
//     public int getDayStatus(AppWorkday workday, String exhibitionNo) {
//         if(workday == null) return 0;
//         String day = workday.getDay();
//         LocalDate dateOfDay = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyyMMdd"));

//         if (!exhibitionNo.startsWith("L")
//                 && workday.getIsWorkday() != WorkdayEnum.OPEN_DAY.getCode()) {
//             return 0;
//         }
//         // 第一步，判断日期是否已经设置过规则，设置过规则的，按规则处理
//         Boolean availableByRound = iAppointmentService.isAvailableByRound(exhibitionNo, dateOfDay);
//         if (availableByRound != null) {
//             if (!availableByRound) {
//                 return 0;
//             } else {
//                 return 1;
//             }
//         }
//         // 第二步，判断日期是节假日
//         Boolean availableByHoliday = iAppointmentService.isAvailableByHoliday(exhibitionNo, dateOfDay);
//         Boolean availableByHoliday2 = iAppointmentService.isAvailableByHoliday("all", dateOfDay);
//         if (!availableByHoliday || !availableByHoliday2) {
//             return 0;
//         }
//         return 1;
//     }

//    /* public LocalDate getDate(LocalDate date, int plusDays,byte dayType) {
//         int hasPlusDays = 0;
//         while( hasPlusDays < plusDays ) {
//             String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//             AppWorkday byDate = mapperHandler.getByDate(yyyyMMdd);
//             if( byDate.getIsWorkday().byteValue() == dayType ) {
//                 hasPlusDays++;
//             }
//             date = date.plus(1, ChronoUnit.DAYS);
//         }

//         return date;
//     }*/
// }
