package com.spup.user.service.impl;


import java.util.List;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import com.spup.core.dto.DateQueryRequest;
import com.spup.core.service.IAppCommentsService;
import com.spup.data.dao.AppCommentsDao;
import com.spup.data.entity.AppComments;
@Service
public class AppCommentsServiceImpl implements IAppCommentsService {
    @Resource
    private AppCommentsDao appCommentsDao;
    @Override
    public AppComments save(AppComments comments) {
        return appCommentsDao.save(comments);
    }
    @Override
    public List<AppComments> findAll(int limit) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'findAll'");
    }
    @Override
    public Page<AppComments> getPageList(DateQueryRequest queryRequest) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getPageList'");
    }
}
