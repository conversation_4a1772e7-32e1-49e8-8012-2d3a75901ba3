package com.spup.user.service.impl;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.spup.core.service.BlackListService;
import com.spup.data.dao.BlackListDao;
import com.spup.data.entity.appointment.BlackList;
import com.spup.data.entity.appointment.BlackList.CategoryEnum;

@Service
public class BlackListServiceImpl implements BlackListService {
    @Resource
    private BlackListDao blackListDao;
    @Override
    public List<BlackList> findInEffect() {
        return blackListDao.findByStatus(BlackList.StatusEnum.IN_EFFECT);
    }

    @Override
    public boolean isInBlackList(String unionid,BlackList.CategoryEnum categoryEnum) {
        List<BlackList> blackLists = blackListDao.findByUnionid(unionid);

        return blackLists.stream()
                .anyMatch(blackList ->
                        (blackList.getStatus() == BlackList.StatusEnum.IN_EFFECT
                        && blackList.getCategory() == categoryEnum));
    }

    @Override
    public boolean isBreakRule(String unionid,BlackList.CategoryEnum categoryEnum) {
        //第一步，判断最新一个黑名单记录
        List<BlackList> blackLists = blackListDao.findByUnionid(unionid);

        Optional<BlackList> last = blackLists.stream()
                            .sorted(Comparator.comparing(BlackList::getLockingDateTime).reversed())
                            .findFirst();
        LocalDateTime validDateTime = LocalDateTime.now().plusDays(30);
        if(last.isPresent()){
            BlackList blackList = last.get();
            validDateTime = validDateTime.isAfter(blackList.getUnlockingDateTime())?validDateTime:blackList.getUnlockingDateTime();
        }

        return false;
    }

    @Override
    public BlackList save(BlackList entity) {
        entity.setLockingDateTime(LocalDateTime.now());
        entity.setUnlockingDateTime(LocalDateTime.now().plusDays(30));
        entity.setStatus(BlackList.StatusEnum.IN_EFFECT);
        return blackListDao.save(entity);
    }

	@Override
	public BlackList addBlackList(String unionid, CategoryEnum categoryEnum) {
		// TODO Auto-generated method stub
		throw new UnsupportedOperationException("Unimplemented method 'addBlackList'");
	}

	@Override
	public BlackList removeBlackList(Long id) {
		// TODO Auto-generated method stub
		throw new UnsupportedOperationException("Unimplemented method 'removeBlackList'");
	}
}
