package com.spup.user.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.core.dto.QuestionnaireListRequest;
import com.spup.core.service.ICommQuestionnaireAnswerService;
import com.spup.data.dao.CommQuestionnaireAnswerDao;
import com.spup.data.entity.CommQuestionnaireAnswer;

@Service
public class CommQuestionnaireAnswerServiceImpl implements ICommQuestionnaireAnswerService {
    @Resource
    private CommQuestionnaireAnswerDao commQuestionnaireAnswerDao;
    public CommQuestionnaireAnswer save(CommQuestionnaireAnswer answer, String unionid){
        answer.setUnionid(unionid);
        return commQuestionnaireAnswerDao.save(answer);
    }

    @Override
    public List<CommQuestionnaireAnswer> getAllAnswer(Long questionnaireId,String unionid) {
        List<CommQuestionnaireAnswer> answers = commQuestionnaireAnswerDao.findByUnionidAndQuestionnaireId(unionid,questionnaireId);
        return answers;
    }

    @Override
    public List<CommQuestionnaireAnswer> getAllAnswer(Long questionnaireId, QuestionnaireListRequest listParam) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAllAnswer'");
    }

    @Override
    public Page<CommQuestionnaireAnswer> getPageList(QuestionnaireListRequest listParam) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getPageList'");
    }

    @Override
    public ObjectNode getAnswerDetail(Long answerId) throws JsonProcessingException {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAnswerDetail'");
    }
}
