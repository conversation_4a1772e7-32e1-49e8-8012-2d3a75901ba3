#!/bin/bash

# Fix #7: Consolidate Test Classes
# Removes duplicate test classes and creates unified test utilities

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🔧 $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "FIX #7: CONSOLIDATE TEST CLASSES"

# List of test classes that might be duplicated
TEST_CLASSES=(
    "JacksonLocalDateTimeTest"
    "TestConfig"
)

# Function to find test files across modules
find_test_files() {
    local test_name="$1"
    local files=()
    
    # Search in all modules
    local found_files=$(find . -name "${test_name}.java" -path "*/src/test/*" -not -path "*/target/*" 2>/dev/null)
    
    while IFS= read -r file; do
        [ -n "$file" ] && files+=("$file")
    done <<< "$found_files"
    
    printf '%s\n' "${files[@]}"
}

# Function to analyze test similarity
analyze_test_similarity() {
    local test_name="$1"
    local files=($(find_test_files "$test_name"))
    
    if [ ${#files[@]} -lt 2 ]; then
        print_status "$test_name: No duplicates found"
        return 2
    fi
    
    print_status "Analyzing $test_name (${#files[@]} files found)..."
    
    # Compare first two files
    local file1="${files[0]}"
    local file2="${files[1]}"
    
    # Clean files for comparison (remove package, imports, comments)
    local temp1=$(mktemp)
    local temp2=$(mktemp)
    
    sed '/^package /d; /^import /d; /^\/\*/,/\*\//d; /^\/\//d; /^[[:space:]]*$/d' "$file1" > "$temp1"
    sed '/^package /d; /^import /d; /^\/\*/,/\*\//d; /^\/\//d; /^[[:space:]]*$/d' "$file2" > "$temp2"
    
    if diff -q "$temp1" "$temp2" > /dev/null 2>&1; then
        print_success "$test_name: IDENTICAL test classes"
        rm "$temp1" "$temp2"
        return 0  # Identical
    else
        # Check similarity percentage
        local total_lines=$(wc -l < "$temp1")
        if [ $total_lines -eq 0 ]; then
            total_lines=1
        fi
        local diff_lines=$(diff "$temp1" "$temp2" | grep -c "^[<>]" || echo "0")
        local similarity=$((100 - (diff_lines * 100 / total_lines)))
        
        if [ $similarity -gt 80 ]; then
            print_warning "$test_name: SIMILAR test classes ($similarity% similar)"
            rm "$temp1" "$temp2"
            return 1  # Similar
        else
            print_status "$test_name: DIFFERENT test classes ($similarity% similar)"
            rm "$temp1" "$temp2"
            return 2  # Different
        fi
    fi
}

# Function to consolidate identical test class
consolidate_identical_test() {
    local test_name="$1"
    local files=($(find_test_files "$test_name"))
    
    print_status "Consolidating identical test: $test_name..."
    
    # Choose the best location for the consolidated test
    local target_location="spup-core/src/test/java/com/spup/core/test/${test_name}.java"
    
    # Create target directory
    mkdir -p "spup-core/src/test/java/com/spup/core/test"
    
    # Use the first file as the base and update package
    local source_file="${files[0]}"
    
    # Create consolidated test
    sed 's/package com\.spup\..*\.test;/package com.spup.core.test;/g' "$source_file" | \
    sed 's/package com\.spup\..*;/package com.spup.core.test;/g' > "$target_location"
    
    print_success "Created consolidated test: $target_location"
    
    # Test compilation
    if mvn test-compile -q; then
        print_success "Test compilation successful for $test_name"
        
        # Remove duplicate files (keep the one in core)
        for file_path in "${files[@]}"; do
            if [ "$file_path" != "$target_location" ]; then
                rm "$file_path"
                print_success "Removed duplicate test: $file_path"
            fi
        done
        
        return 0
    else
        print_error "Test compilation failed for $test_name"
        return 1
    fi
}

# Function to create base test utilities
create_base_test_utilities() {
    print_status "Creating base test utilities..."
    
    mkdir -p "spup-core/src/test/java/com/spup/core/test/utils"
    
    # Create BaseTestUtils
    cat > "spup-core/src/test/java/com/spup/core/test/utils/BaseTestUtils.java" << 'EOF'
package com.spup.core.test.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.test.context.TestPropertySource;

/**
 * Base Test Utilities
 * Common testing utilities shared across all modules
 */
@TestPropertySource(locations = "classpath:application-test.properties")
public class BaseTestUtils {
    
    /**
     * Get configured ObjectMapper for testing
     */
    public static ObjectMapper getTestObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }
    
    /**
     * Create test data with common patterns
     */
    public static <T> T createTestData(Class<T> clazz) {
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create test data for " + clazz.getSimpleName(), e);
        }
    }
    
    /**
     * Common test validation
     */
    public static boolean isValidTestObject(Object obj) {
        return obj != null;
    }
    
    /**
     * Generate test ID
     */
    public static String generateTestId(String prefix) {
        return prefix + "_" + System.currentTimeMillis();
    }
}
EOF
    
    # Create MockDataFactory
    cat > "spup-core/src/test/java/com/spup/core/test/utils/MockDataFactory.java" << 'EOF'
package com.spup.core.test.utils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Mock Data Factory for Testing
 * Generates consistent test data across modules
 */
public class MockDataFactory {
    
    private static final Random random = new Random();
    
    /**
     * Generate mock string with prefix
     */
    public static String mockString(String prefix) {
        return prefix + "_" + random.nextInt(10000);
    }
    
    /**
     * Generate mock integer
     */
    public static Integer mockInteger() {
        return random.nextInt(1000) + 1;
    }
    
    /**
     * Generate mock long
     */
    public static Long mockLong() {
        return random.nextLong();
    }
    
    /**
     * Generate mock LocalDateTime
     */
    public static LocalDateTime mockDateTime() {
        return LocalDateTime.now().plusDays(random.nextInt(30));
    }
    
    /**
     * Generate mock list
     */
    public static <T> List<T> mockList(Class<T> clazz, int size) {
        List<T> list = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            try {
                list.add(clazz.getDeclaredConstructor().newInstance());
            } catch (Exception e) {
                // Skip if can't instantiate
            }
        }
        return list;
    }
}
EOF
    
    print_success "Created base test utilities"
}

# Function to create test configuration
create_unified_test_config() {
    print_status "Creating unified test configuration..."
    
    mkdir -p "spup-core/src/test/java/com/spup/core/test/config"
    
    cat > "spup-core/src/test/java/com/spup/core/test/config/BaseTestConfig.java" << 'EOF'
package com.spup.core.test.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * Base Test Configuration
 * Common test configuration shared across all modules
 */
@TestConfiguration
@Profile("test")
public class BaseTestConfig {
    
    /**
     * Test-specific bean configurations
     */
    @Bean
    @Primary
    public String testEnvironment() {
        return "test";
    }
}
EOF
    
    # Create test properties
    mkdir -p "spup-core/src/test/resources"
    cat > "spup-core/src/test/resources/application-test.properties" << 'EOF'
# Common test properties
spring.profiles.active=test
spring.jpa.hibernate.ddl-auto=create-drop
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# Logging for tests
logging.level.com.spup=DEBUG
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN

# Test-specific settings
management.endpoints.enabled-by-default=false
EOF
    
    print_success "Created unified test configuration"
}

# Main analysis and consolidation process
print_status "Starting analysis of test classes..."

identical_tests=()
similar_tests=()
different_tests=()

# Analyze each test class
for test in "${TEST_CLASSES[@]}"; do
    analyze_test_similarity "$test"
    case $? in
        0) identical_tests+=("$test") ;;
        1) similar_tests+=("$test") ;;
        2) different_tests+=("$test") ;;
    esac
done

print_header "TEST ANALYSIS RESULTS"

print_status "Identical tests (${#identical_tests[@]}):"
for test in "${identical_tests[@]}"; do
    echo "  ✅ $test"
done

print_status "Similar tests (${#similar_tests[@]}):"
for test in "${similar_tests[@]}"; do
    echo "  ⚠️  $test"
done

print_status "Different/No duplicates (${#different_tests[@]}):"
for test in "${different_tests[@]}"; do
    echo "  ℹ️  $test"
done

# Consolidate identical tests
if [ ${#identical_tests[@]} -gt 0 ]; then
    print_header "CONSOLIDATING IDENTICAL TESTS"
    
    for test in "${identical_tests[@]}"; do
        if consolidate_identical_test "$test"; then
            print_success "✅ Successfully consolidated $test"
        else
            print_error "❌ Failed to consolidate $test"
        fi
    done
fi

# Create base test utilities and configuration
print_header "CREATING BASE TEST INFRASTRUCTURE"
create_base_test_utilities
create_unified_test_config

# Final compilation test
print_status "Running final test compilation..."
if mvn test-compile -q; then
    print_success "✅ All test consolidations completed successfully!"
    
    print_header "TEST CONSOLIDATION SUMMARY"
    print_status "- Consolidated ${#identical_tests[@]} identical test classes"
    print_status "- Created base test utilities and configuration"
    print_status "- Enhanced test infrastructure with common patterns"
    print_status "- All test classes compile successfully"
    
    print_success "🎉 Fix #7: Test consolidation completed!"
    
else
    print_error "❌ Final test compilation failed"
    exit 1
fi

print_success "Fix #7 completed!"
