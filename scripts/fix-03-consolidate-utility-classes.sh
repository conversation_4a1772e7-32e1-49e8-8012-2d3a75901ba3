#!/bin/bash

# Fix #3: Consolidate Utility Classes
# Creates unified utility classes in spup-common and updates imports

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🔧 $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "FIX #3: CONSOLIDATE UTILITY CLASSES"

# Create unified utility classes
create_unified_utilities() {
    print_status "Creating unified utility classes in spup-common..."
    
    # Create utils directory
    mkdir -p "spup-common/src/main/java/com/spup/commons/utils"
    
    # 1. Create unified CryptoUtils (consolidates Sha256Util, SignUtil, JasyptUtil)
    print_status "Creating unified CryptoUtils..."
    cat > "spup-common/src/main/java/com/spup/commons/utils/CryptoUtils.java" << 'EOF'
package com.spup.commons.utils;

import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

import java.security.MessageDigest;
import java.util.*;

/**
 * Unified Cryptographic Utilities
 * Consolidates Sha256Util, SignUtil, and JasyptUtil functionality
 */
public class CryptoUtils {
    
    /**
     * Generate SHA-256 hash
     * Consolidated from Sha256Util.getSha256Hash()
     */
    public static String sha256Hash(String input, String charset) {
        try {
            MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
            byte[] hash = sha256.digest(input.getBytes(charset));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("SHA-256 hashing failed", e);
        }
    }
    
    /**
     * Sign parameters with SHA-256
     * Consolidated from SignUtil.signSHA256()
     */
    public static String signSHA256(Map<String, Object> params, String signKey) {
        List<String> keys = new ArrayList<>();
        for (String key : params.keySet()) {
            if (params.get(key) instanceof String) {
                keys.add(key);
            }
        }
        Collections.sort(keys);
        
        StringBuilder sign = new StringBuilder();
        for (String key : keys) {
            sign.append(params.get(key));
        }
        
        String sha256 = sha256Hash(sign.toString(), "UTF-8");
        return sha256Hash(sha256 + signKey, "UTF-8");
    }
    
    /**
     * Encrypt using Jasypt
     * Consolidated from JasyptUtil.encrypt()
     */
    public static String encrypt(String password, String plainText) {
        StringEncryptor encryptor = createEncryptor(password);
        return encryptor.encrypt(plainText);
    }
    
    /**
     * Decrypt using Jasypt
     * Consolidated from JasyptUtil.decrypt()
     */
    public static String decrypt(String password, String encryptedText) {
        StringEncryptor encryptor = createEncryptor(password);
        return encryptor.decrypt(encryptedText);
    }
    
    private static StringEncryptor createEncryptor(String password) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        
        config.setPassword(password);
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        
        encryptor.setConfig(config);
        return encryptor;
    }
}
EOF
    
    # 2. Create unified DateTimeUtils (consolidates AppointHelper)
    print_status "Creating unified DateTimeUtils..."
    cat > "spup-common/src/main/java/com/spup/commons/utils/DateTimeUtils.java" << 'EOF'
package com.spup.commons.utils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Unified Date/Time Utilities
 * Consolidates AppointHelper functionality
 */
public class DateTimeUtils {
    
    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    /**
     * Get days span for exhibition category
     * Consolidated from AppointHelper.getDaysOfSpan()
     */
    public static int getDaysOfSpan(Number exhibitionCategory) {
        switch (exhibitionCategory.intValue()) {
            case 1: return 4;
            case 2: return 15;
            case 4: return 1;
            case 16: return 15;
            case 17: return 1;
            default: return 30;
        }
    }
    
    /**
     * Get not available rules for exhibition category
     * Consolidated from AppointHelper.getNotAvailableRules()
     */
    public static List<DayOfWeek> getNotAvailableRules(Number exhibitionCategory) {
        List<DayOfWeek> result = new ArrayList<>();
        result.add(DayOfWeek.MONDAY);
        
        switch (exhibitionCategory.intValue()) {
            case 2:
            case 4:
                result.add(DayOfWeek.SATURDAY);
                result.add(DayOfWeek.SUNDAY);
                break;
            case 16:
                result.add(DayOfWeek.SATURDAY);
                result.add(DayOfWeek.SUNDAY);
                result.add(DayOfWeek.FRIDAY);
                result.add(DayOfWeek.THURSDAY);
                break;
        }
        return result;
    }
    
    /**
     * Check if a date is a workday for given exhibition category
     */
    public static boolean isWorkday(LocalDate date, Number exhibitionCategory) {
        List<DayOfWeek> notAvailableRules = getNotAvailableRules(exhibitionCategory);
        return !notAvailableRules.contains(date.getDayOfWeek());
    }
    
    /**
     * Get available dates within span for exhibition category
     */
    public static List<LocalDate> getAvailableDates(Number exhibitionCategory) {
        int daysOfSpan = getDaysOfSpan(exhibitionCategory);
        List<LocalDate> result = new ArrayList<>();
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(daysOfSpan);
        
        while (startDate.isBefore(endDate)) {
            if (isWorkday(startDate, exhibitionCategory)) {
                result.add(startDate);
            }
            startDate = startDate.plusDays(1);
        }
        return result;
    }
}
EOF
    
    print_success "Created unified utility classes"
}

# Update imports in user module
update_user_imports() {
    print_status "Updating imports in user module..."
    
    # Update Sha256Util imports
    find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.utils.Sha256Util" {} \; | while read file; do
        sed -i '' 's/import com\.spup\.user\.utils\.Sha256Util;/import com.spup.commons.utils.CryptoUtils;/g' "$file"
        sed -i '' 's/Sha256Util\.getSha256Hash/CryptoUtils.sha256Hash/g' "$file"
        print_status "Updated Sha256Util imports in $file"
    done
    
    # Update SignUtil imports
    find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.utils.SignUtil" {} \; | while read file; do
        sed -i '' 's/import com\.spup\.user\.utils\.SignUtil;/import com.spup.commons.utils.CryptoUtils;/g' "$file"
        sed -i '' 's/SignUtil\.signSHA256/CryptoUtils.signSHA256/g' "$file"
        print_status "Updated SignUtil imports in $file"
    done
    
    # Update AppointHelper imports
    find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.utils.AppointHelper" {} \; | while read file; do
        sed -i '' 's/import com\.spup\.user\.utils\.AppointHelper;/import com.spup.commons.utils.DateTimeUtils;/g' "$file"
        sed -i '' 's/AppointHelper\./DateTimeUtils./g' "$file"
        print_status "Updated AppointHelper imports in $file"
    done
}

# Update imports in admin module
update_admin_imports() {
    print_status "Updating imports in admin module..."
    
    # Update JasyptUtil imports
    find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.utils.JasyptUtil" {} \; | while read file; do
        sed -i '' 's/import com\.spup\.admin\.utils\.JasyptUtil;/import com.spup.commons.utils.CryptoUtils;/g' "$file"
        sed -i '' 's/JasyptUtil\./CryptoUtils./g' "$file"
        print_status "Updated JasyptUtil imports in $file"
    done
    
    # Update any Sha256Util imports in admin
    find spup-admin-web/src -name "*.java" -type f -exec grep -l "Sha256Util" {} \; | while read file; do
        sed -i '' 's/import.*Sha256Util;/import com.spup.commons.utils.CryptoUtils;/g' "$file"
        sed -i '' 's/Sha256Util\.getSha256Hash/CryptoUtils.sha256Hash/g' "$file"
        print_status "Updated Sha256Util imports in $file"
    done
}

# Remove duplicate utility files
remove_duplicate_utilities() {
    print_status "Removing duplicate utility files..."
    
    # List of utility files to remove
    local files_to_remove=(
        "spup-user-web/src/main/java/com/spup/user/utils/Sha256Util.java"
        "spup-user-web/src/main/java/com/spup/user/utils/SignUtil.java"
        "spup-user-web/src/main/java/com/spup/user/utils/AppointHelper.java"
        "spup-admin-web/src/main/java/com/spup/admin/utils/JasyptUtil.java"
    )
    
    for file in "${files_to_remove[@]}"; do
        if [ -f "$file" ]; then
            rm "$file"
            print_success "Removed duplicate utility: $(basename "$file")"
        else
            print_warning "Utility file not found: $file"
        fi
    done
}

# Main execution
print_status "Starting utility class consolidation..."

# Step 1: Create unified utility classes
create_unified_utilities

# Step 2: Update imports in user module
update_user_imports

# Step 3: Update imports in admin module
update_admin_imports

# Step 4: Test compilation
print_status "Testing compilation after utility consolidation..."
if mvn compile -q; then
    print_success "✅ Compilation successful after utility consolidation!"
    
    # Step 5: Remove duplicate utility files
    remove_duplicate_utilities
    
    # Final compilation test
    print_status "Final compilation test..."
    if mvn compile -q; then
        print_success "✅ Final compilation successful!"
        
        print_header "UTILITY CONSOLIDATION SUMMARY"
        print_status "Successfully created and consolidated:"
        print_status "- CryptoUtils (consolidated Sha256Util, SignUtil, JasyptUtil)"
        print_status "- DateTimeUtils (consolidated AppointHelper)"
        print_status "- Updated all imports across user and admin modules"
        print_status "- Removed duplicate utility files"
        print_status "- All modules compile successfully"
        
        print_success "🎉 Fix #3: Utility consolidation completed successfully!"
        
        print_status "Next steps:"
        print_status "1. Test the applications to ensure utilities work correctly"
        print_status "2. Commit changes: git add -A && git commit -m 'Fix #3: Consolidate utility classes'"
        print_status "3. Proceed to Fix #4: Consolidate DTO classes"
        
    else
        print_error "❌ Final compilation failed after removing duplicate utilities"
        exit 1
    fi
else
    print_error "❌ Compilation failed after utility consolidation"
    print_error "Please review the errors and fix them before proceeding"
    exit 1
fi

print_success "Fix #3 completed!"
