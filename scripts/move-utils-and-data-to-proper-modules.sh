#!/bin/bash

# Move Utils and Data to Proper Modules
# Moves all utilities to spup-common and all entities/DAOs to spup-data

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}📦 $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "MOVING UTILS AND DATA TO PROPER MODULES"

# Function to move file and update imports
move_file_and_update_imports() {
    local source_file="$1"
    local target_file="$2"
    local old_package="$3"
    local new_package="$4"
    local class_name="$5"
    
    if [ ! -f "$source_file" ]; then
        print_warning "Source file not found: $source_file"
        return 1
    fi
    
    print_status "Moving $class_name from $old_package to $new_package"
    
    # Create target directory
    mkdir -p "$(dirname "$target_file")"
    
    # Copy file and update package
    sed "s/package $old_package;/package $new_package;/g" "$source_file" > "$target_file"
    
    # Update imports across all modules
    local old_import="import ${old_package}.${class_name};"
    local new_import="import ${new_package}.${class_name};"
    
    find spup-user-web/src spup-admin-web/src spup-core/src spup-activity/src -name "*.java" -type f -exec grep -l "$old_import" {} \; 2>/dev/null | while read file; do
        sed -i '' "s|$old_import|$new_import|g" "$file"
        print_status "Updated imports in $file"
    done
    
    # Remove original file
    rm "$source_file"
    print_success "Moved $class_name to $new_package"
    
    return 0
}

# Function to test compilation
test_compilation() {
    print_status "Testing compilation..."
    if mvn compile -q; then
        print_success "Compilation successful"
        return 0
    else
        print_error "Compilation failed"
        return 1
    fi
}

print_status "Starting systematic move of utilities and data classes..."

# 1. Move all utilities from spup-core to spup-common
print_header "MOVING UTILITIES FROM SPUP-CORE TO SPUP-COMMON"

# Find all utility classes in spup-core
find spup-core/src/main/java -name "*Utils.java" -o -name "*Util.java" -o -name "*Helper.java" | while read util_file; do
    if [ -f "$util_file" ]; then
        # Extract class name and package
        class_name=$(basename "$util_file" .java)
        relative_path=${util_file#spup-core/src/main/java/}
        old_package=$(dirname "$relative_path" | tr '/' '.')
        new_package="com.spup.commons.utils"
        target_file="spup-common/src/main/java/com/spup/commons/utils/${class_name}.java"
        
        move_file_and_update_imports "$util_file" "$target_file" "$old_package" "$new_package" "$class_name"
    fi
done

# 2. Move utilities from user and admin modules to spup-common
print_header "MOVING UTILITIES FROM USER/ADMIN MODULES TO SPUP-COMMON"

# Move utilities from user module
find spup-user-web/src/main/java -name "*Utils.java" -o -name "*Util.java" -o -name "*Helper.java" | while read util_file; do
    if [ -f "$util_file" ]; then
        class_name=$(basename "$util_file" .java)
        relative_path=${util_file#spup-user-web/src/main/java/}
        old_package=$(dirname "$relative_path" | tr '/' '.')
        new_package="com.spup.commons.utils"
        target_file="spup-common/src/main/java/com/spup/commons/utils/${class_name}.java"
        
        move_file_and_update_imports "$util_file" "$target_file" "$old_package" "$new_package" "$class_name"
    fi
done

# Move utilities from admin module
find spup-admin-web/src/main/java -name "*Utils.java" -o -name "*Util.java" -o -name "*Helper.java" | while read util_file; do
    if [ -f "$util_file" ]; then
        class_name=$(basename "$util_file" .java)
        relative_path=${util_file#spup-admin-web/src/main/java/}
        old_package=$(dirname "$relative_path" | tr '/' '.')
        new_package="com.spup.commons.utils"
        target_file="spup-common/src/main/java/com/spup/commons/utils/${class_name}.java"
        
        move_file_and_update_imports "$util_file" "$target_file" "$old_package" "$new_package" "$class_name"
    fi
done

# Test compilation after utility moves
if ! test_compilation; then
    print_error "Compilation failed after moving utilities"
    print_status "Continuing with data moves..."
fi

# 3. Move entities from user and admin modules to spup-data
print_header "MOVING ENTITIES TO SPUP-DATA MODULE"

# Move entities from user module (if any exist outside spup-data)
find spup-user-web/src/main/java -path "*/entity/*" -name "*.java" | while read entity_file; do
    if [ -f "$entity_file" ]; then
        class_name=$(basename "$entity_file" .java)
        relative_path=${entity_file#spup-user-web/src/main/java/}
        old_package=$(dirname "$relative_path" | tr '/' '.')
        
        # Determine appropriate package in spup-data
        if [[ "$relative_path" == *"/authority/"* ]]; then
            new_package="com.spup.data.entity.authority"
            target_file="spup-data/src/main/java/com/spup/data/entity/authority/${class_name}.java"
        elif [[ "$relative_path" == *"/appointment/"* ]]; then
            new_package="com.spup.data.entity.appointment"
            target_file="spup-data/src/main/java/com/spup/data/entity/appointment/${class_name}.java"
        else
            new_package="com.spup.data.entity"
            target_file="spup-data/src/main/java/com/spup/data/entity/${class_name}.java"
        fi
        
        move_file_and_update_imports "$entity_file" "$target_file" "$old_package" "$new_package" "$class_name"
    fi
done

# Move entities from admin module (if any exist outside spup-data)
find spup-admin-web/src/main/java -path "*/entity/*" -name "*.java" | while read entity_file; do
    if [ -f "$entity_file" ]; then
        class_name=$(basename "$entity_file" .java)
        relative_path=${entity_file#spup-admin-web/src/main/java/}
        old_package=$(dirname "$relative_path" | tr '/' '.')
        
        # Determine appropriate package in spup-data
        if [[ "$relative_path" == *"/authority/"* ]]; then
            new_package="com.spup.data.entity.authority"
            target_file="spup-data/src/main/java/com/spup/data/entity/authority/${class_name}.java"
        elif [[ "$relative_path" == *"/appointment/"* ]]; then
            new_package="com.spup.data.entity.appointment"
            target_file="spup-data/src/main/java/com/spup/data/entity/appointment/${class_name}.java"
        else
            new_package="com.spup.data.entity"
            target_file="spup-data/src/main/java/com/spup/data/entity/${class_name}.java"
        fi
        
        move_file_and_update_imports "$entity_file" "$target_file" "$old_package" "$new_package" "$class_name"
    fi
done

# 4. Move DAOs and repositories from user and admin modules to spup-data
print_header "MOVING DAOS AND REPOSITORIES TO SPUP-DATA MODULE"

# Move DAOs from user module
find spup-user-web/src/main/java -name "*Dao.java" -o -name "*Repository.java" | while read dao_file; do
    if [ -f "$dao_file" ]; then
        class_name=$(basename "$dao_file" .java)
        relative_path=${dao_file#spup-user-web/src/main/java/}
        old_package=$(dirname "$relative_path" | tr '/' '.')
        
        # Determine appropriate package in spup-data
        if [[ "$relative_path" == *"/authority/"* ]]; then
            new_package="com.spup.data.dao.authority"
            target_file="spup-data/src/main/java/com/spup/data/dao/authority/${class_name}.java"
        elif [[ "$relative_path" == *"/appointment/"* ]]; then
            new_package="com.spup.data.dao.appointment"
            target_file="spup-data/src/main/java/com/spup/data/dao/appointment/${class_name}.java"
        else
            new_package="com.spup.data.dao"
            target_file="spup-data/src/main/java/com/spup/data/dao/${class_name}.java"
        fi
        
        move_file_and_update_imports "$dao_file" "$target_file" "$old_package" "$new_package" "$class_name"
    fi
done

# Move DAOs from admin module
find spup-admin-web/src/main/java -name "*Dao.java" -o -name "*Repository.java" | while read dao_file; do
    if [ -f "$dao_file" ]; then
        class_name=$(basename "$dao_file" .java)
        relative_path=${dao_file#spup-admin-web/src/main/java/}
        old_package=$(dirname "$relative_path" | tr '/' '.')
        
        # Determine appropriate package in spup-data
        if [[ "$relative_path" == *"/authority/"* ]]; then
            new_package="com.spup.data.dao.authority"
            target_file="spup-data/src/main/java/com/spup/data/dao/authority/${class_name}.java"
        elif [[ "$relative_path" == *"/appointment/"* ]]; then
            new_package="com.spup.data.dao.appointment"
            target_file="spup-data/src/main/java/com/spup/data/dao/appointment/${class_name}.java"
        else
            new_package="com.spup.data.dao"
            target_file="spup-data/src/main/java/com/spup/data/dao/${class_name}.java"
        fi
        
        move_file_and_update_imports "$dao_file" "$target_file" "$old_package" "$new_package" "$class_name"
    fi
done

# 5. Clean up empty directories
print_header "CLEANING UP EMPTY DIRECTORIES"

find spup-user-web/src spup-admin-web/src spup-core/src -type d -empty -delete 2>/dev/null || true
print_success "Cleaned up empty directories"

# 6. Final compilation test
print_status "Running final compilation test..."
if test_compilation; then
    print_success "✅ All utilities and data classes moved successfully!"
    
    print_header "MOVE SUMMARY"
    print_status "Successfully moved:"
    print_status "- All utility classes to spup-common/utils/"
    print_status "- All entity classes to spup-data/entity/"
    print_status "- All DAO/repository classes to spup-data/dao/"
    print_status "- Updated all imports across modules"
    print_status "- All modules compile successfully"
    
    print_success "🎉 Architectural reorganization completed!"
    
else
    print_error "❌ Final compilation failed"
    print_error "Manual review and fixes may be needed"
    exit 1
fi

print_success "Utils and data move completed!"
