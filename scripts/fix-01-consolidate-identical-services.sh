#!/bin/bash

# Fix #1: Consolidate Identical Service Implementations
# Moves identical service implementations to spup-core and updates imports

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🔧 $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "FIX #1: CONSOLIDATE IDENTICAL SERVICE IMPLEMENTATIONS"

# List of services that are nearly identical (only package/comments differ)
IDENTICAL_SERVICES=(
    "AppConfigServiceImpl"
    "AppOperateLogServiceImpl"
)

# Function to consolidate a service
consolidate_service() {
    local service_name="$1"
    local description="$2"
    
    print_status "Consolidating $service_name..."
    
    local user_service="spup-user-web/src/main/java/com/spup/user/service/impl/${service_name}.java"
    local admin_service="spup-admin-web/src/main/java/com/spup/admin/service/impl/${service_name}.java"
    local core_service="spup-core/src/main/java/com/spup/core/service/impl/${service_name}.java"
    
    # Check if both implementations exist
    if [ ! -f "$user_service" ] || [ ! -f "$admin_service" ]; then
        print_warning "Missing implementation files for $service_name"
        return 1
    fi
    
    # Create core service directory
    mkdir -p "spup-core/src/main/java/com/spup/core/service/impl"
    
    # Create consolidated service in core
    print_status "Creating consolidated $service_name in spup-core..."
    
    # Use user implementation as base and update package
    sed 's/package com\.spup\.user\.service\.impl;/package com.spup.core.service.impl;/g' "$user_service" | \
    sed 's/import com\.spup\.user\.service\./import com.spup.core.service./g' | \
    sed 's/User.*Service Implementation/Consolidated Service Implementation/g' | \
    sed 's/user-specific/module-specific/g' | \
    sed 's/User-specific/Module-specific/g' > "$core_service"
    
    print_success "Created consolidated $service_name in spup-core"
    
    # Update imports in user module
    print_status "Updating imports in user module..."
    find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.service.impl.$service_name" {} \; | while read file; do
        sed -i '' "s/import com\.spup\.user\.service\.impl\.$service_name;/import com.spup.core.service.impl.$service_name;/g" "$file"
        print_status "Updated imports in $file"
    done
    
    # Update imports in admin module
    print_status "Updating imports in admin module..."
    find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.service.impl.$service_name" {} \; | while read file; do
        sed -i '' "s/import com\.spup\.admin\.service\.impl\.$service_name;/import com.spup.core.service.impl.$service_name;/g" "$file"
        print_status "Updated imports in $file"
    done
    
    # Test compilation
    print_status "Testing compilation after $service_name consolidation..."
    if mvn compile -q; then
        print_success "Compilation successful for $service_name"
        
        # Remove duplicate files
        print_status "Removing duplicate $service_name files..."
        rm "$user_service"
        rm "$admin_service"
        print_success "Removed duplicate $service_name implementations"
        
        # Final compilation test
        if mvn compile -q; then
            print_success "✅ $service_name consolidation completed successfully!"
            return 0
        else
            print_error "Final compilation failed for $service_name"
            return 1
        fi
    else
        print_error "Compilation failed after $service_name consolidation"
        return 1
    fi
}

# Function to create corresponding service interface in core
create_core_interface() {
    local service_name="$1"
    local interface_name="I${service_name%Impl}"
    
    print_status "Creating core interface $interface_name..."
    
    local user_interface="spup-user-web/src/main/java/com/spup/user/service/${interface_name}.java"
    local admin_interface="spup-admin-web/src/main/java/com/spup/admin/service/${interface_name}.java"
    local core_interface="spup-core/src/main/java/com/spup/core/service/${interface_name}.java"
    
    # Create core service interface directory
    mkdir -p "spup-core/src/main/java/com/spup/core/service"
    
    # Use user interface as base if it exists
    if [ -f "$user_interface" ]; then
        sed 's/package com\.spup\.user\.service;/package com.spup.core.service;/g' "$user_interface" | \
        sed 's/User.*Service/Core Service/g' > "$core_interface"
        print_success "Created core interface $interface_name"
        
        # Update interface imports in the consolidated service
        local core_service="spup-core/src/main/java/com/spup/core/service/impl/${service_name}.java"
        if [ -f "$core_service" ]; then
            sed -i '' "s/import com\.spup\.core\.service\./import com.spup.core.service./g" "$core_service"
        fi
        
        return 0
    elif [ -f "$admin_interface" ]; then
        sed 's/package com\.spup\.admin\.service;/package com.spup.core.service;/g' "$admin_interface" | \
        sed 's/Admin.*Service/Core Service/g' > "$core_interface"
        print_success "Created core interface $interface_name"
        return 0
    else
        print_warning "No interface found for $service_name"
        return 1
    fi
}

# Main consolidation process
print_status "Starting consolidation of identical service implementations..."

success_count=0
total_count=${#IDENTICAL_SERVICES[@]}

for service in "${IDENTICAL_SERVICES[@]}"; do
    print_status "Processing $service..."
    
    # Create core interface first
    create_core_interface "$service"
    
    # Consolidate the service implementation
    if consolidate_service "$service" "Identical service implementation"; then
        ((success_count++))
        print_success "✅ Successfully consolidated $service"
    else
        print_error "❌ Failed to consolidate $service"
    fi
    
    echo ""
done

# Final summary
print_header "CONSOLIDATION SUMMARY"

print_status "Results:"
print_status "- Successfully consolidated: $success_count/$total_count services"
print_status "- Services moved to spup-core/service/impl/"
print_status "- Interfaces moved to spup-core/service/"
print_status "- All imports updated across modules"
print_status "- Duplicate files removed"

if [ $success_count -eq $total_count ]; then
    print_success "🎉 All identical services consolidated successfully!"
    
    print_status "Next steps:"
    print_status "1. Run tests to ensure functionality is preserved"
    print_status "2. Commit changes: git add -A && git commit -m 'Fix #1: Consolidate identical service implementations'"
    print_status "3. Proceed to Fix #2: Create base classes for similar services"
else
    print_warning "Some services failed to consolidate. Please review the errors above."
fi

print_success "Fix #1 completed!"
