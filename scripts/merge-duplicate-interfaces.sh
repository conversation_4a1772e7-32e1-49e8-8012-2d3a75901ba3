#!/bin/bash

# Merge Duplicate Interfaces and Unify Implementations
# Systematically merges duplicate interfaces and unifies their implementations

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🔗 $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "MERGING DUPLICATE INTERFACES AND UNIFYING IMPLEMENTATIONS"

# List of duplicate interfaces to merge
DUPLICATE_INTERFACES=(
    "IAppBatchService"
    "IAppCommentsService"
    "IAppCustomerContactsService"
    "IAppCustomerService"
    "IAppSurroundingGoodsService"
    "IAppTemporaryExhibitionService"
    "IAppVisitGuideService"
    "IAppWorkdayService"
    "IAppAppointmentAnalysisService"
    "IAppAppointmentItemOrderService"
    "IAppAppointmentItemSuborderService"
    "IAppAppointmentOrderCancelService"
    "IAppAppointmentOrderCheckoutService"
    "IAppAppointmentOrderService"
    "IAppAppointmentOrderTemporaryExhibitionService"
    "IAppAppointmentSuborderService"
    "IAppAppointmentTeamOrderService"
    "IAppointmentService"
    "ICommQuestionnaireAnswerService"
    "ICommQuestionnaireService"
    "IOrderService"
)

# Function to find all instances of an interface
find_interface_files() {
    local interface_name="$1"
    find . -name "${interface_name}.java" -not -path "./target/*" -not -path "./.git/*" 2>/dev/null
}

# Function to analyze interface differences
analyze_interface_differences() {
    local interface_name="$1"
    local files=($(find_interface_files "$interface_name"))
    
    if [ ${#files[@]} -lt 2 ]; then
        print_status "$interface_name: No duplicates found"
        return 2
    fi
    
    print_status "Analyzing $interface_name (${#files[@]} files found):"
    for file in "${files[@]}"; do
        echo "    📄 $file"
    done
    
    # Compare first two files (ignoring package declarations and imports)
    local temp1=$(mktemp)
    local temp2=$(mktemp)
    
    # Clean files for comparison
    sed '/^package /d; /^import /d; /^\/\*/,/\*\//d; /^\/\//d; /^[[:space:]]*$/d' "${files[0]}" > "$temp1"
    sed '/^package /d; /^import /d; /^\/\*/,/\*\//d; /^\/\//d; /^[[:space:]]*$/d' "${files[1]}" > "$temp2"
    
    if diff -q "$temp1" "$temp2" > /dev/null 2>&1; then
        print_success "$interface_name: IDENTICAL interfaces - safe to merge"
        rm "$temp1" "$temp2"
        return 0  # Identical
    else
        print_warning "$interface_name: DIFFERENT interfaces - need manual review"
        echo "    🔍 Differences found:"
        diff "$temp1" "$temp2" | head -10 | sed 's/^/      /'
        rm "$temp1" "$temp2"
        return 1  # Different
    fi
}

# Function to merge identical interfaces
merge_identical_interface() {
    local interface_name="$1"
    local files=($(find_interface_files "$interface_name"))
    
    print_status "Merging identical interface: $interface_name"
    
    # Choose target location in spup-core
    local target_file="spup-core/src/main/java/com/spup/core/service/${interface_name}.java"
    
    # Create target directory
    mkdir -p "spup-core/src/main/java/com/spup/core/service"
    
    # Use the first file as base and update package
    local source_file="${files[0]}"
    local original_package=$(grep "^package " "$source_file" | head -1 | sed 's/package \(.*\);/\1/')
    
    # Create consolidated interface
    sed "s/package ${original_package};/package com.spup.core.service;/g" "$source_file" > "$target_file"
    
    print_success "Created consolidated interface: $target_file"
    
    # Update imports across all modules
    for file_path in "${files[@]}"; do
        local pkg=$(grep "^package " "$file_path" | head -1 | sed 's/package \(.*\);/\1/')
        local old_import="import ${pkg}.${interface_name};"
        local new_import="import com.spup.core.service.${interface_name};"
        
        # Update imports in all Java files
        find spup-user-web/src spup-admin-web/src spup-core/src spup-activity/src -name "*.java" -type f -exec grep -l "$old_import" {} \; 2>/dev/null | while read java_file; do
            sed -i '' "s|$old_import|$new_import|g" "$java_file"
            print_status "Updated imports in $java_file"
        done
    done
    
    # Test compilation
    if mvn compile -q; then
        print_success "Compilation successful for $interface_name"
        
        # Remove duplicate files (keep the one in core)
        for file_path in "${files[@]}"; do
            if [ "$file_path" != "$target_file" ]; then
                rm "$file_path"
                print_success "Removed duplicate: $file_path"
            fi
        done
        
        return 0
    else
        print_error "Compilation failed for $interface_name"
        return 1
    fi
}

# Function to create unified implementation
create_unified_implementation() {
    local interface_name="$1"
    local impl_name="${interface_name#I}Impl"
    
    print_status "Creating unified implementation: $impl_name"
    
    # Find implementation files
    local impl_files=($(find . -name "${impl_name}.java" -not -path "./target/*" -not -path "./.git/*" 2>/dev/null))
    
    if [ ${#impl_files[@]} -lt 2 ]; then
        print_status "$impl_name: No duplicate implementations found"
        return 0
    fi
    
    print_status "Found ${#impl_files[@]} implementations of $impl_name:"
    for file in "${impl_files[@]}"; do
        echo "    📄 $file"
    done
    
    # For now, we'll create a base implementation that can be extended
    local base_impl_name="Base${impl_name}"
    local target_file="spup-core/src/main/java/com/spup/core/service/impl/${base_impl_name}.java"
    
    mkdir -p "spup-core/src/main/java/com/spup/core/service/impl"
    
    # Create base implementation template
    cat > "$target_file" << EOF
package com.spup.core.service.impl;

import com.spup.core.service.${interface_name};
import org.springframework.stereotype.Service;

/**
 * Base implementation for ${interface_name}
 * Contains common logic shared between modules
 * Extend this class in module-specific implementations
 */
@Service
public abstract class ${base_impl_name} implements ${interface_name} {
    
    /**
     * Common initialization logic
     */
    protected void initializeCommon() {
        // Common initialization code
    }
    
    /**
     * Module-specific initialization (to be implemented by subclasses)
     */
    protected abstract void initializeModuleSpecific();
    
    /**
     * Common validation logic
     */
    protected boolean validateCommon(Object entity) {
        return entity != null;
    }
    
    /**
     * Module-specific validation (to be implemented by subclasses)
     */
    protected abstract boolean validateModuleSpecific(Object entity);
}
EOF
    
    print_success "Created base implementation: $base_impl_name"
    return 0
}

# Function to test compilation
test_compilation() {
    print_status "Testing compilation..."
    if mvn compile -q; then
        print_success "Compilation successful"
        return 0
    else
        print_error "Compilation failed"
        return 1
    fi
}

# Main execution
print_status "Starting analysis of duplicate interfaces..."

identical_interfaces=()
different_interfaces=()

# Analyze each interface
for interface in "${DUPLICATE_INTERFACES[@]}"; do
    analyze_interface_differences "$interface"
    case $? in
        0) identical_interfaces+=("$interface") ;;
        1) different_interfaces+=("$interface") ;;
        2) ;; # No duplicates found
    esac
done

print_header "INTERFACE ANALYSIS RESULTS"

print_status "Identical interfaces (${#identical_interfaces[@]}):"
for interface in "${identical_interfaces[@]}"; do
    echo "  ✅ $interface"
done

print_status "Different interfaces (${#different_interfaces[@]}):"
for interface in "${different_interfaces[@]}"; do
    echo "  ⚠️  $interface"
done

# Merge identical interfaces
if [ ${#identical_interfaces[@]} -gt 0 ]; then
    print_header "MERGING IDENTICAL INTERFACES"
    
    for interface in "${identical_interfaces[@]}"; do
        if merge_identical_interface "$interface"; then
            print_success "✅ Successfully merged $interface"
            
            # Create unified implementation
            create_unified_implementation "$interface"
        else
            print_error "❌ Failed to merge $interface"
        fi
    done
fi

# Handle different interfaces
if [ ${#different_interfaces[@]} -gt 0 ]; then
    print_header "HANDLING DIFFERENT INTERFACES"
    
    print_status "The following interfaces have differences and need manual review:"
    for interface in "${different_interfaces[@]}"; do
        echo "  📋 $interface - requires manual analysis and merging"
    done
fi

# Final compilation test
print_status "Running final compilation test..."
if test_compilation; then
    print_success "✅ All interface merging completed successfully!"
    
    print_header "INTERFACE MERGING SUMMARY"
    print_status "Successfully merged ${#identical_interfaces[@]} identical interfaces"
    print_status "Created base implementations for unified interfaces"
    print_status "${#different_interfaces[@]} interfaces need manual review"
    print_status "All modules compile successfully"
    
    print_success "🎉 Interface consolidation completed!"
    
else
    print_error "❌ Final compilation failed"
    exit 1
fi

print_success "Interface merging completed!"
