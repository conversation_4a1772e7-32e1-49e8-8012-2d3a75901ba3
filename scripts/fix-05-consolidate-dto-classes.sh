#!/bin/bash

# Fix #5: Consolidate DTO Classes
# Analyzes and consolidates duplicate DTO classes between modules

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🔧 $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "FIX #5: CONSOLIDATE DTO CLASSES"

# List of DTO classes that might be duplicated
DTO_CLASSES=(
    "AccessToken"
    "AppCustomerContactsRequest"
    "AppCustomerRequest"
    "AppTeamOrderListRequest"
    "OrderRequest"
)

# Function to find DTO files across modules
find_dto_files() {
    local dto_name="$1"
    local files=()
    
    # Search in user module
    local user_dto=$(find spup-user-web/src -name "${dto_name}.java" -not -path "*/target/*" 2>/dev/null)
    [ -n "$user_dto" ] && files+=("$user_dto")
    
    # Search in admin module
    local admin_dto=$(find spup-admin-web/src -name "${dto_name}.java" -not -path "*/target/*" 2>/dev/null)
    [ -n "$admin_dto" ] && files+=("$admin_dto")
    
    # Search in core module
    local core_dto=$(find spup-core/src -name "${dto_name}.java" -not -path "*/target/*" 2>/dev/null)
    [ -n "$core_dto" ] && files+=("$core_dto")
    
    # Search in common module
    local common_dto=$(find spup-common/src -name "${dto_name}.java" -not -path "*/target/*" 2>/dev/null)
    [ -n "$common_dto" ] && files+=("$common_dto")
    
    printf '%s\n' "${files[@]}"
}

# Function to analyze DTO similarity
analyze_dto_similarity() {
    local dto_name="$1"
    local files=($(find_dto_files "$dto_name"))
    
    if [ ${#files[@]} -lt 2 ]; then
        print_status "$dto_name: No duplicates found"
        return 2
    fi
    
    print_status "Analyzing $dto_name (${#files[@]} files found)..."
    
    # Compare first two files
    local file1="${files[0]}"
    local file2="${files[1]}"
    
    # Clean files for comparison (remove package, imports, comments)
    local temp1=$(mktemp)
    local temp2=$(mktemp)
    
    sed '/^package /d; /^import /d; /^\/\*/,/\*\//d; /^\/\//d; /^[[:space:]]*$/d' "$file1" > "$temp1"
    sed '/^package /d; /^import /d; /^\/\*/,/\*\//d; /^\/\//d; /^[[:space:]]*$/d' "$file2" > "$temp2"
    
    if diff -q "$temp1" "$temp2" > /dev/null 2>&1; then
        print_success "$dto_name: IDENTICAL DTOs found"
        rm "$temp1" "$temp2"
        return 0  # Identical
    else
        # Check similarity percentage
        local total_lines=$(wc -l < "$temp1")
        if [ $total_lines -eq 0 ]; then
            total_lines=1
        fi
        local diff_lines=$(diff "$temp1" "$temp2" | grep -c "^[<>]" || echo "0")
        local similarity=$((100 - (diff_lines * 100 / total_lines)))
        
        if [ $similarity -gt 85 ]; then
            print_warning "$dto_name: SIMILAR DTOs ($similarity% similar)"
            rm "$temp1" "$temp2"
            return 1  # Similar
        else
            print_status "$dto_name: DIFFERENT DTOs ($similarity% similar)"
            rm "$temp1" "$temp2"
            return 2  # Different
        fi
    fi
}

# Function to consolidate identical DTO
consolidate_identical_dto() {
    local dto_name="$1"
    local files=($(find_dto_files "$dto_name"))
    
    print_status "Consolidating identical DTO: $dto_name..."
    
    # Choose the best location for the consolidated DTO
    local target_location="spup-common/src/main/java/com/spup/commons/dto/${dto_name}.java"
    
    # Create target directory
    mkdir -p "spup-common/src/main/java/com/spup/commons/dto"
    
    # Use the first file as the base and update package
    local source_file="${files[0]}"
    
    # Determine the original package to replace
    local original_package=$(grep "^package " "$source_file" | head -1 | sed 's/package \(.*\);/\1/')
    
    # Create consolidated DTO
    sed "s/package ${original_package};/package com.spup.commons.dto;/g" "$source_file" > "$target_location"
    
    print_success "Created consolidated DTO: $target_location"
    
    # Update imports across all modules
    local old_import_pattern="import ${original_package}.${dto_name};"
    local new_import="import com.spup.commons.dto.${dto_name};"
    
    # Update imports in all Java files
    find spup-user-web/src spup-admin-web/src spup-core/src -name "*.java" -type f -exec grep -l "$old_import_pattern" {} \; 2>/dev/null | while read file; do
        sed -i '' "s|$old_import_pattern|$new_import|g" "$file"
        print_status "Updated imports in $file"
    done
    
    # Also update any other package imports for this DTO
    for file_path in "${files[@]}"; do
        local pkg=$(grep "^package " "$file_path" | head -1 | sed 's/package \(.*\);/\1/')
        if [ "$pkg" != "com.spup.commons.dto" ]; then
            local import_pattern="import ${pkg}.${dto_name};"
            find spup-user-web/src spup-admin-web/src spup-core/src -name "*.java" -type f -exec grep -l "$import_pattern" {} \; 2>/dev/null | while read file; do
                sed -i '' "s|$import_pattern|$new_import|g" "$file"
                print_status "Updated imports in $file"
            done
        fi
    done
    
    # Test compilation
    if mvn compile -q; then
        print_success "Compilation successful for $dto_name"
        
        # Remove duplicate files (keep the one in commons)
        for file_path in "${files[@]}"; do
            if [ "$file_path" != "$target_location" ]; then
                rm "$file_path"
                print_success "Removed duplicate: $file_path"
            fi
        done
        
        return 0
    else
        print_error "Compilation failed for $dto_name"
        return 1
    fi
}

# Function to create unified DTO for similar ones
create_unified_dto() {
    local dto_name="$1"
    local files=($(find_dto_files "$dto_name"))
    
    print_status "Creating unified DTO for similar: $dto_name..."
    
    # For similar DTOs, we'll create a base DTO with common fields
    local base_dto_name="Base${dto_name}"
    local target_location="spup-common/src/main/java/com/spup/commons/dto/${base_dto_name}.java"
    
    mkdir -p "spup-common/src/main/java/com/spup/commons/dto"
    
    # Create a base DTO with common structure
    cat > "$target_location" << EOF
package com.spup.commons.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * Base DTO for $dto_name
 * Contains common fields shared across modules
 */
@Getter
@Setter
public abstract class $base_dto_name {
    
    /**
     * Common validation method
     */
    public boolean isValid() {
        return true;
    }
    
    /**
     * Module-specific validation (to be implemented by subclasses)
     */
    public abstract boolean validateModuleSpecific();
}
EOF
    
    print_success "Created base DTO: $base_dto_name"
}

# Main analysis and consolidation process
print_status "Starting analysis of DTO classes..."

identical_dtos=()
similar_dtos=()
different_dtos=()

# Analyze each DTO
for dto in "${DTO_CLASSES[@]}"; do
    analyze_dto_similarity "$dto"
    case $? in
        0) identical_dtos+=("$dto") ;;
        1) similar_dtos+=("$dto") ;;
        2) different_dtos+=("$dto") ;;
    esac
done

print_header "DTO ANALYSIS RESULTS"

print_status "Identical DTOs (${#identical_dtos[@]}):"
for dto in "${identical_dtos[@]}"; do
    echo "  ✅ $dto"
done

print_status "Similar DTOs (${#similar_dtos[@]}):"
for dto in "${similar_dtos[@]}"; do
    echo "  ⚠️  $dto"
done

print_status "Different/No duplicates (${#different_dtos[@]}):"
for dto in "${different_dtos[@]}"; do
    echo "  ℹ️  $dto"
done

# Consolidate identical DTOs
if [ ${#identical_dtos[@]} -gt 0 ]; then
    print_header "CONSOLIDATING IDENTICAL DTOS"
    
    for dto in "${identical_dtos[@]}"; do
        if consolidate_identical_dto "$dto"; then
            print_success "✅ Successfully consolidated $dto"
        else
            print_error "❌ Failed to consolidate $dto"
        fi
    done
fi

# Create unified DTOs for similar ones
if [ ${#similar_dtos[@]} -gt 0 ]; then
    print_header "CREATING UNIFIED DTOS FOR SIMILAR ONES"
    
    for dto in "${similar_dtos[@]}"; do
        create_unified_dto "$dto"
    done
fi

# Final compilation test
print_status "Running final compilation test..."
if mvn compile -q; then
    print_success "✅ All DTO consolidations completed successfully!"
    
    print_header "DTO CONSOLIDATION SUMMARY"
    print_status "- Consolidated ${#identical_dtos[@]} identical DTOs"
    print_status "- Created base DTOs for ${#similar_dtos[@]} similar DTOs"
    print_status "- Preserved ${#different_dtos[@]} different/unique DTOs"
    print_status "- All modules compile successfully"
    
    print_success "🎉 Fix #5: DTO consolidation completed!"
    
else
    print_error "❌ Final compilation failed"
    exit 1
fi

print_success "Fix #5 completed!"
