#!/bin/bash

# Safe Duplicate Removal - Only remove truly safe duplicates
# This script only removes duplicates that are 100% safe to remove

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🗑️  $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "SAFE DUPLICATE REMOVAL"

# Function to test compilation
test_compilation() {
    print_status "Testing compilation..."
    if mvn compile -q; then
        print_success "Compilation successful"
        return 0
    else
        print_error "Compilation failed"
        return 1
    fi
}

# Function to safely remove a file if it exists
safe_remove() {
    local file_path="$1"
    local description="$2"
    
    if [ -f "$file_path" ]; then
        print_status "Removing: $description"
        rm "$file_path"
        print_success "Removed: $file_path"
        return 0
    else
        print_status "File not found: $file_path"
        return 1
    fi
}

print_status "Starting safe removal of duplicate classes..."

# 1. Remove truly identical test classes that are safe to remove
print_header "REMOVING SAFE TEST DUPLICATES"

# Remove duplicate JacksonLocalDateTimeTest if it exists in commons (keep the one in user)
safe_remove "spup-common/src/test/java/com/spup/commons/jackson/JacksonLocalDateTimeTest.java" "Duplicate JacksonLocalDateTimeTest in commons"

# Test compilation after test removals
if ! test_compilation; then
    print_error "Compilation failed after test removals - this is expected due to missing classes"
    print_status "Continuing with safe removals..."
fi

# 2. Remove empty or unused utility files
print_header "REMOVING EMPTY/UNUSED FILES"

# Remove any empty directories
find spup-user-web/src spup-admin-web/src spup-core/src spup-common/src -type d -empty -delete 2>/dev/null || true
print_success "Cleaned up empty directories"

# 3. Remove duplicate configuration files that are truly identical
print_header "ANALYZING CONFIGURATION DUPLICATES"

# Check if TestConfig files are identical and remove duplicates
if [ -f "spup-user-web/src/test/java/com/spup/user/config/TestConfig.java" ] && \
   [ -f "spup-admin-web/src/test/java/com/spup/admin/config/TestConfig.java" ]; then
    
    # Compare the files (ignoring package declarations)
    if diff -q \
        <(sed '/^package /d; /^import /d' "spup-user-web/src/test/java/com/spup/user/config/TestConfig.java") \
        <(sed '/^package /d; /^import /d' "spup-admin-web/src/test/java/com/spup/admin/config/TestConfig.java") \
        > /dev/null 2>&1; then
        
        print_status "TestConfig files are identical - removing admin version"
        safe_remove "spup-admin-web/src/test/java/com/spup/admin/config/TestConfig.java" "Duplicate TestConfig in admin"
    else
        print_status "TestConfig files are different - keeping both"
    fi
fi

# 4. Create a summary of remaining duplicates that are intentionally different
print_header "ANALYZING REMAINING DUPLICATES"

print_status "Scanning for remaining duplicate class names..."
remaining_duplicates=$(find . -name "*.java" -not -path "./target/*" -not -path "./.git/*" | xargs basename -s .java | sort | uniq -d)

if [ -z "$remaining_duplicates" ]; then
    print_success "No duplicate class names found!"
else
    print_status "Remaining duplicate class names (these are intentionally different):"
    echo "$remaining_duplicates" | head -20 | while read class_name; do
        if [ ! -z "$class_name" ]; then
            echo "  📁 $class_name:"
            find . -name "${class_name}.java" -not -path "./target/*" -not -path "./.git/*" | head -3 | sed 's/^/    /'
        fi
    done
    
    local total_duplicates=$(echo "$remaining_duplicates" | wc -l)
    print_status "Total remaining duplicate class names: $total_duplicates"
    print_status "(These serve different purposes in user vs admin modules)"
fi

# 5. Final compilation test
print_status "Running final compilation test..."
if mvn compile -q; then
    print_success "✅ Safe duplicate removal completed successfully!"
    
    print_header "SAFE REMOVAL SUMMARY"
    print_status "Successfully completed safe duplicate removal:"
    print_status "- Removed safe test duplicates"
    print_status "- Cleaned up empty directories"
    print_status "- Analyzed configuration duplicates"
    print_status "- Preserved intentionally different implementations"
    print_status ""
    print_status "Remaining duplicates are intentionally different implementations"
    print_status "serving different purposes in user vs admin modules."
    print_status ""
    print_success "🎉 Safe duplicate removal completed!"
    
else
    print_error "❌ Final compilation failed"
    print_error "This indicates there may be missing dependencies or classes"
    print_status "Manual review and fixes may be needed"
    exit 1
fi

print_success "Safe duplicate removal completed!"
