#!/bin/bash

# SPUP项目快速编译脚本
# 支持增量编译和模块选择

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
PROJECT_ROOT=$(pwd)
SKIP_TESTS=${SKIP_TESTS:-true}
CLEAN_BUILD=${CLEAN_BUILD:-false}
PARALLEL_BUILD=${PARALLEL_BUILD:-true}

# 显示帮助信息
show_help() {
    echo "SPUP项目快速编译脚本"
    echo ""
    echo "用法: $0 [选项] [模块...]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -c, --clean         执行clean编译"
    echo "  -t, --test          运行测试"
    echo "  -s, --sequential    顺序编译（不并行）"
    echo "  -v, --verbose       详细输出"
    echo ""
    echo "模块:"
    echo "  common              编译spup-common"
    echo "  data                编译spup-data"
    echo "  core                编译spup-core"
    echo "  activity            编译spup-activity"
    echo "  admin               编译spup-admin-web"
    echo "  user                编译spup-user-web"
    echo "  web                 编译所有web模块"
    echo "  base                编译基础模块(common,data,core,activity)"
    echo "  all                 编译所有模块(默认)"
    echo ""
    echo "示例:"
    echo "  $0                  # 编译所有模块"
    echo "  $0 admin            # 只编译管理后台"
    echo "  $0 -c web           # clean编译所有web模块"
    echo "  $0 -t core          # 编译core模块并运行测试"
}

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查Maven
check_maven() {
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装或未配置PATH"
        exit 1
    fi
}

# 构建Maven命令
build_maven_command() {
    local modules="$1"
    local cmd="mvn"
    
    # 添加clean选项
    if [ "$CLEAN_BUILD" = true ]; then
        cmd="$cmd clean"
    fi
    
    # 添加目标
    if [[ "$modules" == *"web"* ]] || [[ "$modules" == *"admin"* ]] || [[ "$modules" == *"user"* ]]; then
        cmd="$cmd package"
    else
        cmd="$cmd install"
    fi
    
    # 添加模块选择
    if [ -n "$modules" ] && [ "$modules" != "all" ]; then
        cmd="$cmd -pl $modules -am"
    fi
    
    # 添加测试选项
    if [ "$SKIP_TESTS" = true ]; then
        cmd="$cmd -DskipTests"
    fi
    
    # 添加并行编译
    if [ "$PARALLEL_BUILD" = true ]; then
        cmd="$cmd -T 1C"
    fi
    
    # 添加其他选项
    cmd="$cmd -Dmaven.compile.fork=true"
    
    echo "$cmd"
}

# 解析模块参数
parse_modules() {
    local input="$1"
    local modules=""
    
    case "$input" in
        "common")
            modules="spup-common"
            ;;
        "data")
            modules="spup-data"
            ;;
        "core")
            modules="spup-core"
            ;;
        "activity")
            modules="spup-activity"
            ;;
        "admin")
            modules="spup-admin-web"
            ;;
        "user")
            modules="spup-user-web"
            ;;
        "web")
            modules="spup-admin-web,spup-user-web"
            ;;
        "base")
            modules="spup-common,spup-data,spup-core,spup-activity"
            ;;
        "all"|"")
            modules=""
            ;;
        *)
            # 直接使用输入的模块名
            modules="$input"
            ;;
    esac
    
    echo "$modules"
}

# 执行编译
execute_build() {
    local modules="$1"
    local maven_cmd=$(build_maven_command "$modules")
    
    log_info "执行编译命令: $maven_cmd"
    echo ""
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 执行编译
    if [ "$VERBOSE" = true ]; then
        eval "$maven_cmd"
    else
        eval "$maven_cmd" | grep -E "(Building|SUCCESS|FAILURE|ERROR|WARNING)"
    fi
    
    local exit_code=$?
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    if [ $exit_code -eq 0 ]; then
        log_success "编译成功 (耗时: ${duration}秒)"
        return 0
    else
        log_error "编译失败 (耗时: ${duration}秒)"
        return 1
    fi
}

# 显示编译结果
show_build_results() {
    local modules="$1"
    
    log_info "检查编译结果..."
    
    # 检查JAR文件
    if [[ "$modules" == "" ]] || [[ "$modules" == *"common"* ]]; then
        if [ -f "spup-common/target/spup-common-1.0.0.jar" ]; then
            log_success "spup-common.jar 已生成"
        fi
    fi
    
    if [[ "$modules" == "" ]] || [[ "$modules" == *"data"* ]]; then
        if [ -f "spup-data/target/spup-data-1.0.0.jar" ]; then
            log_success "spup-data.jar 已生成"
        fi
    fi
    
    if [[ "$modules" == "" ]] || [[ "$modules" == *"core"* ]]; then
        if [ -f "spup-core/target/spup-core-1.0.0.jar" ]; then
            log_success "spup-core.jar 已生成"
        fi
    fi
    
    if [[ "$modules" == "" ]] || [[ "$modules" == *"activity"* ]]; then
        if [ -f "spup-activity/target/spup-activity-1.0.0.jar" ]; then
            log_success "spup-activity.jar 已生成"
        fi
    fi
    
    # 检查WAR文件
    if [[ "$modules" == "" ]] || [[ "$modules" == *"admin"* ]]; then
        if [ -f "spup-admin-web/target/spup-admin.war" ]; then
            log_success "spup-admin.war 已生成 ($(du -h spup-admin-web/target/spup-admin.war | cut -f1))"
        fi
    fi
    
    if [[ "$modules" == "" ]] || [[ "$modules" == *"user"* ]]; then
        if [ -f "spup-user-web/target/spup.war" ]; then
            log_success "spup.war 已生成 ($(du -h spup-user-web/target/spup.war | cut -f1))"
        fi
    fi
}

# 主函数
main() {
    local modules=""
    local module_args=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                CLEAN_BUILD=true
                shift
                ;;
            -t|--test)
                SKIP_TESTS=false
                shift
                ;;
            -s|--sequential)
                PARALLEL_BUILD=false
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                module_args="$module_args $1"
                shift
                ;;
        esac
    done
    
    # 处理模块参数
    if [ -n "$module_args" ]; then
        for module in $module_args; do
            local parsed=$(parse_modules "$module")
            if [ -n "$modules" ] && [ -n "$parsed" ]; then
                modules="$modules,$parsed"
            elif [ -n "$parsed" ]; then
                modules="$parsed"
            fi
        done
    else
        modules="all"
    fi
    
    echo -e "${BLUE}🔨 SPUP项目快速编译${NC}"
    echo "模块: ${modules:-所有模块}"
    echo "Clean编译: $CLEAN_BUILD"
    echo "运行测试: $([ "$SKIP_TESTS" = true ] && echo "否" || echo "是")"
    echo "并行编译: $PARALLEL_BUILD"
    echo ""
    
    # 检查环境
    check_maven
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 执行编译
    if execute_build "$modules"; then
        show_build_results "$modules"
        echo ""
        log_success "编译完成！"
        
        # 如果编译了web模块，提示部署
        if [[ "$modules" == "" ]] || [[ "$modules" == *"admin"* ]] || [[ "$modules" == *"user"* ]]; then
            echo ""
            log_info "提示: 使用以下命令部署到Tomcat:"
            echo "  ./scripts/deploy-tomcat.sh"
        fi
    else
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
