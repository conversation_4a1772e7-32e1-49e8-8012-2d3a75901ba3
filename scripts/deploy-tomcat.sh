#!/bin/bash

# SPUP项目Tomcat自动部署脚本
# 使用方法: ./deploy-tomcat.sh [环境] [模块]
# 环境: dev|test|prod (默认: dev)
# 模块: admin|user|all (默认: all)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
ENVIRONMENT=${1:-dev}
MODULE=${2:-all}
PROJECT_ROOT=$(pwd)

# 环境配置
case $ENVIRONMENT in
    "dev")
        TOMCAT_HOME="/opt/tomcat"
        TOMCAT_PORT="8080"
        ;;
    "test")
        TOMCAT_HOME="/opt/tomcat-test"
        TOMCAT_PORT="8081"
        ;;
    "prod")
        TOMCAT_HOME="/opt/tomcat-prod"
        TOMCAT_PORT="8080"
        ;;
    *)
        echo -e "${RED}❌ 无效环境: $ENVIRONMENT${NC}"
        echo "支持的环境: dev, test, prod"
        exit 1
        ;;
esac

# 函数定义
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装或未配置PATH"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装或未配置PATH"
        exit 1
    fi
    
    # 检查Tomcat目录
    if [ ! -d "$TOMCAT_HOME" ]; then
        log_error "Tomcat目录不存在: $TOMCAT_HOME"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 编译项目
build_project() {
    log_info "开始编译项目..."
    
    cd "$PROJECT_ROOT"
    
    case $MODULE in
        "admin")
            mvn clean package -pl spup-admin-web -am -DskipTests -P$ENVIRONMENT
            ;;
        "user")
            mvn clean package -pl spup-user-web -am -DskipTests -P$ENVIRONMENT
            ;;
        "all")
            mvn clean install -DskipTests -P$ENVIRONMENT
            ;;
        *)
            log_error "无效模块: $MODULE"
            exit 1
            ;;
    esac
    
    if [ $? -ne 0 ]; then
        log_error "编译失败"
        exit 1
    fi
    
    log_success "编译完成"
}

# 停止Tomcat
stop_tomcat() {
    log_info "停止Tomcat服务..."
    
    if [ -f "$TOMCAT_HOME/bin/shutdown.sh" ]; then
        $TOMCAT_HOME/bin/shutdown.sh
        
        # 等待进程完全停止
        local count=0
        while [ $count -lt 30 ]; do
            if ! pgrep -f "$TOMCAT_HOME" > /dev/null; then
                break
            fi
            sleep 1
            count=$((count + 1))
        done
        
        # 强制杀死进程（如果还在运行）
        if pgrep -f "$TOMCAT_HOME" > /dev/null; then
            log_warning "强制停止Tomcat进程"
            pkill -f "$TOMCAT_HOME"
            sleep 2
        fi
        
        log_success "Tomcat已停止"
    else
        log_warning "Tomcat shutdown脚本不存在"
    fi
}

# 清理旧部署
cleanup_old_deployment() {
    log_info "清理旧部署文件..."
    
    case $MODULE in
        "admin")
            rm -rf $TOMCAT_HOME/webapps/spup-admin*
            rm -rf $TOMCAT_HOME/work/Catalina/localhost/spup-admin*
            ;;
        "user")
            rm -rf $TOMCAT_HOME/webapps/spup.war
            rm -rf $TOMCAT_HOME/webapps/spup
            rm -rf $TOMCAT_HOME/work/Catalina/localhost/spup
            ;;
        "all")
            rm -rf $TOMCAT_HOME/webapps/spup*
            rm -rf $TOMCAT_HOME/work/Catalina/localhost/spup*
            ;;
    esac
    
    log_success "清理完成"
}

# 部署WAR文件
deploy_wars() {
    log_info "部署WAR文件..."
    
    case $MODULE in
        "admin")
            if [ -f "spup-admin-web/target/spup-admin.war" ]; then
                cp spup-admin-web/target/spup-admin.war $TOMCAT_HOME/webapps/
                log_success "管理后台WAR文件已部署"
            else
                log_error "管理后台WAR文件不存在"
                exit 1
            fi
            ;;
        "user")
            if [ -f "spup-user-web/target/spup.war" ]; then
                cp spup-user-web/target/spup.war $TOMCAT_HOME/webapps/
                log_success "用户端WAR文件已部署"
            else
                log_error "用户端WAR文件不存在"
                exit 1
            fi
            ;;
        "all")
            if [ -f "spup-admin-web/target/spup-admin.war" ] && [ -f "spup-user-web/target/spup.war" ]; then
                cp spup-admin-web/target/spup-admin.war $TOMCAT_HOME/webapps/
                cp spup-user-web/target/spup.war $TOMCAT_HOME/webapps/
                log_success "所有WAR文件已部署"
            else
                log_error "WAR文件不完整"
                exit 1
            fi
            ;;
    esac
}

# 启动Tomcat
start_tomcat() {
    log_info "启动Tomcat服务..."
    
    if [ -f "$TOMCAT_HOME/bin/startup.sh" ]; then
        $TOMCAT_HOME/bin/startup.sh
        
        # 等待启动完成
        log_info "等待应用启动..."
        local count=0
        while [ $count -lt 60 ]; do
            if curl -s -f "http://localhost:$TOMCAT_PORT" > /dev/null 2>&1; then
                break
            fi
            sleep 2
            count=$((count + 2))
            echo -n "."
        done
        echo ""
        
        if [ $count -ge 60 ]; then
            log_warning "Tomcat启动超时，请检查日志"
        else
            log_success "Tomcat启动成功"
        fi
    else
        log_error "Tomcat startup脚本不存在"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    case $MODULE in
        "admin")
            if curl -s -f "http://localhost:$TOMCAT_PORT/spup-admin" > /dev/null; then
                log_success "管理后台部署成功: http://localhost:$TOMCAT_PORT/spup-admin"
            else
                log_error "管理后台部署失败"
            fi
            ;;
        "user")
            if curl -s -f "http://localhost:$TOMCAT_PORT/spup" > /dev/null; then
                log_success "用户端部署成功: http://localhost:$TOMCAT_PORT/spup"
            else
                log_error "用户端部署失败"
            fi
            ;;
        "all")
            local admin_ok=false
            local user_ok=false
            
            if curl -s -f "http://localhost:$TOMCAT_PORT/spup-admin" > /dev/null; then
                log_success "管理后台部署成功: http://localhost:$TOMCAT_PORT/spup-admin"
                admin_ok=true
            else
                log_error "管理后台部署失败"
            fi
            
            if curl -s -f "http://localhost:$TOMCAT_PORT/spup" > /dev/null; then
                log_success "用户端部署成功: http://localhost:$TOMCAT_PORT/spup"
                user_ok=true
            else
                log_error "用户端部署失败"
            fi
            
            if [ "$admin_ok" = true ] && [ "$user_ok" = true ]; then
                log_success "所有应用部署成功"
            fi
            ;;
    esac
}

# 显示日志
show_logs() {
    log_info "显示Tomcat日志 (Ctrl+C退出)..."
    tail -f $TOMCAT_HOME/logs/catalina.out
}

# 主函数
main() {
    echo -e "${BLUE}🚀 SPUP项目Tomcat部署脚本${NC}"
    echo "环境: $ENVIRONMENT"
    echo "模块: $MODULE"
    echo "Tomcat: $TOMCAT_HOME"
    echo ""
    
    check_environment
    build_project
    stop_tomcat
    cleanup_old_deployment
    deploy_wars
    start_tomcat
    verify_deployment
    
    echo ""
    log_success "部署完成！"
    
    # 询问是否查看日志
    read -p "是否查看Tomcat日志? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        show_logs
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
