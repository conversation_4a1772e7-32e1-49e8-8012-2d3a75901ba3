#!/bin/bash

# Fix #6: Consolidate Controller Classes
# Analyzes duplicate controller classes and creates base controllers where appropriate

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🔧 $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "FIX #6: CONSOLIDATE CONTROLLER CLASSES"

# List of controller classes that might be duplicated
CONTROLLER_CLASSES=(
    "ActivityController"
    "AppCommentsController"
    "AppCustomerController"
    "AppGoodsController"
    "AppInstructionsController"
    "AppLoginController"
    "AppVisitGuideController"
)

# Function to find controller files across modules
find_controller_files() {
    local controller_name="$1"
    local files=()
    
    # Search in user module
    local user_controller=$(find spup-user-web/src -name "${controller_name}.java" -not -path "*/target/*" 2>/dev/null)
    [ -n "$user_controller" ] && files+=("$user_controller")
    
    # Search in admin module
    local admin_controller=$(find spup-admin-web/src -name "${controller_name}.java" -not -path "*/target/*" 2>/dev/null)
    [ -n "$admin_controller" ] && files+=("$admin_controller")
    
    # Search in activity module
    local activity_controller=$(find spup-activity/src -name "${controller_name}.java" -not -path "*/target/*" 2>/dev/null)
    [ -n "$activity_controller" ] && files+=("$activity_controller")
    
    printf '%s\n' "${files[@]}"
}

# Function to analyze controller endpoints and functionality
analyze_controller_endpoints() {
    local controller_name="$1"
    local files=($(find_controller_files "$controller_name"))
    
    if [ ${#files[@]} -lt 2 ]; then
        print_status "$controller_name: No duplicates found"
        return 2
    fi
    
    print_status "Analyzing $controller_name (${#files[@]} files found)..."
    
    # Extract endpoint mappings from each file
    local temp_endpoints1=$(mktemp)
    local temp_endpoints2=$(mktemp)
    
    # Extract @RequestMapping, @GetMapping, @PostMapping, etc.
    grep -E "@(Request|Get|Post|Put|Delete|Patch)Mapping" "${files[0]}" | sort > "$temp_endpoints1"
    grep -E "@(Request|Get|Post|Put|Delete|Patch)Mapping" "${files[1]}" | sort > "$temp_endpoints2"
    
    # Compare endpoints
    if diff -q "$temp_endpoints1" "$temp_endpoints2" > /dev/null 2>&1; then
        print_warning "$controller_name: SAME endpoints - potential duplicate functionality"
        rm "$temp_endpoints1" "$temp_endpoints2"
        return 0  # Same endpoints
    else
        local common_endpoints=$(comm -12 "$temp_endpoints1" "$temp_endpoints2" | wc -l)
        local total_endpoints1=$(wc -l < "$temp_endpoints1")
        local total_endpoints2=$(wc -l < "$temp_endpoints2")
        local max_endpoints=$((total_endpoints1 > total_endpoints2 ? total_endpoints1 : total_endpoints2))
        
        if [ $max_endpoints -eq 0 ]; then
            max_endpoints=1
        fi
        
        local similarity=$((common_endpoints * 100 / max_endpoints))
        
        if [ $similarity -gt 50 ]; then
            print_warning "$controller_name: OVERLAPPING endpoints ($similarity% overlap)"
            rm "$temp_endpoints1" "$temp_endpoints2"
            return 1  # Overlapping
        else
            print_status "$controller_name: DIFFERENT endpoints ($similarity% overlap)"
            rm "$temp_endpoints1" "$temp_endpoints2"
            return 2  # Different
        fi
    fi
}

# Function to create base controller for common functionality
create_base_controller() {
    local controller_name="$1"
    local files=($(find_controller_files "$controller_name"))
    
    print_status "Creating base controller for: $controller_name..."
    
    local base_name="Base${controller_name}"
    local target_location="spup-core/src/main/java/com/spup/core/controller/${base_name}.java"
    
    mkdir -p "spup-core/src/main/java/com/spup/core/controller"
    
    # Create base controller with common patterns
    cat > "$target_location" << EOF
package com.spup.core.controller;

import com.spup.commons.api.CommonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

/**
 * Base Controller for $controller_name
 * Contains common functionality shared between modules
 */
@RestController
public abstract class $base_name {
    
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    /**
     * Common validation method
     */
    protected boolean validateRequest(Object request) {
        if (request == null) {
            logger.warn("Request object is null");
            return false;
        }
        return true;
    }
    
    /**
     * Common error response
     */
    protected <T> CommonResult<T> errorResponse(String message) {
        logger.error("Error: {}", message);
        return CommonResult.failed(message);
    }
    
    /**
     * Common success response
     */
    protected <T> CommonResult<T> successResponse(T data) {
        return CommonResult.succeeded(data);
    }
    
    /**
     * Common success response with message
     */
    protected <T> CommonResult<T> successResponse(T data, String message) {
        return CommonResult.succeeded(data, message);
    }
    
    /**
     * Module-specific initialization (to be implemented by subclasses)
     */
    protected abstract void initializeModuleSpecific();
    
    /**
     * Get module name for logging
     */
    protected abstract String getModuleName();
}
EOF
    
    print_success "Created base controller: $base_name"
}

# Function to update existing controllers to extend base
update_controller_to_extend_base() {
    local controller_name="$1"
    local files=($(find_controller_files "$controller_name"))
    local base_name="Base${controller_name}"
    
    for file_path in "${files[@]}"; do
        print_status "Updating $file_path to extend $base_name..."
        
        # Determine module name from path
        local module_name=""
        if [[ "$file_path" == *"spup-user-web"* ]]; then
            module_name="USER"
        elif [[ "$file_path" == *"spup-admin-web"* ]]; then
            module_name="ADMIN"
        elif [[ "$file_path" == *"spup-activity"* ]]; then
            module_name="ACTIVITY"
        fi
        
        # Add import for base controller
        if ! grep -q "import com.spup.core.controller.$base_name;" "$file_path"; then
            # Find the last import line and add after it
            local last_import_line=$(grep -n "^import " "$file_path" | tail -1 | cut -d: -f1)
            if [ -n "$last_import_line" ]; then
                sed -i '' "${last_import_line}a\\
import com.spup.core.controller.$base_name;
" "$file_path"
            fi
        fi
        
        # Update class declaration to extend base controller
        sed -i '' "s/public class $controller_name/public class $controller_name extends $base_name/g" "$file_path"
        
        # Add implementation of abstract methods
        if ! grep -q "getModuleName()" "$file_path"; then
            # Add methods before the last closing brace
            sed -i '' '$i\
\
    @Override\
    protected void initializeModuleSpecific() {\
        logger.info("Initializing {} module specific functionality", getModuleName());\
    }\
\
    @Override\
    protected String getModuleName() {\
        return "'"$module_name"'";\
    }
' "$file_path"
        fi
        
        print_success "Updated $file_path"
    done
}

# Main analysis and consolidation process
print_status "Starting analysis of controller classes..."

same_endpoint_controllers=()
overlapping_controllers=()
different_controllers=()

# Analyze each controller
for controller in "${CONTROLLER_CLASSES[@]}"; do
    analyze_controller_endpoints "$controller"
    case $? in
        0) same_endpoint_controllers+=("$controller") ;;
        1) overlapping_controllers+=("$controller") ;;
        2) different_controllers+=("$controller") ;;
    esac
done

print_header "CONTROLLER ANALYSIS RESULTS"

print_status "Controllers with same endpoints (${#same_endpoint_controllers[@]}):"
for controller in "${same_endpoint_controllers[@]}"; do
    echo "  ⚠️  $controller"
done

print_status "Controllers with overlapping endpoints (${#overlapping_controllers[@]}):"
for controller in "${overlapping_controllers[@]}"; do
    echo "  ⚠️  $controller"
done

print_status "Controllers with different endpoints (${#different_controllers[@]}):"
for controller in "${different_controllers[@]}"; do
    echo "  ℹ️  $controller"
done

# Create base controllers for controllers with common patterns
controllers_to_enhance=("${same_endpoint_controllers[@]}" "${overlapping_controllers[@]}")

if [ ${#controllers_to_enhance[@]} -gt 0 ]; then
    print_header "CREATING BASE CONTROLLERS"
    
    for controller in "${controllers_to_enhance[@]}"; do
        create_base_controller "$controller"
        update_controller_to_extend_base "$controller"
    done
fi

# Final compilation test
print_status "Running final compilation test..."
if mvn compile -q; then
    print_success "✅ All controller consolidations completed successfully!"
    
    print_header "CONTROLLER CONSOLIDATION SUMMARY"
    print_status "- Created base controllers for ${#controllers_to_enhance[@]} controllers"
    print_status "- Enhanced controllers with common functionality"
    print_status "- Preserved different controllers as separate implementations"
    print_status "- All modules compile successfully"
    
    print_success "🎉 Fix #6: Controller consolidation completed!"
    
else
    print_error "❌ Final compilation failed"
    exit 1
fi

print_success "Fix #6 completed!"
