#!/bin/bash

# Fix #4: Consolidate Remaining Service Implementations
# Analyzes and consolidates the remaining duplicate service implementations

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🔧 $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "FIX #4: CONSOLIDATE REMAINING SERVICE IMPLEMENTATIONS"

# List of remaining service implementations to analyze
REMAINING_SERVICES=(
    "AppCustomerServiceImpl"
    "AppSurroundingGoodsServiceImpl"
    "AppAppointmentAnalysisServiceImpl"
    "AppBatchServiceImpl"
    "AppCommentsServiceImpl"
    "AppCustomerContactsServiceImpl"
    "AppTemporaryExhibitionServiceImpl"
    "AppVisitGuideServiceImpl"
    "AppWorkdayServiceImpl"
    "AppointmentServiceImpl"
    "BlackListServiceImpl"
    "CommQuestionnaireAnswerServiceImpl"
    "CommQuestionnaireServiceImpl"
)

# Function to analyze if two service implementations are identical or similar
analyze_service_similarity() {
    local service_name="$1"
    local user_service="spup-user-web/src/main/java/com/spup/user/service/impl/${service_name}.java"
    local admin_service="spup-admin-web/src/main/java/com/spup/admin/service/impl/${service_name}.java"
    
    if [ ! -f "$user_service" ] || [ ! -f "$admin_service" ]; then
        print_warning "Missing implementation files for $service_name"
        return 2
    fi
    
    print_status "Analyzing $service_name..."
    
    # Remove package declarations and comments for comparison
    local temp_user=$(mktemp)
    local temp_admin=$(mktemp)
    
    # Clean files for comparison (remove package, imports, comments)
    sed '/^package /d; /^import /d; /^\/\*/,/\*\//d; /^\/\//d; /^[[:space:]]*$/d' "$user_service" > "$temp_user"
    sed '/^package /d; /^import /d; /^\/\*/,/\*\//d; /^\/\//d; /^[[:space:]]*$/d' "$admin_service" > "$temp_admin"
    
    if diff -q "$temp_user" "$temp_admin" > /dev/null 2>&1; then
        print_success "$service_name: IDENTICAL implementations"
        rm "$temp_user" "$temp_admin"
        return 0  # Identical
    else
        # Check similarity percentage
        local total_lines=$(wc -l < "$temp_user")
        local diff_lines=$(diff "$temp_user" "$temp_admin" | grep -c "^[<>]")
        local similarity=$((100 - (diff_lines * 100 / total_lines)))
        
        if [ $similarity -gt 80 ]; then
            print_warning "$service_name: SIMILAR implementations ($similarity% similar)"
            rm "$temp_user" "$temp_admin"
            return 1  # Similar
        else
            print_status "$service_name: DIFFERENT implementations ($similarity% similar)"
            rm "$temp_user" "$temp_admin"
            return 2  # Different
        fi
    fi
}

# Function to consolidate identical service
consolidate_identical_service() {
    local service_name="$1"
    
    print_status "Consolidating identical service: $service_name..."
    
    local user_service="spup-user-web/src/main/java/com/spup/user/service/impl/${service_name}.java"
    local admin_service="spup-admin-web/src/main/java/com/spup/admin/service/impl/${service_name}.java"
    local core_service="spup-core/src/main/java/com/spup/core/service/impl/${service_name}.java"
    
    # Create core service directory
    mkdir -p "spup-core/src/main/java/com/spup/core/service/impl"
    
    # Create consolidated service in core (use user implementation as base)
    sed 's/package com\.spup\.user\.service\.impl;/package com.spup.core.service.impl;/g' "$user_service" | \
    sed 's/import com\.spup\.user\.service\./import com.spup.core.service./g' | \
    sed 's/import com\.spup\.user\./import com.spup.core./g' | \
    sed 's/User.*Service Implementation/Consolidated Service Implementation/g' > "$core_service"
    
    # Create corresponding interface in core
    local interface_name="I${service_name%Impl}"
    local user_interface="spup-user-web/src/main/java/com/spup/user/service/${interface_name}.java"
    local core_interface="spup-core/src/main/java/com/spup/core/service/${interface_name}.java"
    
    mkdir -p "spup-core/src/main/java/com/spup/core/service"
    
    if [ -f "$user_interface" ]; then
        sed 's/package com\.spup\.user\.service;/package com.spup.core.service;/g' "$user_interface" > "$core_interface"
    fi
    
    # Update imports in both modules
    find spup-user-web/src -name "*.java" -type f -exec grep -l "com.spup.user.service.impl.$service_name" {} \; | while read file; do
        sed -i '' "s/import com\.spup\.user\.service\.impl\.$service_name;/import com.spup.core.service.impl.$service_name;/g" "$file"
    done
    
    find spup-admin-web/src -name "*.java" -type f -exec grep -l "com.spup.admin.service.impl.$service_name" {} \; | while read file; do
        sed -i '' "s/import com\.spup\.admin\.service\.impl\.$service_name;/import com.spup.core.service.impl.$service_name;/g" "$file"
    done
    
    # Test compilation
    if mvn compile -q; then
        print_success "Compilation successful for $service_name"
        # Remove duplicate files
        rm "$user_service" "$admin_service"
        [ -f "$user_interface" ] && rm "$user_interface"
        [ -f "spup-admin-web/src/main/java/com/spup/admin/service/${interface_name}.java" ] && rm "spup-admin-web/src/main/java/com/spup/admin/service/${interface_name}.java"
        return 0
    else
        print_error "Compilation failed for $service_name"
        return 1
    fi
}

# Function to create base class for similar services
create_base_class_for_similar() {
    local service_name="$1"
    
    print_status "Creating base class for similar service: $service_name..."
    
    local base_name="Base${service_name}"
    local user_service="spup-user-web/src/main/java/com/spup/user/service/impl/${service_name}.java"
    local admin_service="spup-admin-web/src/main/java/com/spup/admin/service/impl/${service_name}.java"
    local base_service="spup-core/src/main/java/com/spup/core/service/impl/${base_name}.java"
    
    # Extract common methods and create base class
    # This is a simplified approach - in practice, you'd need more sophisticated analysis
    cat > "$base_service" << EOF
package com.spup.core.service.impl;

import org.springframework.stereotype.Service;

/**
 * Base implementation for $service_name
 * Contains common logic shared between admin and user modules
 */
@Service
public abstract class $base_name {
    
    /**
     * Common initialization logic
     */
    protected void initializeCommon() {
        // Common initialization code
    }
    
    /**
     * Module-specific initialization (to be implemented by subclasses)
     */
    protected abstract void initializeModuleSpecific();
    
    /**
     * Common validation logic
     */
    protected boolean validateCommon(Object entity) {
        return entity != null;
    }
    
    /**
     * Module-specific validation (to be implemented by subclasses)
     */
    protected abstract boolean validateModuleSpecific(Object entity);
}
EOF
    
    print_success "Created base class: $base_name"
}

# Main analysis and consolidation process
print_status "Starting analysis of remaining service implementations..."

identical_services=()
similar_services=()
different_services=()

# Analyze each service
for service in "${REMAINING_SERVICES[@]}"; do
    analyze_service_similarity "$service"
    case $? in
        0) identical_services+=("$service") ;;
        1) similar_services+=("$service") ;;
        2) different_services+=("$service") ;;
    esac
done

print_header "ANALYSIS RESULTS"

print_status "Identical services (${#identical_services[@]}):"
for service in "${identical_services[@]}"; do
    echo "  ✅ $service"
done

print_status "Similar services (${#similar_services[@]}):"
for service in "${similar_services[@]}"; do
    echo "  ⚠️  $service"
done

print_status "Different services (${#different_services[@]}):"
for service in "${different_services[@]}"; do
    echo "  ℹ️  $service"
done

# Consolidate identical services
if [ ${#identical_services[@]} -gt 0 ]; then
    print_header "CONSOLIDATING IDENTICAL SERVICES"
    
    for service in "${identical_services[@]}"; do
        if consolidate_identical_service "$service"; then
            print_success "✅ Successfully consolidated $service"
        else
            print_error "❌ Failed to consolidate $service"
        fi
    done
fi

# Create base classes for similar services
if [ ${#similar_services[@]} -gt 0 ]; then
    print_header "CREATING BASE CLASSES FOR SIMILAR SERVICES"
    
    for service in "${similar_services[@]}"; do
        create_base_class_for_similar "$service"
    done
fi

# Final compilation test
print_status "Running final compilation test..."
if mvn compile -q; then
    print_success "✅ All consolidations completed successfully!"
    
    print_header "CONSOLIDATION SUMMARY"
    print_status "- Consolidated ${#identical_services[@]} identical services"
    print_status "- Created base classes for ${#similar_services[@]} similar services"
    print_status "- Preserved ${#different_services[@]} different services"
    print_status "- All modules compile successfully"
    
    print_success "🎉 Fix #4: Remaining service consolidation completed!"
    
else
    print_error "❌ Final compilation failed"
    exit 1
fi

print_success "Fix #4 completed!"
