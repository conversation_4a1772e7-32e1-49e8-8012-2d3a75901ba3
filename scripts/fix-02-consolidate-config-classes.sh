#!/bin/bash

# Fix #2: Consolidate Configuration Classes
# Creates base configuration classes and updates module-specific configs

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🔧 $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "FIX #2: CONSOLIDATE CONFIGURATION CLASSES"

# Create enhanced base configuration classes
create_base_configs() {
    print_status "Creating enhanced base configuration classes..."
    
    # Create config directory
    mkdir -p "spup-core/src/main/java/com/spup/core/config"
    
    # 1. Enhanced BaseWebConfig with common resource handlers
    print_status "Creating enhanced BaseWebConfig..."
    cat > "spup-core/src/main/java/com/spup/core/config/BaseWebConfig.java" << 'EOF'
package com.spup.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Enhanced Base Web Configuration
 * Provides common resource handlers and web MVC configuration
 */
@Configuration
public abstract class BaseWebConfig implements WebMvcConfigurer {
    
    /**
     * Add common resource handlers (Swagger, static resources)
     */
    protected void addCommonResourceHandlers(ResourceHandlerRegistry registry) {
        // Static resources
        registry.addResourceHandler("/html/**")
                .addResourceLocations("classpath:/html/");
        
        // Swagger UI resources
        registry.addResourceHandler("/swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        
        // API documentation
        registry.addResourceHandler("/doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");
    }
    
    /**
     * Module-specific resource handlers (to be implemented by subclasses)
     */
    protected void addModuleSpecificResourceHandlers(ResourceHandlerRegistry registry) {
        // Default implementation - no additional handlers
    }
    
    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // Add common resource handlers
        addCommonResourceHandlers(registry);
        
        // Add module-specific resource handlers
        addModuleSpecificResourceHandlers(registry);
    }
}
EOF
    
    # 2. BaseOpenApiConfig for Swagger configuration
    print_status "Creating BaseOpenApiConfig..."
    cat > "spup-core/src/main/java/com/spup/core/config/BaseOpenApiConfig.java" << 'EOF'
package com.spup.core.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Base OpenAPI Configuration
 * Provides common Swagger/OpenAPI configuration with module-specific customization
 */
@Configuration
public abstract class BaseOpenApiConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title(getApiTitle())
                        .description(getApiDescription())
                        .version(getApiVersion())
                        .contact(new Contact()
                                .name("SPUP Development Team")
                                .email("<EMAIL>")));
    }
    
    /**
     * Get API title (to be implemented by subclasses)
     */
    protected abstract String getApiTitle();
    
    /**
     * Get API description (to be implemented by subclasses)
     */
    protected abstract String getApiDescription();
    
    /**
     * Get API version (default implementation)
     */
    protected String getApiVersion() {
        return "1.0.0";
    }
}
EOF
    
    # 3. BaseExceptionControllerAdvice for global exception handling
    print_status "Creating BaseExceptionControllerAdvice..."
    cat > "spup-core/src/main/java/com/spup/core/config/BaseExceptionControllerAdvice.java" << 'EOF'
package com.spup.core.config;

import com.spup.common.CommonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Base Exception Controller Advice
 * Provides common exception handling with module-specific customization
 */
@RestControllerAdvice
public abstract class BaseExceptionControllerAdvice {
    
    private static final Logger logger = LoggerFactory.getLogger(BaseExceptionControllerAdvice.class);
    
    /**
     * Handle general exceptions
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public CommonResult<String> handleException(Exception e) {
        logger.error("Unhandled exception in {}: {}", getModuleName(), e.getMessage(), e);
        return CommonResult.failed("Internal server error: " + e.getMessage());
    }
    
    /**
     * Handle illegal argument exceptions
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public CommonResult<String> handleIllegalArgumentException(IllegalArgumentException e) {
        logger.warn("Invalid argument in {}: {}", getModuleName(), e.getMessage());
        return CommonResult.failed("Invalid argument: " + e.getMessage());
    }
    
    /**
     * Get module name for logging (to be implemented by subclasses)
     */
    protected abstract String getModuleName();
}
EOF
    
    print_success "Created enhanced base configuration classes"
}

# Update user module configurations
update_user_configs() {
    print_status "Updating user module configurations..."
    
    # Update user WebConfig
    if [ -f "spup-user-web/src/main/java/com/spup/user/javaConfig/WebConfig.java" ]; then
        print_status "Updating user WebConfig to extend BaseWebConfig..."
        cat > "spup-user-web/src/main/java/com/spup/user/javaConfig/WebConfig.java" << 'EOF'
package com.spup.user.javaConfig;

import com.spup.core.config.BaseWebConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

/**
 * User Module Web Configuration
 * Extends BaseWebConfig with user-specific settings
 */
@Configuration
public class WebConfig extends BaseWebConfig {
    
    @Override
    protected void addModuleSpecificResourceHandlers(ResourceHandlerRegistry registry) {
        // Add user-specific resource handlers here if needed
        // Example: registry.addResourceHandler("/user-assets/**").addResourceLocations("classpath:/user-assets/");
    }
}
EOF
        print_success "Updated user WebConfig"
    fi
    
    # Update user OpenApiConfig
    if [ -f "spup-user-web/src/main/java/com/spup/user/javaConfig/OpenApiConfig.java" ]; then
        print_status "Updating user OpenApiConfig to extend BaseOpenApiConfig..."
        cat > "spup-user-web/src/main/java/com/spup/user/javaConfig/OpenApiConfig.java" << 'EOF'
package com.spup.user.javaConfig;

import com.spup.core.config.BaseOpenApiConfig;
import org.springframework.context.annotation.Configuration;

/**
 * User Module OpenAPI Configuration
 * Extends BaseOpenApiConfig with user-specific settings
 */
@Configuration
public class OpenApiConfig extends BaseOpenApiConfig {
    
    @Override
    protected String getApiTitle() {
        return "浦东规划馆用户端API";
    }
    
    @Override
    protected String getApiDescription() {
        return "用户端接口文档详情信息 - 基于SpringDoc OpenAPI 3";
    }
}
EOF
        print_success "Updated user OpenApiConfig"
    fi
    
    # Update user ExceptionControllerAdvice
    if [ -f "spup-user-web/src/main/java/com/spup/user/javaConfig/ExceptionControllerAdvice.java" ]; then
        print_status "Updating user ExceptionControllerAdvice to extend base class..."
        cat > "spup-user-web/src/main/java/com/spup/user/javaConfig/ExceptionControllerAdvice.java" << 'EOF'
package com.spup.user.javaConfig;

import com.spup.core.config.BaseExceptionControllerAdvice;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * User Module Exception Controller Advice
 * Extends BaseExceptionControllerAdvice with user-specific exception handling
 */
@RestControllerAdvice
public class ExceptionControllerAdvice extends BaseExceptionControllerAdvice {
    
    @Override
    protected String getModuleName() {
        return "USER-MODULE";
    }
    
    // Add user-specific exception handlers here if needed
}
EOF
        print_success "Updated user ExceptionControllerAdvice"
    fi
}

# Update admin module configurations
update_admin_configs() {
    print_status "Updating admin module configurations..."
    
    # Update admin WebConfig
    if [ -f "spup-admin-web/src/main/java/com/spup/admin/javaConfig/WebConfig.java" ]; then
        print_status "Updating admin WebConfig to extend BaseWebConfig..."
        cat > "spup-admin-web/src/main/java/com/spup/admin/javaConfig/WebConfig.java" << 'EOF'
package com.spup.admin.javaConfig;

import com.spup.core.config.BaseWebConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

/**
 * Admin Module Web Configuration
 * Extends BaseWebConfig with admin-specific settings
 */
@Configuration
public class WebConfig extends BaseWebConfig {
    
    @Override
    protected void addModuleSpecificResourceHandlers(ResourceHandlerRegistry registry) {
        // Add admin-specific resource handlers here if needed
        // Example: registry.addResourceHandler("/admin-assets/**").addResourceLocations("classpath:/admin-assets/");
    }
}
EOF
        print_success "Updated admin WebConfig"
    fi
    
    # Update admin OpenApiConfig
    if [ -f "spup-admin-web/src/main/java/com/spup/admin/javaConfig/OpenApiConfig.java" ]; then
        print_status "Updating admin OpenApiConfig to extend BaseOpenApiConfig..."
        cat > "spup-admin-web/src/main/java/com/spup/admin/javaConfig/OpenApiConfig.java" << 'EOF'
package com.spup.admin.javaConfig;

import com.spup.core.config.BaseOpenApiConfig;
import org.springframework.context.annotation.Configuration;

/**
 * Admin Module OpenAPI Configuration
 * Extends BaseOpenApiConfig with admin-specific settings
 */
@Configuration
public class OpenApiConfig extends BaseOpenApiConfig {
    
    @Override
    protected String getApiTitle() {
        return "浦东规划馆后台管理API";
    }
    
    @Override
    protected String getApiDescription() {
        return "后台管理接口文档详情信息 - 基于SpringDoc OpenAPI 3";
    }
}
EOF
        print_success "Updated admin OpenApiConfig"
    fi
    
    # Update admin ExceptionControllerAdvice
    if [ -f "spup-admin-web/src/main/java/com/spup/admin/javaConfig/ExceptionControllerAdvice.java" ]; then
        print_status "Updating admin ExceptionControllerAdvice to extend base class..."
        cat > "spup-admin-web/src/main/java/com/spup/admin/javaConfig/ExceptionControllerAdvice.java" << 'EOF'
package com.spup.admin.javaConfig;

import com.spup.core.config.BaseExceptionControllerAdvice;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Admin Module Exception Controller Advice
 * Extends BaseExceptionControllerAdvice with admin-specific exception handling
 */
@RestControllerAdvice
public class ExceptionControllerAdvice extends BaseExceptionControllerAdvice {
    
    @Override
    protected String getModuleName() {
        return "ADMIN-MODULE";
    }
    
    // Add admin-specific exception handlers here if needed
}
EOF
        print_success "Updated admin ExceptionControllerAdvice"
    fi
}

# Main execution
print_status "Starting configuration class consolidation..."

# Step 1: Create base configuration classes
create_base_configs

# Step 2: Update user module configurations
update_user_configs

# Step 3: Update admin module configurations
update_admin_configs

# Step 4: Test compilation
print_status "Testing compilation after configuration consolidation..."
if mvn compile -q; then
    print_success "✅ Compilation successful after configuration consolidation!"
    
    print_header "CONFIGURATION CONSOLIDATION SUMMARY"
    print_status "Successfully created and updated:"
    print_status "- BaseWebConfig with common resource handlers"
    print_status "- BaseOpenApiConfig with module-specific extensions"
    print_status "- BaseExceptionControllerAdvice with unified exception handling"
    print_status "- Updated user module configurations to extend base classes"
    print_status "- Updated admin module configurations to extend base classes"
    print_status "- All modules compile successfully"
    
    print_success "🎉 Fix #2: Configuration consolidation completed successfully!"
    
    print_status "Next steps:"
    print_status "1. Test the applications to ensure configurations work correctly"
    print_status "2. Commit changes: git add -A && git commit -m 'Fix #2: Consolidate configuration classes'"
    print_status "3. Proceed to Fix #3: Consolidate utility classes"
    
else
    print_error "❌ Compilation failed after configuration consolidation"
    print_error "Please review the errors and fix them before proceeding"
    exit 1
fi

print_success "Fix #2 completed!"
