#!/bin/bash

# Remove Duplicate Classes - Systematic Removal
# Actually removes the duplicate classes that can be safely consolidated

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${PURPLE}🗑️  $1${NC}"
    echo "=================================================="
}

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pom.xml" ] || [ ! -d "spup-user-web" ] || [ ! -d "spup-admin-web" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "REMOVING DUPLICATE CLASSES"

# Function to safely remove duplicate and update imports
remove_duplicate_class() {
    local class_name="$1"
    local keep_location="$2"
    local remove_location="$3"
    local new_import="$4"
    
    print_status "Removing duplicate: $class_name"
    print_status "  Keep: $keep_location"
    print_status "  Remove: $remove_location"
    
    if [ ! -f "$keep_location" ]; then
        print_error "Keep location doesn't exist: $keep_location"
        return 1
    fi
    
    if [ ! -f "$remove_location" ]; then
        print_warning "Remove location doesn't exist: $remove_location"
        return 0
    fi
    
    # Extract old import from the file being removed
    local old_package=$(grep "^package " "$remove_location" | head -1 | sed 's/package \(.*\);/\1/')
    local old_import="import ${old_package}.${class_name};"
    
    # Update all imports across the codebase
    find spup-user-web/src spup-admin-web/src spup-core/src spup-activity/src -name "*.java" -type f -exec grep -l "$old_import" {} \; 2>/dev/null | while read file; do
        sed -i '' "s|$old_import|$new_import|g" "$file"
        print_status "Updated imports in $file"
    done
    
    # Remove the duplicate file
    rm "$remove_location"
    print_success "Removed duplicate: $remove_location"
    
    return 0
}

# Function to test compilation after removal
test_compilation() {
    print_status "Testing compilation..."
    if mvn compile -q; then
        print_success "Compilation successful"
        return 0
    else
        print_error "Compilation failed"
        return 1
    fi
}

print_status "Starting systematic removal of duplicate classes..."

# 1. Remove duplicate AvailableDayResponse (keep commons version)
print_header "REMOVING DUPLICATE DTOS"

if remove_duplicate_class "AvailableDayResponse" \
    "spup-common/src/main/java/com/spup/commons/dto/AvailableDayResponse.java" \
    "spup-core/src/main/java/com/spup/core/dto/AvailableDayResponse.java" \
    "import com.spup.commons.dto.AvailableDayResponse;"; then
    print_success "✅ Removed duplicate AvailableDayResponse"
fi

# 2. Remove duplicate DateTimeUtils (keep commons version)
if remove_duplicate_class "DateTimeUtils" \
    "spup-common/src/main/java/com/spup/commons/utils/DateTimeUtils.java" \
    "spup-core/src/main/java/com/spup/core/util/DateTimeUtils.java" \
    "import com.spup.commons.utils.DateTimeUtils;"; then
    print_success "✅ Removed duplicate DateTimeUtils"
fi

# Test compilation after DTO removals
if ! test_compilation; then
    print_error "Stopping due to compilation failure"
    exit 1
fi

# 3. Remove duplicate service interfaces (keep core versions)
print_header "REMOVING DUPLICATE SERVICE INTERFACES"

# IAppConfigService - remove from user and admin, keep core
if [ -f "spup-user-web/src/main/java/com/spup/user/service/IAppConfigService.java" ]; then
    remove_duplicate_class "IAppConfigService" \
        "spup-core/src/main/java/com/spup/core/service/IAppConfigService.java" \
        "spup-user-web/src/main/java/com/spup/user/service/IAppConfigService.java" \
        "import com.spup.core.service.IAppConfigService;"
fi

if [ -f "spup-admin-web/src/main/java/com/spup/admin/service/IAppConfigService.java" ]; then
    remove_duplicate_class "IAppConfigService" \
        "spup-core/src/main/java/com/spup/core/service/IAppConfigService.java" \
        "spup-admin-web/src/main/java/com/spup/admin/service/IAppConfigService.java" \
        "import com.spup.core.service.IAppConfigService;"
fi

# IAppOperateLogService - remove from user and admin, keep core
if [ -f "spup-user-web/src/main/java/com/spup/user/service/IAppOperateLogService.java" ]; then
    remove_duplicate_class "IAppOperateLogService" \
        "spup-core/src/main/java/com/spup/core/service/IAppOperateLogService.java" \
        "spup-user-web/src/main/java/com/spup/user/service/IAppOperateLogService.java" \
        "import com.spup.core.service.IAppOperateLogService;"
fi

if [ -f "spup-admin-web/src/main/java/com/spup/admin/service/IAppOperateLogService.java" ]; then
    remove_duplicate_class "IAppOperateLogService" \
        "spup-core/src/main/java/com/spup/core/service/IAppOperateLogService.java" \
        "spup-admin-web/src/main/java/com/spup/admin/service/IAppOperateLogService.java" \
        "import com.spup.core.service.IAppOperateLogService;"
fi

# Test compilation after interface removals
if ! test_compilation; then
    print_error "Stopping due to compilation failure"
    exit 1
fi

# 4. Remove truly identical classes that serve no different purpose
print_header "REMOVING TRULY IDENTICAL CLASSES"

# Remove duplicate test classes
if [ -f "spup-common/src/test/java/com/spup/commons/jackson/JacksonLocalDateTimeTest.java" ]; then
    remove_duplicate_class "JacksonLocalDateTimeTest" \
        "spup-user-web/src/test/java/com/spup/user/jackson/JacksonLocalDateTimeTest.java" \
        "spup-common/src/test/java/com/spup/commons/jackson/JacksonLocalDateTimeTest.java" \
        "// Test moved to user module"
fi

# Remove duplicate AccessToken if they're truly identical
print_status "Analyzing AccessToken classes for removal..."
if diff -q "spup-user-web/src/main/java/com/spup/user/mpapi/AccessToken.java" \
         "spup-core/src/main/java/com/spup/core/ssbapi/AccessToken.java" > /dev/null 2>&1; then
    print_status "AccessToken classes are identical - consolidating to core"
    remove_duplicate_class "AccessToken" \
        "spup-core/src/main/java/com/spup/core/ssbapi/AccessToken.java" \
        "spup-user-web/src/main/java/com/spup/user/mpapi/AccessToken.java" \
        "import com.spup.core.ssbapi.AccessToken;"
else
    print_status "AccessToken classes are different - keeping both"
fi

# Test compilation after identical class removals
if ! test_compilation; then
    print_error "Stopping due to compilation failure"
    exit 1
fi

# 5. Clean up empty directories
print_header "CLEANING UP EMPTY DIRECTORIES"

find spup-user-web/src spup-admin-web/src -type d -empty -delete 2>/dev/null || true
print_success "Cleaned up empty directories"

# 6. Final compilation and summary
print_header "FINAL VALIDATION"

if test_compilation; then
    print_success "✅ All duplicate removals completed successfully!"
    
    # Count remaining duplicates
    local remaining_duplicates=$(find . -name "*.java" -not -path "./target/*" -not -path "./.git/*" | xargs basename -s .java | sort | uniq -d | wc -l)
    
    print_header "REMOVAL SUMMARY"
    print_status "Successfully removed duplicate classes:"
    print_status "- AvailableDayResponse (consolidated to commons)"
    print_status "- DateTimeUtils (consolidated to commons)"  
    print_status "- IAppConfigService (consolidated to core)"
    print_status "- IAppOperateLogService (consolidated to core)"
    print_status "- Various test classes"
    print_status ""
    print_status "Remaining duplicate class names: $remaining_duplicates"
    print_status "(These are intentionally different implementations)"
    print_status ""
    print_success "🎉 Duplicate class removal completed!"
    
else
    print_error "❌ Final compilation failed"
    exit 1
fi
