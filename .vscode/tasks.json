{"version": "2.0.0", "tasks": [{"type": "java (build)", "paths": ["${workspace}"], "isFullBuild": true, "group": "build", "problemMatcher": [], "label": "java (build): Build Workspace", "detail": "$(tools) Build all the Java projects in workspace."}, {"label": "🔥 Compile and Sync to Remote", "type": "shell", "command": "./scripts/auto-sync-remote.sh", "args": ["sync-once"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🔄 Start Auto-Sync Watcher", "type": "shell", "command": "./scripts/auto-sync-remote.sh", "args": ["start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "isBackground": true}]}