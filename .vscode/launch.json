{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "🔗 Remote: spup-admin (supec.douwifi.cn:8000)",
            "request": "attach",
            "hostName": "supec.douwifi.cn",
            "port": "8000",
            "projectName": "spup-admin",
            "timeout": 10000,
            "sourcePaths": [
                "/Users/<USER>/workspace/supup-app/app-spup-admin/src/main/java",
                "/Users/<USER>/workspace/supup-app/common/src/main/java"
            ]
        },
        {
            "type": "java",
            "name": "🔥 Remote: Hot Reload Debug (supec.douwifi.cn:8000)",
            "request": "attach",
            "hostName": "supec.douwifi.cn",
            "port": "8000",
            "projectName": "spup-admin",
            "timeout": 10000,
            "sourcePaths": [
                "/Users/<USER>/workspace/supup-app/app-spup-admin/src/main/java",
                "/Users/<USER>/workspace/supup-app/common/src/main/java"
            ]
        },
        {
            "type": "java",
            "name": "🚀 Launch: SpupAdmin Local",
            "request": "launch",
            "mainClass": "com.spup.SpupAdmin",
            "projectName": "spup-admin",
            "args": ["--server.port=8888"],
            "vmArgs": [
                "-Dspring.profiles.active=dev",
                "-Dspring.devtools.restart.enabled=false",
                "-Dspring.devtools.livereload.enabled=false",
                "-Dspring.config.location=classpath:/application.yml,classpath:/application-dev.yml"
            ],
            "cwd": "${workspaceFolder}/app-spup-admin",
            "env": {
                "SPRING_PROFILES_ACTIVE": "dev"
            },
            "console": "integratedTerminal",
            "stopOnEntry": false,
            "internalConsoleOptions": "openOnSessionStart"
        },
        {
            "type": "java",
            "name": "🐛 Debug: SpupAdmin with Hot Reload",
            "request": "launch",
            "mainClass": "com.spup.SpupAdmin",
            "projectName": "spup-admin",
            "args": ["--server.port=8888", "--debug"],
            "vmArgs": [
                "-Dspring.profiles.active=dev",
                "-Dspring.devtools.restart.enabled=true",
                "-Dspring.devtools.livereload.enabled=true",
            ],
            "cwd": "${workspaceFolder}/app-spup-admin",
            "env": {
                "SPRING_PROFILES_ACTIVE": "dev",
                "DEBUG": "true"
            },
            "console": "integratedTerminal",
            "stopOnEntry": false,
            "internalConsoleOptions": "openOnSessionStart"
        },
        {
            "type": "java",
            "name": "🔧 Debug: SpupAdmin with Custom Port",
            "request": "launch",
            "mainClass": "com.spup.SpupAdmin",
            "projectName": "spup-admin",
            "args": ["--server.port=9999"],
            "vmArgs": [
                "-Dspring.profiles.active=dev",
                "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005"
            ],
            "cwd": "${workspaceFolder}/app-spup-admin",
            "env": {
                "SPRING_PROFILES_ACTIVE": "dev"
            },
            "console": "integratedTerminal"
        },
        {
            "type": "java",
            "name": "🎯 Debug: SpupAdmin (No DevTools)",
            "request": "launch",
            "mainClass": "com.spup.SpupAdmin",
            "projectName": "spup-admin",
            "args": ["--server.port=8888"],
            "vmArgs": [
                "-Dspring.profiles.active=dev",
                "-Dspring.devtools.restart.enabled=false",
                "-Dspring.devtools.livereload.enabled=false",
                "-Dspring.boot.devtools.restart.enabled=false"
            ],
            "cwd": "${workspaceFolder}/app-spup-admin",
            "env": {
                "SPRING_PROFILES_ACTIVE": "dev"
            },
            "console": "integratedTerminal",
            "stopOnEntry": false,
            "internalConsoleOptions": "openOnSessionStart"
        },
        {
            "type": "java",
            "name": "🔗 Remote: spup-admin (IP:8000)",
            "request": "attach",
            "hostName": "*************",
            "port": "8000",
            "projectName": "spup-admin",
            "timeout": 10000,
            "sourcePaths": [
                "/Users/<USER>/workspace/supup-app/app-spup-admin/src/main/java",
                "/Users/<USER>/workspace/supup-app/common/src/main/java"
            ]
        },
        {
            "type": "java",
            "name": "🔗 Remote: spup-main (Port 8001)",
            "request": "attach",
            "hostName": "supec.douwifi.cn",
            "port": "8001",
            "projectName": "spup",
            "timeout": 10000,
            "sourcePaths": [
                "${workspaceFolder}/app-spup/src/main/java",
                "${workspaceFolder}/common/src/main/java"
            ]
        },
        {
            "type": "java",
            "name": "🚀 Local: SpupAdmin",
            "request": "launch",
            "mainClass": "com.spup.SpupAdmin",
            "projectName": "spup-admin",
            "cwd": "${workspaceFolder}/app-spup-admin",
            "args": ["--server.port=8888"],
            "vmArgs": ["-Dspring.profiles.active=dev"],
            "classPaths": ["${workspaceFolder}/app-spup-admin/target/classes", "${workspaceFolder}/common/target/classes"]
        },
        {
            "type": "java",
            "name": "🚀 Local: SpupApplication",
            "request": "launch",
            "mainClass": "com.spup.SpupApplication",
            "projectName": "spup",
            "cwd": "${workspaceFolder}/app-spup",
            "args": ["--server.port=8080"],
            "vmArgs": ["-Dspring.profiles.active=dev"],
            "classPaths": ["${workspaceFolder}/app-spup/target/classes", "${workspaceFolder}/common/target/classes"]
        }
    ]
}