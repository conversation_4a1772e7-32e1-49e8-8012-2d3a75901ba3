# SPUP Development Makefile
# Provides simple commands for common development tasks

.PHONY: help build quick install clean test core admin user activity all package deploy-tomcat deploy-dev deploy-prod status logs

# Default target
help:
	@echo "SPUP Development Commands:"
	@echo ""
	@echo "Quick Development:"
	@echo "  make quick     - Quick compile all modules (fastest)"
	@echo "  make build     - Build all modules with install"
	@echo "  make clean     - Clean all modules"
	@echo ""
	@echo "Module-specific:"
	@echo "  make core      - Build spup-core module"
	@echo "  make admin     - Build spup-admin-web module"
	@echo "  make user      - Build spup-user-web module"
	@echo "  make activity  - Build spup-activity module"
	@echo ""
	@echo "Testing:"
	@echo "  make test      - Run all tests"
	@echo "  make test-core - Test spup-core module"
	@echo "  make test-admin - Test spup-admin-web module"
	@echo "  make test-user - Test spup-user-web module"
	@echo ""
	@echo "Deployment:"
	@echo "  make package   - Package WAR files for deployment"
	@echo "  make deploy-tomcat - Deploy to local Tomcat"
	@echo "  make deploy-dev - Deploy to development environment"
	@echo "  make deploy-prod - Deploy to production environment"
	@echo ""
	@echo "Utilities:"
	@echo "  make deps      - Show dependency tree"
	@echo "  make status    - Show build status"
	@echo "  make logs      - Show Tomcat logs"

# Quick development build
quick:
	@echo "🚀 Quick build (compile only, no tests)..."
	@mvn compile -Pquick -q

# Full build with install
build:
	@echo "📦 Building all modules..."
	@mvn install -Pdev -q

# Install all modules
install:
	@echo "📦 Installing all modules..."
	@mvn install -DskipTests -q

# Clean all modules
clean:
	@echo "🧹 Cleaning all modules..."
	@mvn clean -q

# Test all modules
test:
	@echo "🧪 Running all tests..."
	@mvn test -q

# Core module
core:
	@echo "🔧 Building spup-core..."
	@mvn install -pl spup-core -DskipTests -q

# Admin module
admin:
	@echo "🌐 Building spup-admin-web..."
	@mvn package -pl spup-admin-web -am -DskipTests -q

# User module
user:
	@echo "👤 Building spup-user-web..."
	@mvn package -pl spup-user-web -am -DskipTests -q

# Activity module
activity:
	@echo "🎯 Building spup-activity..."
	@mvn compile -pl spup-activity -am -q

# Test specific modules
test-core:
	@echo "🧪 Testing spup-core..."
	@mvn test -pl spup-core -q

test-admin:
	@echo "🧪 Testing spup-admin-web..."
	@mvn test -pl spup-admin-web -q

test-user:
	@echo "🧪 Testing spup-user-web..."
	@mvn test -pl spup-user-web -q

# Utility commands
deps:
	@echo "📊 Dependency tree:"
	@mvn dependency:tree -q

status:
	@echo "📋 Build status:"
	@echo "Checking compilation..."
	@mvn compile -q && echo "✅ All modules compile successfully" || echo "❌ Compilation errors found"

# Development workflow shortcuts
dev-core:
	@echo "🔄 Core development workflow..."
	@make core && make admin && make user

dev-admin:
	@echo "🔄 Admin development workflow..."
	@make admin

dev-user:
	@echo "🔄 User development workflow..."
	@make user

# Package WAR files for deployment
package:
	@echo "📦 Packaging WAR files..."
	@mvn clean package -DskipTests -q
	@echo "✅ WAR files generated:"
	@ls -la spup-admin-web/target/*.war spup-user-web/target/*.war 2>/dev/null || echo "❌ WAR files not found"

# Deploy to local Tomcat
deploy-tomcat:
	@echo "🚀 Deploying to local Tomcat..."
	@if [ -f "scripts/deploy-tomcat.sh" ]; then \
		chmod +x scripts/deploy-tomcat.sh; \
		./scripts/deploy-tomcat.sh; \
	else \
		echo "❌ Deploy script not found. Creating basic deployment..."; \
		make package; \
		echo "Please copy WAR files to Tomcat webapps directory:"; \
		echo "  cp spup-admin-web/target/spup-admin.war $$TOMCAT_HOME/webapps/"; \
		echo "  cp spup-user-web/target/spup.war $$TOMCAT_HOME/webapps/"; \
	fi

# Deploy to development environment
deploy-dev:
	@echo "🚀 Deploying to development environment..."
	@if [ -f "scripts/deploy-tomcat.sh" ]; then \
		chmod +x scripts/deploy-tomcat.sh; \
		./scripts/deploy-tomcat.sh dev; \
	else \
		echo "❌ Deploy script not found"; \
		make package; \
	fi

# Deploy to production environment
deploy-prod:
	@echo "🚀 Deploying to production environment..."
	@echo "⚠️  WARNING: This will deploy to PRODUCTION!"
	@read -p "Type 'DEPLOY' to confirm: " confirm; \
	if [ "$$confirm" = "DEPLOY" ]; then \
		if [ -f "scripts/deploy-tomcat.sh" ]; then \
			chmod +x scripts/deploy-tomcat.sh; \
			./scripts/deploy-tomcat.sh prod; \
		else \
			echo "❌ Deploy script not found"; \
		fi; \
	else \
		echo "❌ Deployment cancelled"; \
	fi

# Check deployment status
status:
	@echo "📋 Checking deployment status..."
	@echo "Checking if applications are running..."
	@curl -s -I http://localhost:8080/spup-admin > /dev/null 2>&1 && echo "✅ Admin app is running" || echo "❌ Admin app is not accessible"
	@curl -s -I http://localhost:8080/spup > /dev/null 2>&1 && echo "✅ User app is running" || echo "❌ User app is not accessible"

# Show Tomcat logs
logs:
	@echo "📋 Showing Tomcat logs..."
	@if [ -f "/opt/tomcat/logs/catalina.out" ]; then \
		tail -f /opt/tomcat/logs/catalina.out; \
	elif [ -f "$$TOMCAT_HOME/logs/catalina.out" ]; then \
		tail -f $$TOMCAT_HOME/logs/catalina.out; \
	else \
		echo "❌ Tomcat log file not found"; \
		echo "Please set TOMCAT_HOME environment variable or check Tomcat installation"; \
	fi

# Full development cycle
all: clean build test
	@echo "✅ Full build cycle completed"

# Complete deployment workflow
deploy: package deploy-tomcat status
	@echo "🎉 Deployment workflow completed!"
