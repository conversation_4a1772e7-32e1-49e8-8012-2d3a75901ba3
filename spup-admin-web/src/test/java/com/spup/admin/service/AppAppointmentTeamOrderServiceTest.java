package com.spup.admin.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Collections;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.spup.admin.service.appointment.impl.AppAppointmentTeamOrderServiceImpl;
import com.spup.core.dto.AppAdminTeamOrderListRequest;
import com.spup.data.dao.appointment.AppAppointmentTeamOrderDao;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;

/**
 * Test for AppAppointmentTeamOrderService to verify it handles empty results correctly
 * Uses Mockito to avoid loading full Spring context
 */
@ExtendWith(MockitoExtension.class)
public class AppAppointmentTeamOrderServiceTest {

    @Mock
    private AppAppointmentTeamOrderDao teamOrderDao;

    @InjectMocks
    private AppAppointmentTeamOrderServiceImpl teamOrderService;

    @Test
    public void testGetListWithNoRecords() {
        // Test the exact scenario from your question
        AppAdminTeamOrderListRequest queryParam = new AppAdminTeamOrderListRequest();
        queryParam.setExhibitionNo("1");
        queryParam.setTeamName(null);
        queryParam.setStartDate(null);
        queryParam.setEndDate(null);
        queryParam.setOrderStatus(null);
        queryParam.setMethod(null);
        queryParam.setPageNum(1);
        queryParam.setPageSize(10);

        // Mock the DAO to return an empty page
        Page<AppAppointmentTeamOrder> emptyPage = new PageImpl<>(
            Collections.emptyList(),
            PageRequest.of(0, 10),
            0
        );
        when(teamOrderDao.findAll(any(Specification.class), any(Pageable.class)))
            .thenReturn(emptyPage);

        // This should NOT throw an exception
        assertDoesNotThrow(() -> {
            Page<AppAppointmentTeamOrder> result = teamOrderService.getAdminTeamOrderList(queryParam);

            // Verify the result is a valid empty page
            assertNotNull(result, "Result should not be null");
            assertTrue(result.getContent().isEmpty(), "Content should be empty when no records exist");
            assertEquals(0, result.getTotalElements(), "Total elements should be 0");
            assertEquals(0, result.getNumberOfElements(), "Number of elements should be 0");
            assertEquals(0, result.getTotalPages(), "Total pages should be 0 for empty result");
            assertEquals(0, result.getNumber(), "Page number should be 0 (0-based)");

            System.out.println("✅ Test passed: No exception thrown with empty database");
            System.out.println("Result: " + result);
        });
    }

    @Test
    public void testGetListWithAllNullParameters() {
        // Test with all parameters null
        AppAdminTeamOrderListRequest queryParam = new AppAdminTeamOrderListRequest();
        queryParam.setPageNum(1);
        queryParam.setPageSize(10);

        // Mock the DAO to return an empty page
        Page<AppAppointmentTeamOrder> emptyPage = new PageImpl<>(
            Collections.emptyList(),
            PageRequest.of(0, 10),
            0
        );
        when(teamOrderDao.findAll(any(Specification.class), any(Pageable.class)))
            .thenReturn(emptyPage);

        // This should also NOT throw an exception
        assertDoesNotThrow(() -> {
            Page<AppAppointmentTeamOrder> result = teamOrderService.getAdminTeamOrderList(queryParam);

            assertNotNull(result, "Result should not be null even with all null parameters");
            System.out.println("✅ Test passed: No exception with all null parameters");
        });
    }

    @Test
    public void testGetListWithInvalidExhibitionNo() {
        // Test with non-existent exhibition number
        AppAdminTeamOrderListRequest queryParam = new AppAdminTeamOrderListRequest();
        queryParam.setExhibitionNo("999"); // Non-existent exhibition
        queryParam.setPageNum(1);
        queryParam.setPageSize(10);

        // Mock the DAO to return an empty page for invalid exhibition
        Page<AppAppointmentTeamOrder> emptyPage = new PageImpl<>(
            Collections.emptyList(),
            PageRequest.of(0, 10),
            0
        );
        when(teamOrderDao.findAll(any(Specification.class), any(Pageable.class)))
            .thenReturn(emptyPage);

        assertDoesNotThrow(() -> {
            Page<AppAppointmentTeamOrder> result = teamOrderService.getAdminTeamOrderList(queryParam);

            assertNotNull(result, "Result should not be null for non-existent exhibition");
            assertTrue(result.getContent().isEmpty(), "Should return empty result for non-existent exhibition");
            System.out.println("✅ Test passed: No exception with non-existent exhibition");
        });
    }
}
