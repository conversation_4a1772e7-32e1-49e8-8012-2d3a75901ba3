package com.spup.admin.service.analysis.impl;

import com.spup.data.dao.mp.MpDatacubeArticleSummaryDao;
import com.spup.data.entity.mp.MpDatacubeArticleSummary;
import com.spup.admin.dto.DateQueryRequest;
import com.spup.data.entity.Article;
import com.spup.data.entity.ArticleDailyDetail;
import com.spup.admin.service.analysis.IMpDatacubeArticleSummaryService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MpDatacubeArticleSummaryServiceImpl implements IMpDatacubeArticleSummaryService {
    @Resource
    private MpDatacubeArticleSummaryDao articleSummaryDao;

    public Long getArticleTotal(){
        return articleSummaryDao.count();
    }

    @Override
    public Long getArticleTotal(LocalDate start, LocalDate end) {
        return articleSummaryDao.countBySendDateBetween(start,end);
    }

    @Override
    public Long getArticleReadTotal() {
        List<MpDatacubeArticleSummary> allArticleList = articleSummaryDao.findAll();
        int sum = allArticleList.stream().mapToInt(MpDatacubeArticleSummary::getTotalReadCount).sum();
        return (long)sum;
    }

    @Override
    public Long getArticleReadTotal(LocalDate start, LocalDate end) {
        List<MpDatacubeArticleSummary> allArticleList = articleSummaryDao.findBySendDateBetween(start,end);
        int sum = allArticleList.stream().mapToInt(MpDatacubeArticleSummary::getTotalReadCount).sum();
        return (long)sum;
    }

    @Override
    public Page<MpDatacubeArticleSummary> getListByPage(DateQueryRequest queryParam) {
        Pageable pageable = PageRequest.of(queryParam.getPageNum()-1, queryParam.getPageSize());
        Specification<MpDatacubeArticleSummary> spec = new Specification<MpDatacubeArticleSummary>() {
            @Override
            public Predicate toPredicate(@NonNull Root<MpDatacubeArticleSummary> root, @NonNull CriteriaQuery<?> query, @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if(queryParam.getStartDate()!=null){
                    list.add(cb.greaterThanOrEqualTo(root.get("sendDate"),queryParam.getStartDate()));//筛选
                }
                if(queryParam.getEndDate()!=null){
                    list.add(cb.lessThanOrEqualTo(root.get("sendDate"),queryParam.getEndDate()));//筛选
                }
                query.orderBy(cb.desc(root.get("sendDate")));//排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };
        Page<MpDatacubeArticleSummary> page = articleSummaryDao.findAll(spec, pageable);
        return page;
    }

    @Override
    public void saveList(List<Article> newArticleList) {
        List<MpDatacubeArticleSummary> result =
                newArticleList.stream().map(article -> {
                    MpDatacubeArticleSummary articleSummary = new MpDatacubeArticleSummary();
                    BeanUtils.copyProperties(article, articleSummary);
                    return articleSummary;
                }).collect(Collectors.toList());

        articleSummaryDao.saveAll(result);
    }

    @Override
    public void updateList(List<ArticleDailyDetail> dailyDetailList) {
        List<MpDatacubeArticleSummary> result =
                        dailyDetailList.stream().map(articleDailyDetail -> {
                            Optional<MpDatacubeArticleSummary> articleSummaryOptional = articleSummaryDao.getByMsgId(articleDailyDetail.getMsgId());
                            if(!articleSummaryOptional.isPresent()){
                                return null;
                            }
                            MpDatacubeArticleSummary articleSummary = articleSummaryOptional.get();
                            plus(articleSummary,articleDailyDetail);
                            articleSummary.setAnalysisDate(LocalDate.parse(articleDailyDetail.getRefDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                            return articleSummary;
                        })
                        .filter(articleSummary -> articleSummary!=null)
                        .collect(Collectors.toList());

        articleSummaryDao.saveAll(result);
    }

    public void plus(MpDatacubeArticleSummary articleSummary,ArticleDailyDetail detail){
        articleSummary.setTotalReadCount(articleSummary.getTotalReadCount()+detail.getIntPageReadCount());
        articleSummary.setTotalReadCount(articleSummary.getTotalReadCount()+detail.getOriPageReadCount());
        articleSummary.setTotalReadUser(articleSummary.getTotalReadUser()+detail.getIntPageReadUser());
        articleSummary.setTotalReadUser(articleSummary.getTotalReadUser()+detail.getOriPageReadUser());
        articleSummary.setAddToFavUser(articleSummary.getAddToFavUser()+detail.getAddToFavUser());
        articleSummary.setAddToFavCount(articleSummary.getAddToFavCount()+detail.getAddToFavCount());
        articleSummary.setTotalShareCount(articleSummary.getTotalShareCount()+detail.getShareCount());
        articleSummary.setTotalShareUser(articleSummary.getTotalShareUser()+detail.getShareUser());
    }
}
