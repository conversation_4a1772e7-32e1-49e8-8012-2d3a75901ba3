package com.spup.admin.service;

import org.springframework.data.domain.Page;

import com.spup.core.dto.GoodsListRequest;
import com.spup.core.service.BaseSurroundingGoodsService;
import com.spup.data.entity.AppSurroundingGoods;

/**
 * Admin Surrounding Goods Service Interface
 * Extends BaseSurroundingGoodsService with admin-specific operations
 */
public interface IAppSurroundingGoodsService extends BaseSurroundingGoodsService {

    /**
     * Get paginated goods list (admin-specific)
     */
    Page<AppSurroundingGoods> getListByPage(GoodsListRequest listParam);
}
