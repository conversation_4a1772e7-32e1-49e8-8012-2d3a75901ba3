// package com.spup.admin.service.appointment;



// import java.util.List;
// import java.util.Map;

// import com.spup.data.entity.appointment.AppBatch;

// public interface IAppBatchService {
//     AppBatch getByNo(String batchNo, Byte batchCategory);
//     Map<String,List<AppBatch>> getListByDate(Byte category, String startDate, String endDate);

//     List<AppBatch> getListByDate(Byte category, String date);
//     AppBatch save(AppBatch batch);
//     AppBatch update(AppBatch batch);
//     AppBatch updateRemaining(String batchNo,Byte batchCategory,int updateNum);

// }
