// package com.spup.admin.service.appointment;

// import com.spup.data.entity.appointment.AppWorkday;

// import java.time.LocalDate;
// import java.util.List;

// public interface IAppWorkdayService {
//     List<AppWorkday> getListByDate(String startDate, String endDate);
//     boolean isWorkDay(String day);
//     int insert(LocalDate day);
//     AppWorkday setWorkDay(String day,Integer status,String remark,String unionid);
//     AppWorkday setWorkDayOfTemp(String exhibitionNo,String day,Integer status,String remark,String unionid);
//     LocalDate getDate(LocalDate date,int plusDays,byte dayType);
//     List<AppWorkday> getListByDateOfTemp(String exhibitionNo, String startDate, String endDate);

// }
