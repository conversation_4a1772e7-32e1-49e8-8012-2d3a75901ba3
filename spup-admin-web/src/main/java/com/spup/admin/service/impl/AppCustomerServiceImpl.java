package com.spup.admin.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.spup.core.dto.CustomerListRequest;
import com.spup.core.service.IAppCustomerService;
import com.spup.core.service.impl.BaseCustomerServiceImpl;
import com.spup.data.entity.authority.AppCustomer;

/**
 * Admin Customer Service Implementation
 * Extends BaseCustomerServiceImpl with admin-specific operations
 */
@Service
public class AppCustomerServiceImpl extends BaseCustomerServiceImpl implements IAppCustomerService {

    /**
     * Admin-specific: Set additional customer fields during full save
     */
    @Override
    protected void setAdditionalCustomerFields(AppCustomer customer, String unionid, String openid,
                                             String userName, String avatar, byte gender) {
        // Set admin-specific fields
        customer.setUserGender(gender);
        customer.setUserAvatarSrc(avatar);
        customer.setUserName(userName);
    }

    @Override
    public Page<AppCustomer> getPageList(CustomerListRequest listParam) {
        //调用分页插件
        Pageable pageable = PageRequest.of(listParam.getPageNum()-1, listParam.getPageSize());
        //从数据库查询
        Specification<AppCustomer> spec = new Specification<AppCustomer>() {
            //Predicate:封装了 单个的查询条件
            /**
             * Root<Users> root:查询对象的属性的封装。
             * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select  from order by
             * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
             */
            @Override
            public Predicate toPredicate(@NonNull Root<AppCustomer> root, @NonNull CriteriaQuery<?> query, @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if(StringUtils.hasLength(listParam.getUserName())){
                    list.add(cb.like(root.get("userName"),"%"+listParam.getUserName()+"%"));//筛选
                }
                if(StringUtils.hasLength(listParam.getRealName())){
                    list.add(cb.like(root.get("realName"),"%"+listParam.getRealName()+"%"));//筛选
                }

                if(StringUtils.hasLength(listParam.getPhone())){
                    list.add(cb.like(root.get("phone"),"%"+listParam.getPhone()+"%"));//筛选
                }
                if(StringUtils.hasLength(listParam.getCardNo())){
                    list.add(cb.equal(root.get("cardNo"),listParam.getCardNo()));//筛选
                }
                query.orderBy(cb.desc(root.get("id")));//排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };

        Page<AppCustomer> all = appCustomerDao.findAll(spec, pageable);

        return all;
    }
}
