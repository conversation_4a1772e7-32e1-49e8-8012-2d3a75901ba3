package com.spup.admin.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.spup.core.service.IAppCustomerContactsService;
import com.spup.data.dao.appointment.AppAppointmentSuborderDao;
import com.spup.data.dao.authority.AppCustomerContactsDao;
import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.data.entity.authority.AppCustomerContacts;
import com.spup.enums.OrderStatusEnum;

@Service
public class AppCustomerContactsServiceImpl implements IAppCustomerContactsService {
    @Resource
    private AppCustomerContactsDao appCustomerContactsDao;
    @Resource
    private AppAppointmentSuborderDao appAppointmentSuborderDao;

    @Override
    public AppCustomerContacts save(AppCustomerContacts contacts) {
        return appCustomerContactsDao.save(contacts);
    }

    @Override
    public AppCustomerContacts modify(AppCustomerContacts contacts) {
        return appCustomerContactsDao.save(contacts);
    }

    @Override
    public int delete(Long contactId) {
        appCustomerContactsDao.deleteById(contactId);
        return 1;
    }


    @Override
    public List<AppCustomerContacts> getListByOwner(String ownerUnionid) {
        List<AppCustomerContacts> byOwerUnionid = appCustomerContactsDao.findByOwerUnionid(ownerUnionid);
        byOwerUnionid.sort((o1,o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
        return byOwerUnionid;
    }

    @Override
    public List<AppCustomerContacts> getListByOwner(String yyyyMMdd, String ownerUnionid) {
        List<AppAppointmentSuborder> subordersByUnionid = appAppointmentSuborderDao.findByOnwerUnionid(ownerUnionid);
        List<AppAppointmentSuborder> todaySuborders =
                subordersByUnionid.stream()
                        .filter(suborder -> suborder.getBatchDate().equals(yyyyMMdd))
                        .filter(suborder -> suborder.getSuborderStatus().shortValue() == OrderStatusEnum.FINISHED.getCode())
                        .collect(Collectors.toList());

        List<AppCustomerContacts> avalidContacts = new ArrayList<>();

        for (int i = 0; i < todaySuborders.size(); i++) {
            AppAppointmentSuborder suborder = todaySuborders.get(i);
            AppCustomerContacts contact = new AppCustomerContacts();
            contact.setId((long)(i+1));
            contact.setName(suborder.getContactsName());
            contact.setPhone(suborder.getContactsPhone());
            contact.setIdcardCategory(suborder.getContactsIdcardCategory());
            contact.setIdcardNo(suborder.getContactsIdcardNo());
            contact.setRemark("");
            avalidContacts.add(contact);
        }
        return avalidContacts;
    }

    @Override
    public Optional<AppCustomerContacts> findById(Long contactsId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'findById'");
    }
}
