package com.spup.admin.service.appointment;

import org.springframework.data.domain.Page;

import com.spup.core.dto.DateQueryRequest;
import com.spup.data.entity.appointment.AppAppointmentTeamOffline;

public interface IAppAppointmentTeamOfflineService {
    Page<AppAppointmentTeamOffline> getList(DateQueryRequest queryParam);
    AppAppointmentTeamOffline save(AppAppointmentTeamOffline offline);
    AppAppointmentTeamOffline view(Long id);
    int delete(Long id);
}
