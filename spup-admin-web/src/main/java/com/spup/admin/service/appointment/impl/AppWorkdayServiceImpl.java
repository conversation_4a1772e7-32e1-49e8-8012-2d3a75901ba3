// package com.spup.admin.service.appointment.impl;

// import java.time.DayOfWeek;
// import java.time.LocalDate;
// import java.time.format.DateTimeFormatter;
// import java.time.temporal.ChronoUnit;
// import java.util.List;
// import java.util.Optional;
// import java.util.stream.Collectors;

// import javax.annotation.Resource;

// import org.springframework.stereotype.Service;
// import org.springframework.util.StringUtils;

// import com.spup.admin.service.IAppointmentService;
// import com.spup.admin.service.appointment.IAppWorkdayService;
// import com.spup.data.dao.RoundDao;
// import com.spup.data.dao.appointment.AppBatchDao;
// // import com.spup.data.dao.appointment.AppWorkdayDao;
// import com.spup.data.entity.RoundConfig;
// // import com.spup.data.entity.appointment.AppWorkday;
// import com.spup.enums.WorkdayEnum;

// import lombok.extern.slf4j.Slf4j;

// /**
//  * Service implementation for managing appointment workdays.
//  * Provides functionality to:
//  * - Retrieve workdays within a date range
//  * - Check if a specific date is a workday
//  * - Insert new workday records
//  * - Update workday status and remarks
//  * - Manage temporary exhibition workday settings
//  * - Calculate dates based on workday type
//  * 
//  * Handles business logic for workday operations including:
//  * - Setting appropriate remarks for open/closed days
//  * - Managing round configurations for exhibitions
//  * - Coordinating with appointment service for availability checks
//  * 
//  * Uses DAOs for data access: AppWorkdayDao, AppBatchDao, RoundDao
//  * Collaborates with IAppointmentService for holiday/availability checks
//  */
// @Slf4j
// @Service
// public class AppWorkdayServiceImpl implements IAppWorkdayService {

//     private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
//     private static final String DI_TU_ZHAN_NO = "";
//     private static final String GUO_TU_ZHAN = "L20240716T20241231";

//     // @Resource
//     // private AppWorkdayDao appWorkdayDao;
//     @Resource
//     private AppBatchDao appBatchDao;
//     @Resource
//     private RoundDao roundDao;

//     @Resource
//     private IAppointmentService iAppointmentService;

//     @Override
//     public List<AppWorkday> getListByDate(String startDate, String endDate) {
//         return findByDayBetween(startDate, endDate);
//     }

//     /**
//      * Finds workdays between given date range and processes them.
//      * - Queries workdays from DAO between start and end dates
//      * - For each workday:
//      * - Sets empty remark if it's an open day
//      * - Sets "馆休" remark if it's a closed day
//      * - Returns the processed workday list
//      * 
//      * @param start Start date in string format
//      * @param end   End date in string format
//      * @return List of processed AppWorkday objects
//      *         handle ‘节假日’和已设置的特殊日期。
//      */
//     private List<AppWorkday> findByDayBetween(String start, String end) {
//         List<AppWorkday> workdayList = appWorkdayDao.findByDayBetween(start, end);
//         // String remarkStr = exhibitionNo.equals("person") ? "馆休" : "";

//         // if workdayList has empty date from start to end, then create a new workday of
//         // the date and append to the list, isWorkday set to 1, remark set to "", config
//         // set to {}
//         // DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
//         LocalDate startDateObj = LocalDate.parse(start, formatter);
//         LocalDate endDateObj = LocalDate.parse(end, formatter);

//         // Collect all existing days for quick lookup
//         java.util.Set<String> existingDays = new java.util.HashSet<>();
//         for (AppWorkday workday : workdayList) {
//             existingDays.add(workday.getDay());
//         }

//         // Fill missing dates
//         for (LocalDate date = startDateObj; !date.isAfter(endDateObj); date = date.plusDays(1)) {
//             String dateStr = date.format(formatter);
//             if (!existingDays.contains(dateStr)) {
//                 AppWorkday newWorkday = new AppWorkday();
//                 newWorkday.setDay(dateStr);
//                 newWorkday.setIsWorkday((int) WorkdayEnum.OPEN_DAY.getCode());
//                 newWorkday.setDayRemark("");
//                 // If AppWorkday has a setConfig method and config field, set it to an empty
//                 // object if needed
//                 // newWorkday.setConfig(new ObjectNode(JsonNodeFactory.instance));
//                 workdayList.add(newWorkday);
//             }
//         }

//         // Set remarks for all workdays
//         for (AppWorkday workday : workdayList) {
//             if (workday.getIsWorkday().byteValue() == WorkdayEnum.OPEN_DAY.getCode()) {
//                 workday.setDayRemark("");
//             } else {
//                 workday.setDayRemark("馆休");
//             }
//         }
//         workdayList.sort((o1, o2) -> o1.getDay().compareTo(o2.getDay()));
//         return workdayList;
//     }

//     @Override
//     public boolean isWorkDay(String day) {
//         Optional<AppWorkday> workdayOptional = appWorkdayDao.getByDay(day);
//         if (!workdayOptional.isPresent()) {
//             return false;
//         }
//         AppWorkday workday = workdayOptional.get();
//         boolean isWorkDay = workday.getIsWorkday().byteValue() == WorkdayEnum.OPEN_DAY.getCode();
//         return isWorkDay;
//     }

//     @Override
//     public int insert(LocalDate day) {
//         DayOfWeek week = day.getDayOfWeek();
//         AppWorkday workday = new AppWorkday();
//         workday.setIsWorkday(week.getValue() > 6 ? 0 : 1);
//         workday.setDay(day.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
//         workday.setDayRemark(week.getValue() > 6 ? "馆休" : "");
//         appWorkdayDao.save(workday);
//         return 0;
//     }

//     @Override
//     public AppWorkday setWorkDay(String day, Integer status, String remark, String unionid) {
//         Optional<AppWorkday> dayOpt = appWorkdayDao.getByDay(day);
//         if (dayOpt.isPresent()) {
//             AppWorkday workday = dayOpt.get();
//             if (status.byteValue() == WorkdayEnum.OPEN_DAY.getCode()) {
//                 workday.setDayRemark("");
//             } else {
//                 if (!StringUtils.hasLength(remark)) {
//                     LocalDate date = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyyMMdd"));
//                     Boolean availableByHoliday = iAppointmentService.isAvailableByHoliday("all", date);
//                     if (!availableByHoliday) {
//                         workday.setDayRemark("闭馆");
//                     } else {
//                         workday.setDayRemark("已约满");
//                     }
//                 } else {
//                     workday.setDayRemark(remark);
//                 }
//             }
//             // 除了设置workday，还需要配置团队通道和门票两个
//             saveOrUpdateRoundConfig("ticket", day, status);

//             saveOrUpdateRoundConfig("team", day, status);

//             workday.setIsWorkday(status);
//             return appWorkdayDao.save(workday);
//         }
//         return null;
//     }

//     private RoundConfig saveOrUpdateRoundConfig(String exhibitionId, String roundDateStr, int status) {
//         LocalDate roundDate = LocalDate.parse(roundDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
//         Optional<RoundConfig> configOpt = roundDao.findByExhibitionIdAndRoundDate(exhibitionId, roundDate);
//         RoundConfig config;
//         if (!configOpt.isPresent()) {
//             config = new RoundConfig();
//             config.setExhibitionId(exhibitionId);
//             config.setRoundDate(roundDate);
//             config.setDeleted(0);
//         } else {
//             config = configOpt.get();
//         }
//         config.setRoundStatus(status == 0 ? RoundConfig.RoundStatusEnum.close : RoundConfig.RoundStatusEnum.open);
//         if ("team".equals(exhibitionId)) { // 如果是团队预约，开放场馆时，需删除数据库配置，采用yml配置
//             config.setDeleted(status);
//         }
//         return roundDao.save(config);
//     }

//     /*
//      * @Override
//      * public AppWorkday setWorkDayOfTemp(String exhibitionNo,String day, Integer
//      * status, String remark, String unionid) {
//      * Optional<AppWorkday> dayOpt = appWorkdayDao.getByDay(day);
//      * if(dayOpt.isPresent()){
//      * AppWorkday workday = dayOpt.get();
//      * ObjectNode config = workday.getConfig();
//      * config.put(exhibitionNo,status);
//      * workday.setConfig(config);
//      * 
//      * List<AppBatch> batchList =
//      * appBatchDao.findByBatchCategoryAndBatchDateBetween(BatchCategoryEnum.
//      * EXHIBITION_TEAM.getCode(), day, day);
//      * batchList.forEach(batch -> {
//      * batch.setBatchStatus(status.byteValue());
//      * appBatchDao.save(batch);
//      * });
//      * return appWorkdayDao.save(workday);
//      * } else {
//      * throw new RuntimeException("未开启该日期");
//      * }
//      * }
//      */
//     @Override
//     public AppWorkday setWorkDayOfTemp(String exhibitionNo, String day, Integer status, String remark, String unionid) {
//         Optional<AppWorkday> dayOpt = appWorkdayDao.getByDay(day);
//         AppWorkday targetWorkDay = dayOpt.isPresent() ? dayOpt.get() : new AppWorkday();
//         // if(dayOpt.isPresent()){
//         // AppWorkday workday = dayOpt.get();
//         LocalDate date = LocalDate.parse(day, formatter);

//         Optional<RoundConfig> configOpt = roundDao.findByExhibitionIdAndRoundDate(exhibitionNo, date);
//         RoundConfig config;
//         if (!configOpt.isPresent()) {
//             config = new RoundConfig();
//             config.setExhibitionId(exhibitionNo);
//             config.setRoundDate(date);
//         } else {
//             config = configOpt.get();
//         }
//         config.setRoundStatus(status == 0 ? RoundConfig.RoundStatusEnum.close : RoundConfig.RoundStatusEnum.open);
//         roundDao.save(config);

//         targetWorkDay.setIsWorkday(status);
//         appWorkdayDao.save(targetWorkDay);
//         return targetWorkDay;
//         // } else {
//         // throw new RuntimeException("未开启该日期");
//         // }
//     }

//     @Override
//     public LocalDate getDate(LocalDate date, int plusDays, byte dayType) {
//         int hasPlusDays = 0;
//         while (hasPlusDays < plusDays) {
//             String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//             Optional<AppWorkday> workdayOptional = appWorkdayDao.getByDay(yyyyMMdd);
//             AppWorkday byDate = workdayOptional.get();
//             if (byDate.getIsWorkday().byteValue() == dayType) {
//                 hasPlusDays++;
//             }
//             date = date.plus(1, ChronoUnit.DAYS);
//         }

//         return date;
//     }

//     /*
//      * @Override
//      * public List<AppWorkday> getListByDateOfTemp(String exhibitionNo,String
//      * startDate, String endDate) {
//      * List<AppWorkday> dayList = appWorkdayDao.findByDayBetween(startDate,
//      * endDate);
//      * dayList.stream().forEach(day -> {
//      * ObjectNode config = day.getConfig();
//      * JsonNode statusNode = config.get(exhibitionNo);
//      * if(statusNode == null){
//      * day.setIsWorkday(0);
//      * } else {
//      * Integer status = statusNode.intValue();
//      * day.setIsWorkday(status);
//      * }
//      * 
//      * });
//      * return dayList;
//      * }
//      */

//     @Override
//     public List<AppWorkday> getListByDateOfTemp(String exhibitionNo, String startDateStr, String endDateStr) {
//         log.info("entering getListByDateOfTemp -> ");
//         List<AppWorkday> dayList = findByDayBetween(startDateStr, endDateStr);
//         LocalDate startDate = LocalDate.parse(startDateStr, formatter);
//         LocalDate endDate = LocalDate.parse(endDateStr, formatter);

//         List<AppWorkday> filteredDayList = dayList.stream()
//                 .map(workday -> {
//                     if (workDayPredicate.test(workday.getDay(), DayOfWeek.MONDAY)){
//                         workday.setIsWorkday(0);
//                         workday.setDayRemark("馆休");
//                     } 
//                     return workday; 
//                 })
//                 .collect(Collectors.toList());
        
//         List<AppWorkday> result = null;
//         if(exhibitionNo.equals(GUO_TU_ZHAN)) {
//             result = filteredDayList.stream()
//                 .map(workday -> {
//                     if (ifAvailableOpenWeekDay(GUO_TU_ZHAN, workday.getDay())){
//                         workday.setIsWorkday(0);
//                         workday.setDayRemark("业务不开放");
//                     } 
//                     return workday; 
//                 })
//                 .collect(Collectors.toList());
//         } else {
//             result = filteredDayList;
//         }
        
        
//         List<RoundConfig> configList = roundDao.findByexhibitionIdAndRoundDateBetween(exhibitionNo, startDate, endDate);
//         for (RoundConfig config: configList) {
//             String roundDateStr = config.getRoundDate().format(formatter);
//             for (AppWorkday workday : result) {
//                 if (workday.getDay().equals(roundDateStr)) {
//                     workday.setIsWorkday(config.getRoundStatus()==RoundConfig.RoundStatusEnum.open ? 1: 0);
//                 }
//                 if(workDayPredicate.test(workday.getDay(), DayOfWeek.MONDAY) && workday.getIsWorkday() == 1) {
//                     workday.setDayRemark("特殊开放");
//                 }
//                 if(ifAvailableOpenWeekDay(exhibitionNo, workday.getDay()) && workday.getIsWorkday() == 1) {
//                     workday.setDayRemark("特殊开放");
//                 }
//             }
//         }

//         // for (int i = 0; i < dayList.size(); i++) {
//         //     AppWorkday workDay = dayList.get(i);
//         //     String day = workDay.getDay();
//             // workDay.setIsWorkday(1);
//             // LocalDate dateOfDay = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyyMMdd"));
//             // 第一步，判断日期是否在此展会时间内，不在范围内的，全部关闭

//             // 第二步，判断日期是否已经设置过规则，设置过规则的，按规则处理
//             // log.info("exhibitionNo:" + exhibitionNo);
//             // log.info("dateOfDay:" + dateOfDay);
//             // Boolean availableByRound = iAppointmentService.isAvailableByRound(exhibitionNo, dateOfDay);
//             // log.info("availableByRound:" + availableByRound);
//             // if (availableByRound != null) {
//             //     if (!availableByRound) {
//             //         workDay.setIsWorkday(0);
//             //     } else {
//             //         workDay.setIsWorkday(1);
//             //     }
//             //     continue;
//             // }
//             // 第三步，判断日期是节假日, these is get from properties, and will be deprecated
//             // Boolean availableByHoliday = iAppointmentService.isAvailableByHoliday(exhibitionNo, dateOfDay);
//             // if (!availableByHoliday) {
//             //     workDay.setIsWorkday(0);
//             // }
//         // }

//         return filteredDayList;
//     }


//     private Boolean ifAvailableOpenWeekDay(String exhibitionNo, String dateStr) {
//         if(exhibitionNo.equals(GUO_TU_ZHAN)) {
//             return workDayPredicate.test(dateStr, DayOfWeek.SUNDAY) 
//                         || workDayPredicate.test(dateStr, DayOfWeek.SATURDAY)
//                         || workDayPredicate.test(dateStr, DayOfWeek.FRIDAY)
//                         || workDayPredicate.test(dateStr, DayOfWeek.THURSDAY);
//         }
//         return false;
//     }   

//     private java.util.function.BiPredicate<String, DayOfWeek> workDayPredicate = (dateStr, dayOfWeek) -> {
//         if (dayOfWeek.getValue() > 7) {
//             return false;
//         }
//         LocalDate date = LocalDate.parse(dateStr, formatter);
//         return date.getDayOfWeek() == dayOfWeek;
//     };
// }
