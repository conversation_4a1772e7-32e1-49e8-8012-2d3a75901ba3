package com.spup.admin.service.appointment.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.spup.admin.service.appointment.IAppAppointmentItemSuborderService;
import com.spup.data.dao.appointment.AppAppointmentItemSuborderDao;
import com.spup.data.entity.appointment.AppAppointmentItemSuborder;

@Service
public class AppAppointmentItemSuborderServiceImpl implements IAppAppointmentItemSuborderService {
    @Resource
    private AppAppointmentItemSuborderDao suborderDao;
    @Override
    public long countByStatus(Short suborderStatus) {
        return suborderDao.countBySuborderStatus(suborderStatus);
    }
    @Override
    public long countByStatus(Short suborderStatus,LocalDate start,LocalDate end) {
        return suborderDao.countBySuborderStatusAndBatchDateBetween(suborderStatus,
                                        start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                                        end.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }
    @Override
    public List<AppAppointmentItemSuborder> findByDateBetween(LocalDate startDate, LocalDate endDate) {
        List<AppAppointmentItemSuborder> suborderList =
                suborderDao.findByBatchDateBetween(
                        startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                        endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        return suborderList;
    }
}
