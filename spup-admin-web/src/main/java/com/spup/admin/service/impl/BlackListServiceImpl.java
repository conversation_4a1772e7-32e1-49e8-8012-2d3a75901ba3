package com.spup.admin.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.spup.core.service.BlackListService;
import com.spup.data.dao.BlackListDao;
import com.spup.data.entity.appointment.BlackList;
import com.spup.data.entity.appointment.BlackList.CategoryEnum;

@Service
public class BlackListServiceImpl implements BlackListService {
    @Resource
    private BlackListDao blackListDao;
    @Override
    public List<BlackList> findInEffect() {
        return blackListDao.findByStatus(BlackList.StatusEnum.IN_EFFECT);
    }

    @Override
    public boolean isInBlackList(String unionid,BlackList.CategoryEnum categoryEnum) {
        List<BlackList> blackLists = blackListDao.findByUnionid(unionid);

        return blackLists.stream()
                .anyMatch(blackList ->
                        (blackList.getStatus() == BlackList.StatusEnum.IN_EFFECT
                        && blackList.getCategory() == categoryEnum));
    }

    @Override
    public BlackList addBlackList(String unionid,BlackList.CategoryEnum categoryEnum){
        Optional<BlackList> blackListOpt = blackListDao.getByUnionidAndStatusAndCategory(unionid, BlackList.StatusEnum.IN_EFFECT, categoryEnum);
        if(blackListOpt.isPresent()){
            throw new RuntimeException("已在黑名单中了");
        }
        BlackList entity = new BlackList();
        entity.setUnionid(unionid);
        entity.setCategory(categoryEnum);
        entity.setLockingDateTime(LocalDateTime.now());
        entity.setUnlockingDateTime(LocalDateTime.now().plusDays(30));
        entity.setStatus(BlackList.StatusEnum.IN_EFFECT);
        return blackListDao.save(entity);
    }

    @Override
    public BlackList removeBlackList(Long id) {
        Optional<BlackList> blackListOpt = blackListDao.findById(id);
        if(blackListOpt.isPresent()){
            BlackList blackList = blackListOpt.get();
            blackList.setStatus(BlackList.StatusEnum.EXPIRED);
            return blackListDao.save(blackList);
        }
        throw new RuntimeException("记录未找到");
    }

    @Override
    public boolean isBreakRule(String unionid, CategoryEnum categoryEnum) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'isBreakRule'");
    }

    @Override
    public BlackList save(BlackList entity) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }
}
