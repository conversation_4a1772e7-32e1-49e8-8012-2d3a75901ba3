package com.spup.admin.service.appointment.impl;

import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.spup.admin.service.AdminCounterService;
import com.spup.commons.api.CommonResult;
import com.spup.commons.dto.OrderRequest;
import com.spup.commons.utils.NumberGenerator;
import com.spup.core.dto.AppAppointmentItemOrderRequest;
import com.spup.core.dto.AppAppointmentItemOrderResponse;
import com.spup.core.service.IAppAppointmentItemOrderService;
import com.spup.core.service.IAppBatchService;
import com.spup.data.dao.appointment.AppAppointmentItemOrderDao;
import com.spup.data.dao.appointment.AppAppointmentItemSuborderDao;
import com.spup.data.dao.appointment.AppBatchDao;
import com.spup.data.entity.appointment.AppAppointmentItemOrder;
import com.spup.data.entity.appointment.AppAppointmentItemSuborder;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.enums.BatchStatusEnum;
import com.spup.enums.ItemOrderStatusEnum;
import com.spup.enums.OrderCategoryEnum;
import com.spup.enums.OrderStatusEnum;

@Service
public class AppAppointmentItemOrderServiceImpl implements IAppAppointmentItemOrderService {
    @Autowired
    private AppAppointmentItemOrderDao appAppointmentItemOrderDao;
    @Autowired
    private AppAppointmentItemSuborderDao appAppointmentItemSuborderDao;

    @Autowired
    private AppBatchDao appBatchDao;

    @Autowired
    private IAppBatchService iAppBatchService;

    @Autowired
    private AdminCounterService adminCounterService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackOn = Exception.class)
    public CommonResult<?> save(AppAppointmentItemOrderRequest orderRequest, String unionid) throws IOException {
        String batchNo = orderRequest.getBatchNo();
        String contacts = orderRequest.getContacts();

        // 数据校验
        if (!StringUtils.hasLength(batchNo)) {
            return CommonResult.failed("场次为空");
        }

        Optional<AppBatch> batchOptional = appBatchDao.getByBatchNoAndBatchCategory(batchNo,
                OrderCategoryEnum.ITEM_FYPD.getCode());
        if (!batchOptional.isPresent()) {
            return CommonResult.failed("场次不正确");
        }
        AppBatch batch = batchOptional.get();
        if (batch.getBatchStatus() == BatchStatusEnum.CLOSED.getCode()) {
            return CommonResult.failed("场次已关闭");
        }
        ArrayNode node = (ArrayNode) objectMapper.readTree(contacts);
        // TODO What if the contact is dupliated in the order? frontend do the check?
        int personNum = node.size();
        if (node.isEmpty()) {
            return CommonResult.failed("预约人为空");
        }
        if (personNum > 2) {
            return CommonResult.failed("预约人最多2人");
        }

        // 核实预约人人数与剩余票数的关系
        if (batch.getTicketRemaining() < personNum) {
            return CommonResult.failed("名额不足");
        }

        // 校验已预约人
        String batchDate = batch.getBatchDate();
        List<AppAppointmentItemSuborder> subordersByUnionid = appAppointmentItemSuborderDao.findByOnwerUnionid(unionid);
        List<AppAppointmentItemSuborder> todaySuborders = subordersByUnionid.stream()
                .filter(suborder -> suborder.getBatchDate().equals(batchDate))
                .collect(Collectors.toList());
        ;

        if (personNum + todaySuborders.size() > 2) {
            return CommonResult.failed("您已预约了" + todaySuborders.size() + "人, 每日最多可预约2人");
        }

        /**
         * 数据处理
         */
        DecimalFormat df = new DecimalFormat("00");

        LocalDateTime now = LocalDateTime.now();
        String orderNo = NumberGenerator.getOrderNo();

        AppAppointmentItemOrder order = new AppAppointmentItemOrder();
        order.setOrderNo(orderNo);
        order.setBatchNo(batch.getBatchNo());
        order.setBatchDate(batch.getBatchDate());
        order.setBatchStartTime(batch.getBatchStartTime());
        order.setBatchEndTime(batch.getBatchEndTime());
        order.setOwnerUnionid(unionid);

        order.setOrderStatus(ItemOrderStatusEnum.SUCCESS.getCode());
        order.setOrderCategory(OrderCategoryEnum.getEnum(orderRequest.getCategory()).getCode());

        order.setCreateBy(unionid);
        order.setUpdateBy(unionid);
        order.setCreateTime(now);
        order.setUpdateTime(now);

        appAppointmentItemOrderDao.save(order);
        // No need to check for null as save() will throw an exception if it fails
        // If we reach this point, the order was saved successfully

        Byte[] seatNoByteArray = new Byte[personNum];
        try {

            for (int i = 0; i < personNum; i++) {
                // generate the suborder number from order number
                String subOrderNo = order.getOrderNo() + df.format((i + 1));
                // get the seat number
                Byte seatNo = adminCounterService.getNo(order.getBatchNo(), subOrderNo);
                if (seatNo == -1) {
                    throw new RuntimeException("名额不足");
                }
                seatNoByteArray[i] = seatNo;
            }
        } catch (Exception e) {
            for (int j = 0; j < seatNoByteArray.length; j++) {
                if (seatNoByteArray[j] > 0) {
                    adminCounterService.releaseNo(order.getBatchNo(), seatNoByteArray[j]);
                }
            }
            return CommonResult.failed(e.getMessage());
        }

        for (int i = 0; i < personNum; i++) {
            JsonNode jsonNode = node.get(i);
            AppAppointmentItemSuborder suborder = new AppAppointmentItemSuborder();

            suborder.setOrderNo(order.getOrderNo());
            suborder.setSuborderNo(order.getOrderNo() + df.format((i + 1)));

            suborder.setBatchNo(order.getBatchNo());
            suborder.setBatchDate(order.getBatchDate());
            suborder.setBatchStartTimeStr(order.getBatchStartTime());
            suborder.setBatchEndTimeStr(order.getBatchEndTime());

            suborder.setOnwerUnionid(order.getOwnerUnionid());
            suborder.setContactsName(jsonNode.findValue("name").asText());
            suborder.setContactsPhone(jsonNode.findValue("phone").asText());
            suborder.setContactsIdcardCategory(jsonNode.findValue("idcardCategory").numberValue().byteValue());
            suborder.setContactsIdcardNo(jsonNode.findValue("idcardNo").asText());

            suborder.setSeatNo(seatNoByteArray[i]);

            suborder.setSuborderStatus(OrderStatusEnum.SUCCESS.getCode());

            suborder.setCreateBy(unionid);
            suborder.setUpdateBy(unionid);
            suborder.setCreateTime(now);
            suborder.setUpdateTime(now);
            appAppointmentItemSuborderDao.save(suborder);
        }

        iAppBatchService.updateRemaining(batchNo, OrderCategoryEnum.ITEM_FYPD.getCode(), -1 * personNum);

        return CommonResult.succeeded("展项预约成功");

    }

    @Override
    public CommonResult<?> getList(String unionid) {

        List<AppAppointmentItemOrder> appAppointmentOrders = appAppointmentItemOrderDao.findByOwnerUnionid(unionid);
        appAppointmentOrders.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
        /*
         * List<AppAppointmentItemOrderResponse> orderResponses = new ArrayList<>();
         * for (AppAppointmentItemOrder order: appAppointmentOrders) {
         * 
         * }
         */

        // todo 需要测试

        List<AppAppointmentItemOrderResponse> orderResponses = appAppointmentOrders.stream().map(order -> {
            AppAppointmentItemOrderResponse orderResponse = new AppAppointmentItemOrderResponse();
            BeanUtils.copyProperties(order, orderResponse);
            List<AppAppointmentItemSuborder> appAppointmentSuborders = appAppointmentItemSuborderDao
                    .findByOrderNo(order.getOrderNo());
            orderResponse.setSuborders(appAppointmentSuborders);
            return orderResponse;
        }).collect(Collectors.toList());

        return CommonResult.succeeded(orderResponses);
    }

    @Override
    public CommonResult<?> cancel(String orderNo, String unionid) {

        Optional<AppAppointmentItemOrder> orderOptional = appAppointmentItemOrderDao.findByOrderNo(orderNo);
        if (!orderOptional.isPresent()) {
            return CommonResult.failed("订单未找到");
        }

        AppAppointmentItemOrder order = orderOptional.get();
        if (!unionid.equals(order.getOwnerUnionid())) {
            return CommonResult.failed("无权操作");
        }
        order.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
        order.setUpdateBy(unionid);
        appAppointmentItemOrderDao.save(order);

        List<AppAppointmentItemSuborder> suborders = appAppointmentItemSuborderDao.findByOrderNo(orderNo);
        suborders.forEach(suborder -> {
            suborder.setSuborderStatus(OrderStatusEnum.CANCELED.getCode());
            suborder.setUpdateBy(unionid);
            appAppointmentItemSuborderDao.save(suborder);

            adminCounterService.releaseNo(suborder.getBatchNo(), suborder.getSeatNo());
        });
        int updateNum = suborders.size();
        // 更新时刻表
        iAppBatchService.updateRemaining(order.getBatchNo(), OrderCategoryEnum.ITEM_FYPD.getCode(), updateNum);

        return CommonResult.succeeded(updateNum);
    }

    @Override
    public CommonResult<?> breaked(String orderNo, String unionid) {
        Optional<AppAppointmentItemOrder> orderOptional = appAppointmentItemOrderDao.findByOrderNo(orderNo);
        if (!orderOptional.isPresent()) {
            return CommonResult.failed("订单未找到");
        }

        AppAppointmentItemOrder order = orderOptional.get();
        order.setOrderStatus(OrderStatusEnum.BREAKED.getCode());
        order.setUpdateBy(unionid);
        appAppointmentItemOrderDao.save(order);

        List<AppAppointmentItemSuborder> suborders = appAppointmentItemSuborderDao.findByOrderNo(orderNo);
        suborders.forEach(suborder -> {
            if (suborder.getSuborderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode()) {
                suborder.setSuborderStatus(OrderStatusEnum.BREAKED.getCode());
                suborder.setUpdateBy(unionid);
                appAppointmentItemSuborderDao.save(suborder);
                adminCounterService.releaseNo(suborder.getBatchNo(), suborder.getSeatNo());
            }
        });
        int updateNum = suborders.size();
        iAppBatchService.updateRemaining(order.getBatchNo(), OrderCategoryEnum.ITEM_FYPD.getCode(), updateNum);

        return CommonResult.succeeded(updateNum);
    }

    @Override
    public CommonResult<?> delete(String orderNo, String unionid) {
        Optional<AppAppointmentItemOrder> orderOptional = appAppointmentItemOrderDao.findByOrderNo(orderNo);
        if (!orderOptional.isPresent()) {
            return CommonResult.failed("订单未找到");
        }
        AppAppointmentItemOrder order = orderOptional.get();
        if (!unionid.equals(order.getOwnerUnionid())) {
            return CommonResult.failed("无权操作");
        }
        order.setUpdateBy(unionid);
        appAppointmentItemOrderDao.delete(order);

        List<AppAppointmentItemSuborder> suborders = appAppointmentItemSuborderDao.findByOrderNo(orderNo);
        suborders.forEach(suborder -> {
            suborder.setUpdateBy(unionid);
            appAppointmentItemSuborderDao.delete(suborder);
        });

        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> checkout(OrderRequest orderRequest, String unionid) {
        String suborderNo = orderRequest.getSubOrderId();
        Optional<AppAppointmentItemSuborder> suborderOptional = appAppointmentItemSuborderDao
                .findBySuborderNo(suborderNo);
        if (!suborderOptional.isPresent()) {
            return CommonResult.failed("未找到该预约信息");
        }
        AppAppointmentItemSuborder suborder = suborderOptional.get();
        if (suborder.getSuborderStatus() == OrderStatusEnum.CANCELED.getCode()) {
            return CommonResult.failed("该预约已取消");
        }
        if (suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode()) {
            return CommonResult.failed("该预约已核销");
        }
        suborder.setSuborderStatus(OrderStatusEnum.FINISHED.getCode());
        suborder.setUpdateTime(LocalDateTime.now());
        appAppointmentItemSuborderDao.save(suborder);

        List<AppAppointmentItemSuborder> suborders = appAppointmentItemSuborderDao.findByOrderNo(suborder.getOrderNo());
        boolean result = suborders.stream()
                .allMatch(item -> item.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode());
        if (result) {
            Optional<AppAppointmentItemOrder> orderOptional = appAppointmentItemOrderDao
                    .findByOrderNo(suborder.getOrderNo());
            AppAppointmentItemOrder order = orderOptional.get();
            order.setOrderStatus(OrderStatusEnum.FINISHED.getCode());
            order.setOrderNo(suborder.getOrderNo());
            order.setUpdateTime(LocalDateTime.now());
            appAppointmentItemOrderDao.save(order);
        }
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> getSuborderDetail(String suborderNo) {
        Optional<AppAppointmentItemSuborder> suborderOptional = appAppointmentItemSuborderDao
                .findBySuborderNo(suborderNo);
        if (!suborderOptional.isPresent()) {
            return CommonResult.failed("未找到该预约信息");
        }
        AppAppointmentItemSuborder suborder = suborderOptional.get();
        return CommonResult.succeeded(suborder);
    }
}
