package com.spup.admin.service.analysis;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.domain.Page;

import com.spup.core.dto.DateQueryRequest;
import com.spup.data.entity.Article;
import com.spup.data.entity.ArticleDailyDetail;
import com.spup.data.entity.mp.MpDatacubeArticleSummary;

public interface IMpDatacubeArticleSummaryService {
    Long getArticleTotal();
    Long getArticleTotal(LocalDate start,LocalDate end);
    Long getArticleReadTotal();
    Long getArticleReadTotal(LocalDate start,LocalDate end);
    Page<MpDatacubeArticleSummary> getListByPage(DateQueryRequest queryParam);
    void saveList(List<Article> newArticleList);
    void updateList(List<ArticleDailyDetail> dailyDetailList);
}
