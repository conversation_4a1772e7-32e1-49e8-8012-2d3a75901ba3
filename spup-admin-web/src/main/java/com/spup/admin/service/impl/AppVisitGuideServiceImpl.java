package com.spup.admin.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.spup.core.dto.VisitGuideListRequest;
import com.spup.core.service.IAppVisitGuideService;
import com.spup.data.dao.AppVisitGuideDao;
import com.spup.data.entity.AppVisitGuide;

@Service
public class AppVisitGuideServiceImpl implements IAppVisitGuideService {
    @Resource
    private AppVisitGuideDao appVisitGuideDao;

    @Override
    public Page<AppVisitGuide> getListByPage(VisitGuideListRequest listParam) {

        // 调用分页插件,访问第一页，每页2条数据
        Pageable pageable = PageRequest.of(listParam.getPageNum() - 1, listParam.getPageSize());
        // 从数据库查询
        Specification<AppVisitGuide> spec = new Specification<AppVisitGuide>() {
            // Predicate:封装了 单个的查询条件
            /**
             * Root<Users> root:查询对象的属性的封装。
             * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select from order by
             * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
             */
            @Override
            public Predicate toPredicate(@NonNull Root<AppVisitGuide> root, @NonNull CriteriaQuery<?> query,
                    @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if (StringUtils.hasLength(listParam.getSpotArea())) {
                    list.add(cb.equal(root.get("spotArea"), listParam.getSpotArea()));// 筛选
                }
                if (StringUtils.hasLength(listParam.getSpotName())) {
                    list.add(cb.like(root.get("spotName"), "%" + listParam.getSpotName() + "%"));// 筛选
                }
                query.orderBy(cb.asc(root.get("sortCode")));// 排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };
        Page<AppVisitGuide> all = appVisitGuideDao.findAll(spec, pageable);
        return all;
    }

    @Override
    public List<AppVisitGuide> findAllSortByCode(int limit) {
        List<AppVisitGuide> allVisitGuideList = appVisitGuideDao.findAll();
        List<AppVisitGuide> result = allVisitGuideList.stream()
                .sorted((guide1, guide2) -> guide2.getSortCode() - guide1.getSortCode())
                .limit(limit)
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public List<AppVisitGuide> findAllSortByPageViews(int limit) {
        List<AppVisitGuide> allVisitGuideList = appVisitGuideDao.findAll();
        List<AppVisitGuide> result = allVisitGuideList.stream()
                .sorted((guide1, guide2) -> guide2.getPageViews() - guide1.getPageViews())
                .limit(limit)
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public AppVisitGuide create(AppVisitGuide appVisitGuide, String openId) {
        return appVisitGuideDao.save(appVisitGuide);
    }

    @Override
    public AppVisitGuide update(AppVisitGuide appVisitGuide, String openId) {
        return appVisitGuideDao.save(appVisitGuide);
    }

    @Override
    public int delete(long id, String openId) {
        appVisitGuideDao.deleteById(id);
        return 1;
    }

    @Override
    public AppVisitGuide view(long id) {
        return appVisitGuideDao.findById(id).get();
    }

    @Override
    public List<AppVisitGuide> getAllList() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAllList'");
    }

    @Override
    public void read(long id) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'read'");
    }
}
