package com.spup.admin.service.analysis.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.spup.data.dao.mp.MpDatacubeUserSummaryDao;
import com.spup.data.dao.sys.AppOperateLogDao;
import com.spup.data.entity.mp.MpDatacubeUserSummary;
import com.spup.admin.dto.DateQueryRequest;
import com.spup.data.entity.UserSummary;
import com.spup.admin.service.analysis.IMpDatacubeUserSummaryService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
// import java.util.Optional;

@Service
public class MpDatacubeUserSummaryServiceImpl implements IMpDatacubeUserSummaryService {
    @Resource
    private MpDatacubeUserSummaryDao userSummaryDao;
    @Resource
    private AppOperateLogDao operateLogDao;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public Long getUserTotal() {
        MpDatacubeUserSummary userSummary = findTopByOrderByCreateTimeDesc();
        if(userSummary==null){
            return 0L;
        }
        return Long.valueOf(userSummary.getUserCumulate());
    }

    @Override
    public Long getUserTotal(LocalDate start, LocalDate end) {
        // TODO
        if (start.isAfter(end)) {
            throw new IllegalArgumentException("开始日期不能大于结束日期");
        }
        if (start.isEqual(end)) {
            return 0L;
        }
        if (end.isAfter(LocalDate.now()) || end == null) {
            end = LocalDate.now();
        }

        MpDatacubeUserSummary userSummaryEnd = userSummaryDao.getByAnalysisDate(end).orElse(null);
        MpDatacubeUserSummary userSummaryStart = userSummaryDao.getByAnalysisDate(start).orElse(null);
        if(userSummaryEnd==null || userSummaryStart==null){
            return 0L;
        }
        return Long.valueOf(userSummaryEnd.getUserCumulate()-userSummaryStart.getUserCumulate());

        // MpDatacubeUserSummary userSummary = findTopByOrderByCreateTimeDesc();
        // Optional<MpDatacubeUserSummary> userSummaryStart = userSummaryDao.getByAnalysisDate(start);
        // return Long.valueOf(userSummary.getUserCumulate()-userSummaryStart.get().getUserCumulate());
    }

    public MpDatacubeUserSummary findTopByOrderByCreateTimeDesc(){
        Pageable pageable = PageRequest.of(0, 1, Sort.by(Sort.Direction.DESC, "analysisDate"));
        Page<MpDatacubeUserSummary> userSummaryPage = userSummaryDao.findAll(pageable);
        return userSummaryPage.get().findFirst().orElse(null);
    }

    @Override
    public Page<MpDatacubeUserSummary> getListByPage(DateQueryRequest queryParam) {
        Pageable pageable = PageRequest.of(queryParam.getPageNum()-1, queryParam.getPageSize());
        Specification<MpDatacubeUserSummary> spec = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            if(queryParam.getStartDate()!=null){
                list.add(cb.greaterThanOrEqualTo(root.get("analysisDate"),queryParam.getStartDate()));//筛选
            }
            if(queryParam.getEndDate()!=null){
                list.add(cb.lessThanOrEqualTo(root.get("analysisDate"),queryParam.getEndDate()));//筛选
            }
            query.orderBy(cb.desc(root.get("analysisDate")));//排序
            Predicate[] arr = new Predicate[list.size()];
            return cb.and(list.toArray(arr));
        };
        Page<MpDatacubeUserSummary> page = userSummaryDao.findAll(spec, pageable);
        return page;
    }

    @Override
    public void save(LocalDate date, Integer total, List<UserSummary> userSummaryList) {
        MpDatacubeUserSummary userSummary = new MpDatacubeUserSummary();
        userSummary.setAnalysisDate(date);
        userSummary.setUserCumulate(total);
        plus(userSummary,userSummaryList);

        LocalDateTime start = date.atTime(0,0,0);
        LocalDateTime end = date.atTime(23,59,59);

        userSummary.setVisits(operateLogDao.findByOperateTimeBetween(start,end).stream().count());
        userSummary.setTotalVisits(operateLogDao.findAll().stream().count());

        userSummaryDao.save(userSummary);
    }

    public void plus(MpDatacubeUserSummary userSummary,List<UserSummary> userSummaryList){
        int newUserSum = userSummaryList.stream().mapToInt(UserSummary::getNewUser).sum();
        int cancelUserSum = userSummaryList.stream().mapToInt(UserSummary::getCancelUser).sum();
        userSummary.setNewUser(newUserSum);
        userSummary.setCancelUser(cancelUserSum);
        try {
            userSummary.setArticleSummaryJson(objectMapper.writeValueAsString(userSummaryList));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }


}
