package com.spup.admin.service.appointment.impl;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.spup.commons.api.CommonResult;
import com.spup.commons.dto.OrderRequest;
import com.spup.commons.utils.NumberGenerator;
import com.spup.core.dto.AppAppointmentOrderRequest;
import com.spup.core.dto.AppAppointmentOrderResponse;
import com.spup.core.service.IAppAppointmentOrderService;
import com.spup.core.service.IAppBatchService;
import com.spup.core.service.IAppTemporaryExhibitionService;
import com.spup.data.dao.appointment.AppAppointmentOrderDao;
import com.spup.data.dao.appointment.AppAppointmentSuborderDao;
import com.spup.data.entity.appointment.AppAppointmentOrder;
import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.data.entity.appointment.AppBatch;
import com.spup.data.entity.appointment.AppTemporaryExhibition;
import com.spup.enums.BatchStatusEnum;
import com.spup.enums.DeletedStatusEnum;
import com.spup.enums.OrderCategoryEnum;
import com.spup.enums.OrderStatusEnum;

@Service
public class AppAppointmentOrderServiceImpl implements IAppAppointmentOrderService {

    @Resource
    private AppAppointmentOrderDao appAppointmentOrderDao;
    @Resource
    private AppAppointmentSuborderDao appAppointmentSuborderDao;

    @Autowired
    private IAppBatchService iAppBatchService;
    @Resource
    private IAppTemporaryExhibitionService iAppTemporaryExhibitionService;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public CommonResult<?> save(AppAppointmentOrderRequest orderRequest, String unionid)
            throws JsonProcessingException, JsonMappingException {
        String batchNo = orderRequest.getBatchNo();
        String contacts = orderRequest.getContacts();
        // 数据校验
        // 场次校验
        if (!StringUtils.hasLength(batchNo)) {
            return CommonResult.failed("场次为空");
        }
        AppBatch batch = iAppBatchService.getByNo(batchNo, OrderCategoryEnum.TICKET.getCode());
        if (batch == null) {
            return CommonResult.failed("场次不正确");
        }
        if (batch.getBatchStatus().byteValue() == BatchStatusEnum.CLOSED.getCode()) {
            return CommonResult.failed("场次已关闭");
        }

        // 预约人格式校验
        ArrayNode personArray = (ArrayNode) objectMapper.readTree(contacts);
        int personNum = personArray.size();

        if (personNum == 0) {
            return CommonResult.failed("预约人为空");
        } else if (personNum > 5) {
            return CommonResult.failed("预约人最多预约5人");
        }

        // 核实预约人人数与剩余票数的关系
        if (batch.getTicketRemaining() < personNum) {
            return CommonResult.failed("名额不足");
        }
        // 校验已预约人
        String batchDate = batch.getBatchDate();
        List<AppAppointmentSuborder> subordersByUnionid = appAppointmentSuborderDao.findByOnwerUnionid(unionid);
        List<AppAppointmentSuborder> todaySuborders = subordersByUnionid.stream()
                .filter(suborder -> suborder.getBatchDate().equals(batchDate))
                .collect(Collectors.toList());
        ;
        if (personNum + todaySuborders.size() > 5) {
            return CommonResult.failed("您已预约了" + todaySuborders.size() + "人, 每日最多可预约5人");
        }

        /**
         * 数据处理
         */
        DecimalFormat df = new DecimalFormat("00");

        String orderNo = NumberGenerator.getOrderNo();
        AppAppointmentOrder order = new AppAppointmentOrder();
        order.setOrderNo(orderNo);
        order.setBatchNo(batch.getBatchNo());
        order.setBatchDate(batch.getBatchDate());
        order.setBatchStartTime(batch.getBatchStartTime());
        order.setBatchEndTime(batch.getBatchEndTime());
        order.setOwnerUnionid(unionid);
        order.setOrderStatus(OrderStatusEnum.SUCCESS.getCode());
        order.setOrderRemark(orderRequest.getExhibitionNo());
        order.setOrderCategory(orderRequest.getCategory());

        appAppointmentOrderDao.save(order);

        iAppBatchService.updateRemaining(batchNo, OrderCategoryEnum.TICKET.getCode(), -1 * personNum);
        for (int i = 0; i < personNum; i++) {
            JsonNode jsonObject = personArray.get(i);

            AppAppointmentSuborder suborder = new AppAppointmentSuborder();

            suborder.setOrderNo(order.getOrderNo());
            suborder.setSuborderNo(order.getOrderNo() + df.format((i + 1)));

            suborder.setBatchNo(order.getBatchNo());
            suborder.setBatchDate(order.getBatchDate());
            suborder.setBatchStartTimeStr(order.getBatchStartTime());
            suborder.setBatchEndTimeStr(order.getBatchEndTime());

            suborder.setOnwerUnionid(order.getOwnerUnionid());
            suborder.setContactsName(jsonObject.get("name").asText());
            suborder.setContactsPhone(jsonObject.get("phone").asText());
            suborder.setContactsIdcardCategory(jsonObject.get("idcardCategory").numberValue().byteValue());
            suborder.setContactsIdcardNo(jsonObject.get("idcardNo").asText());

            suborder.setSuborderStatus(OrderStatusEnum.SUCCESS.getCode());

            appAppointmentSuborderDao.save(suborder);
        }

        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> getList(String unionid) {

        // appAppointmentOrderExample.setOrderByClause(" create_time desc ");
        List<AppAppointmentOrder> appAppointmentOrders = appAppointmentOrderDao.findByOwnerUnionid(unionid);
        appAppointmentOrders.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
        List<AppAppointmentOrderResponse> orderResponses = new ArrayList<>();
        for (AppAppointmentOrder order : appAppointmentOrders) {
            AppAppointmentOrderResponse orderResponse = new AppAppointmentOrderResponse();

            BeanUtils.copyProperties(order, orderResponse);

            List<AppAppointmentSuborder> appAppointmentSuborders = appAppointmentSuborderDao
                    .findByOrderNo(order.getOrderNo());

            orderResponse.setSuborders(appAppointmentSuborders);
            orderResponses.add(orderResponse);

            if (!StringUtils.hasLength(order.getOrderRemark())
                    && !StringUtils.hasLength(order.getOrderRemark().trim())) {
                AppTemporaryExhibition exhibitionDetail = iAppTemporaryExhibitionService
                        .getExhibitionDetail(order.getOrderRemark());
                orderResponse.setExhibition(exhibitionDetail);
            }
        }

        return CommonResult.succeeded(orderResponses);
    }

    @Override
    public CommonResult<?> cancel(String orderNo, String unionid) {
        Optional<AppAppointmentOrder> orderOptional = appAppointmentOrderDao.getByOrderNo(orderNo);
        if (!orderOptional.isPresent()) {
            return CommonResult.failed("订单不存在");
        }
        AppAppointmentOrder order = orderOptional.get();
        if (order.getOrderStatus().shortValue() != OrderStatusEnum.SUCCESS.getCode()) {
            return CommonResult.failed("订单状态异常");
        }
        if (!order.getOwnerUnionid().equals(unionid)) {
            return CommonResult.failed("无权操作");
        }
        order.setOrderStatus(OrderStatusEnum.CANCELED.getCode());

        appAppointmentOrderDao.save(order);

        List<AppAppointmentSuborder> suborders = appAppointmentSuborderDao.findByOrderNo(orderNo);
        suborders.forEach(sub -> {
            if (sub.getSuborderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode()) {
                sub.setSuborderStatus(OrderStatusEnum.CANCELED.getCode());
                appAppointmentSuborderDao.save(sub);
                iAppBatchService.updateRemaining(order.getBatchNo(), OrderCategoryEnum.TICKET.getCode(), 1);
            }
        });

        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> breaked(String orderNo, String unionid) {
        Optional<AppAppointmentOrder> orderOptional = appAppointmentOrderDao.getByOrderNo(orderNo);
        if (!orderOptional.isPresent()) {
            return CommonResult.failed("订单不存在");
        }
        AppAppointmentOrder order = orderOptional.get();
        if (order.getOrderStatus().shortValue() != OrderStatusEnum.SUCCESS.getCode()) {
            return CommonResult.failed("订单状态异常");
        }
        if (!order.getOwnerUnionid().equals(unionid)) {
            return CommonResult.failed("无权操作");
        }
        order.setOrderStatus(OrderStatusEnum.BREAKED.getCode());

        appAppointmentOrderDao.save(order);

        List<AppAppointmentSuborder> suborders = appAppointmentSuborderDao.findByOrderNo(orderNo);
        suborders.forEach(sub -> {
            if (sub.getSuborderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode()) {
                sub.setSuborderStatus(OrderStatusEnum.BREAKED.getCode());
                appAppointmentSuborderDao.save(sub);
            }
        });
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> delete(String orderNo) {
        Optional<AppAppointmentOrder> orderOptional = appAppointmentOrderDao.getByOrderNo(orderNo);
        if (!orderOptional.isPresent()) {
            return CommonResult.failed("订单不存在");
        }
        AppAppointmentOrder order = orderOptional.get();
        if (order.getOrderStatus().shortValue() != OrderStatusEnum.CANCELED.getCode()) {
            return CommonResult.failed("只有取消的预约才能删除");
        }
        order.setDeleted(DeletedStatusEnum.DELETED.getCode());
        appAppointmentOrderDao.save(order);

        List<AppAppointmentSuborder> suborders = appAppointmentSuborderDao.findByOrderNo(orderNo);
        suborders.forEach(sub -> {
            if (sub.getSuborderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode()) {
                sub.setDeleted(DeletedStatusEnum.DELETED.getCode());
                appAppointmentSuborderDao.save(sub);
            }
        });
        return CommonResult.succeeded("");
    }

    @Override
    public List<AppAppointmentOrder> getListByUnionid(String unionid) {
        return null;
    }

    @Override
    public AppAppointmentOrder getOrderByOrderNo(String orderNo) {
        return appAppointmentOrderDao.getByOrderNo(orderNo).get();
    }

    @Override
    public CommonResult<?> checkout(OrderRequest orderRequest, String unionid) {
        String suborderId = orderRequest.getSubOrderId();
        Optional<AppAppointmentSuborder> suborderOptional = appAppointmentSuborderDao.findBySuborderNo(suborderId);
        if (!suborderOptional.isPresent()) {
            return CommonResult.failed("未找到该预约信息");
        }
        AppAppointmentSuborder suborder = suborderOptional.get();
        String batchDate = suborder.getBatchDate();
        String startTime = suborder.getBatchStartTimeStr();
        String endTime = suborder.getBatchEndTimeStr();
        LocalDateTime now = LocalDateTime.now();
        String today = now.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        String hhmm = now.toLocalTime().format(DateTimeFormatter.ofPattern("HHmm"));
        // String hhmm = DateTimeUtil.getSysTime("HHmm");

        if (!today.equals(batchDate)) {
            return CommonResult.failed("非今日预约");
        }
        if (hhmm.compareTo(startTime) < 0 || hhmm.compareTo(endTime) >= 0) {
            System.out.println("开始时间：" + startTime);
            System.out.println("结束时间：" + endTime);
            System.out.println("当前时间：" + hhmm);
            return CommonResult.failed("非本时段预约");
        }
        if (suborder.getSuborderStatus() == OrderStatusEnum.CANCELED.getCode()) {
            return CommonResult.failed("该预约已取消");
        }
        if (suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode()) {
            return CommonResult.failed("该预约已核销");
        }

        suborder.setSuborderStatus(OrderStatusEnum.FINISHED.getCode());

        appAppointmentSuborderDao.save(suborder);
        List<AppAppointmentSuborder> suborders = appAppointmentSuborderDao.findByOrderNo(suborder.getOrderNo());
        boolean isAllCheckout = suborders.stream()
                .allMatch(sub -> sub.getSuborderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode());
        if (isAllCheckout) {
            Optional<AppAppointmentOrder> orderOptional = appAppointmentOrderDao.getByOrderNo(suborder.getOrderNo());
            AppAppointmentOrder order = orderOptional.get();
            order.setOrderStatus(OrderStatusEnum.FINISHED.getCode());
            appAppointmentOrderDao.save(order);
        }
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> getSuborderDetail(String subOrderId) {
        Optional<AppAppointmentSuborder> suborder = appAppointmentSuborderDao.findBySuborderNo(subOrderId);
        if (!suborder.isPresent()) {
            return CommonResult.failed("未找到该预约信息");
        }
        return CommonResult.succeeded(suborder.get());
    }
}
