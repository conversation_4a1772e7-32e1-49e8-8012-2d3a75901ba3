package com.spup.admin.service.appointment;

import java.io.IOException;

import com.spup.commons.api.CommonResult;
import com.spup.core.dto.AppAppointmentOrderTemporaryExhibitionRequest;
import com.spup.core.service.IOrderService;

public interface IAppAppointmentOrderTemporaryExhibitionService extends IOrderService {
    // 对外接口
    CommonResult<?> save(AppAppointmentOrderTemporaryExhibitionRequest orderRequest, String unionid) throws IOException;

    CommonResult<?> delete(String orderNo);
    // 内部接口
}
