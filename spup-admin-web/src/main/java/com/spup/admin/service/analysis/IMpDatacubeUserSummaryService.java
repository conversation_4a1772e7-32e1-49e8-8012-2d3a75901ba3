package com.spup.admin.service.analysis;

import com.spup.data.entity.mp.MpDatacubeUserSummary;
import com.spup.admin.dto.DateQueryRequest;
import com.spup.data.entity.UserSummary;
import org.springframework.data.domain.Page;

import java.time.LocalDate;
import java.util.List;

public interface IMpDatacubeUserSummaryService {
    Long getUserTotal();
    Long getUserTotal(LocalDate start,LocalDate end);
    MpDatacubeUserSummary findTopByOrderByCreateTimeDesc();
    Page<MpDatacubeUserSummary> getListByPage(DateQueryRequest queryParam);
    void save(LocalDate date,Integer total,List<UserSummary> userSummaryList);

}
