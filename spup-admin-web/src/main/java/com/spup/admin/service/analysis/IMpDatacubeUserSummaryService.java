package com.spup.admin.service.analysis;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.domain.Page;

import com.spup.core.dto.DateQueryRequest;
import com.spup.data.entity.UserSummary;
import com.spup.data.entity.mp.MpDatacubeUserSummary;

public interface IMpDatacubeUserSummaryService {
    Long getUserTotal();
    Long getUserTotal(LocalDate start,LocalDate end);
    MpDatacubeUserSummary findTopByOrderByCreateTimeDesc();
    Page<MpDatacubeUserSummary> getListByPage(DateQueryRequest queryParam);
    void save(LocalDate date,Integer total,List<UserSummary> userSummaryList);

}
