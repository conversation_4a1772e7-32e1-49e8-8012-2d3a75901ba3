package com.spup.admin.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.spup.admin.service.IAppSurroundingGoodsService;
import com.spup.core.dto.GoodsListRequest;
import com.spup.core.service.impl.BaseSurroundingGoodsServiceImpl;
import com.spup.data.entity.AppSurroundingGoods;

/**
 * Admin Surrounding Goods Service Implementation
 * Extends BaseSurroundingGoodsServiceImpl with admin-specific operations
 */
@Service
public class AppSurroundingGoodsServiceImpl extends BaseSurroundingGoodsServiceImpl implements IAppSurroundingGoodsService {

    @Override
    public Page<AppSurroundingGoods> getListByPage(GoodsListRequest listParam) {

        // 调用分页插件
        Pageable pageable = PageRequest.of(listParam.getPageNum() - 1, listParam.getPageSize());
        // 从数据库查询
        Specification<AppSurroundingGoods> spec = new Specification<AppSurroundingGoods>() {
            // Predicate:封装了 单个的查询条件
            /**
             * Root<Users> root:查询对象的属性的封装。
             * CriteriaQuery<?> query：封装了我们要执行的查询中的各个部分的信息，select from order by
             * CriteriaBuilder cb:查询条件的构造器。定义不同的查询条件
             */
            @Override
            public Predicate toPredicate(@NonNull Root<AppSurroundingGoods> root, @NonNull CriteriaQuery<?> query,
                    @NonNull CriteriaBuilder cb) {
                List<Predicate> list = new ArrayList<>();
                if (StringUtils.hasLength(listParam.getGoodsName())) {
                    list.add(cb.like(root.get("goodsName"), listParam.getGoodsName()));// 筛选
                }
                if (StringUtils.hasLength(listParam.getGoodsCategory())) {
                    list.add(cb.equal(root.get("goodsCategory"), listParam.getGoodsCategory()));// 筛选
                }
                if (listParam.getGoodsStatus() != null) {
                    list.add(cb.equal(root.get("goodsStatus"), listParam.getGoodsStatus()));// 筛选
                }
                query.orderBy(cb.desc(root.get("id")));// 排序
                Predicate[] arr = new Predicate[list.size()];
                return cb.and(list.toArray(arr));
            }
        };

        Page<AppSurroundingGoods> all = appSurroundingGoodsDao.findAll(spec, pageable);

        return all;
    }

    // view, create, update, delete methods are inherited from BaseSurroundingGoodsServiceImpl
}
