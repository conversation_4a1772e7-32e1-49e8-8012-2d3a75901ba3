package com.spup.admin.mp.service;


import com.spup.data.entity.Article;
import com.spup.data.entity.ArticleDailyDetail;
import com.spup.data.entity.UserSummary;

import java.time.LocalDate;
import java.util.List;

public interface IMpDatacubeSerivce {
    List<ArticleDailyDetail> getArticleSummary(LocalDate date) throws Exception;
    List<Article> getArticleTotal(LocalDate date) throws Exception;
    Integer getUserCumulate(LocalDate date) throws Exception;
    List<UserSummary> getUserSummary(LocalDate date) throws Exception;
}
