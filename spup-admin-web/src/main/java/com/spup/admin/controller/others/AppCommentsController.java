package com.spup.admin.controller.others;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.spup.admin.service.IAppCommentsService;
import com.spup.commons.api.CommonResult;
import com.spup.core.dto.DateQueryRequest;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "留言管理")
@RestController
@RequestMapping("/comment")
public class AppCommentsController {
    @Autowired
    private IAppCommentsService iAppCommentsService;

    @Operation(summary = "留言")
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    public CommonResult<?> list(@RequestBody DateQueryRequest queryRequest)  {

        return CommonResult.succeeded(iAppCommentsService.getPageList(queryRequest));
    }
}
