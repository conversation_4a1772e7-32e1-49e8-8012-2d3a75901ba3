package com.spup.admin.controller.appointment;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.spup.commons.api.CommonResult;
import com.spup.core.dto.AppAdminTeamOrderListRequest;
import com.spup.core.service.IAppAppointmentTeamOrderService;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "团队登记")
@RestController
@RequestMapping(value = "/team")
public class AppointmentTeamController {
    @Resource
    private IAppAppointmentTeamOrderService iAppAppointmentTeamOrderService;

    @Operation(summary = "分页查询")
    @PostMapping(value="/list/byPage")
    public CommonResult<?> listByPage (@RequestBody AppAdminTeamOrderListRequest param)  {
        return CommonResult.succeeded(iAppAppointmentTeamOrderService.getAdminTeamOrderList(param));
    }

    @Operation(summary = "保存")
    @PostMapping(value="/save")
    public CommonResult<?> save (@RequestBody AppAppointmentTeamOrder teamOrder, HttpServletRequest req) {
        String unionid = (String)req.getSession().getAttribute("unionid");
        //teamOrder.setOwnerUnionid(unionid);
        if(teamOrder.getId()!=null) {
            AppAppointmentTeamOrder view = iAppAppointmentTeamOrderService.view(teamOrder.getId());
            teamOrder.setOwnerUnionid(view.getOwnerUnionid());
            teamOrder.setCreateBy(view.getCreateBy());
            teamOrder.setCreateTime(view.getCreateTime());
            teamOrder.setOrderCategory(view.getOrderCategory());
            teamOrder.setExhibitionNo(view.getExhibitionNo());
        } else {
            teamOrder.setOwnerUnionid(unionid);
        }
        AppAppointmentTeamOrder save = iAppAppointmentTeamOrderService.save(teamOrder);
        return CommonResult.succeeded(save);
    }

    @Operation(summary = "查看")
    @PostMapping(value="/view/byId/{id}")
    public CommonResult<?> viewActivity (@PathVariable Long id)  {
        return CommonResult.succeeded(iAppAppointmentTeamOrderService.view(id));
    }



}
