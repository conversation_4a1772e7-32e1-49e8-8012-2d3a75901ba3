package com.spup.admin.controller.others;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.spup.commons.api.CommonResult;
import com.spup.core.controller.BaseController;
import com.spup.core.dto.CustomerListRequest;
import com.spup.core.service.BlackListService;
import com.spup.core.service.IAppCustomerService;
import com.spup.data.entity.appointment.BlackList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "客户管理")
@RestController
@RequestMapping(value = "/customer")
public class AppCustomerController extends BaseController {
    @Resource
    private IAppCustomerService iAppCustomerService;
    @Resource
    private BlackListService blackListService;

    @Operation(summary = "查询客户信息")
    @GetMapping(value="/listByPage")
    public CommonResult<?> listByPage (CustomerListRequest param)  {
        logRequest("查询客户信息", param);
        return success(iAppCustomerService.getPageList(param));
    }

    @Operation(summary = "查询客户详细信息（含预约数据）")
    @PostMapping(value="/view/{unionid}")
    public CommonResult<?> view (@PathVariable String unionid)  {
        logRequest("查询客户详细信息", unionid);
        return success(iAppCustomerService.get(unionid));
    }

    @Operation(summary = "添加黑名单")
    @PostMapping(value="/blackList/add/{unionid}/{categoryEnum}")
    public CommonResult<?> addBlack (@PathVariable String unionid,
                                     @PathVariable BlackList.CategoryEnum categoryEnum)  {
        logRequest("添加黑名单", String.format("unionid=%s, category=%s", unionid, categoryEnum));
        return success(blackListService.addBlackList(unionid,categoryEnum));
    }

    @Operation(summary = "取消黑名单")
    @PostMapping(value="/blackList/remove/{id}")
    public CommonResult<?> removeBlack (@PathVariable Long id)  {
        logRequest("取消黑名单", id);
        return success(blackListService.removeBlackList(id));
    }

    @Operation(summary = "黑名单列表")
    @PostMapping(value="/blackList/list")
    public CommonResult<?> listBlack ()  {
        logRequest("黑名单列表");
        return success(blackListService.findInEffect());
    }
}
