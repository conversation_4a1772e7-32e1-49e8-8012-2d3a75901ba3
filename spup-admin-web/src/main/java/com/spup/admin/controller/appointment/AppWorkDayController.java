// package com.spup.admin.controller.appointment;

// import java.io.UnsupportedEncodingException;
// import java.util.List;
// import java.util.stream.Collectors;

// import javax.annotation.Resource;
// import javax.servlet.http.HttpServletRequest;

// import org.springframework.beans.BeanUtils;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.PostMapping;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RestController;

// import com.spup.admin.dto.WorkDayDTO;
// import com.spup.admin.dto.WorkdayVO;
// import com.spup.admin.service.IAppointmentService;
// import com.spup.admin.service.appointment.IAppBatchService;
// import com.spup.admin.service.appointment.IAppWorkdayService;
// import com.spup.commons.api.CommonResult;
// import com.spup.data.entity.appointment.AppBatch;
// import com.spup.data.entity.appointment.AppWorkday;

// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.tags.Tag;

// @Tag(name = "场馆开放日期管理")
// @RestController
// @RequestMapping(value = "/workday")
// public class AppWorkDayController {
//     @Resource
//     private IAppWorkdayService iAppWorkdayService;
//     @Resource
//     private IAppointmentService iAppointmentService;
//     @Resource
//     private IAppBatchService iAppBatchService;

//     @Operation(summary = "查询开放日期情况")
//     @GetMapping(value = "/getWorkDays/{startDate}_{endDate}")
//     public CommonResult<?> getWorkDays(@PathVariable String startDate, @PathVariable String endDate) {
//         List<AppWorkday> dayList = iAppWorkdayService.getListByDate(startDate, endDate);
//         List<WorkdayVO> resultList = dayList.stream()
//                 .map(day -> {
//                     WorkdayVO vo = new WorkdayVO();
//                     BeanUtils.copyProperties(day, vo);
//                     List<AppBatch> personBatchList = iAppBatchService.getListByDate((byte) 1, day.getDay());
//                     int personTotal = personBatchList.stream()
//                             .mapToInt(AppBatch::getTicketTotal)
//                             .sum();
//                     vo.setPersonTotal(personTotal);
//                     List<AppBatch> teamBatchList = iAppBatchService.getListByDate((byte) 2, day.getDay());
//                     int teamTotal = teamBatchList.stream()
//                             .mapToInt(AppBatch::getTicketTotal)
//                             .sum();
//                     vo.setTeamTotal(teamTotal);
//                     return vo;
//                 }).collect(Collectors.toList());

//         return CommonResult.succeeded(resultList);
//     }

//     @Operation(summary = "开启/关闭日期开放")
//     @PostMapping(value = "/setWorkDay")
//     public CommonResult<?> setWorkDay(@RequestBody WorkDayDTO dayDTO, HttpServletRequest req)
//             throws UnsupportedEncodingException {
//         String day = dayDTO.getDays();
//         Integer status = dayDTO.getStatus();
//         String remark = dayDTO.getRemark();
//         String unionid = (String) req.getSession().getAttribute("unionid");

//         return CommonResult.succeeded(iAppWorkdayService.setWorkDay(day, status, remark, unionid));
//     }

//     @Operation(summary = "批量开启/关闭日期开放")
//     @PostMapping(value = "/setWorkDays")
//     public CommonResult<?> setWorkDays(@RequestBody WorkDayDTO dayDTO, HttpServletRequest req)
//             throws UnsupportedEncodingException {
//         String days = dayDTO.getDays();
//         Integer status = dayDTO.getStatus();
//         String remark = dayDTO.getRemark();

//         String unionid = (String) req.getSession().getAttribute("unionid");

//         String[] day_array = days.split(",");
//         for (int i = 0; i < day_array.length; i++) {
//             String day = day_array[i];
//             iAppWorkdayService.setWorkDay(day, status, remark, unionid);
//         }
//         return CommonResult.succeeded("");
//     }

// }
