package com.spup.admin.controller.others;


import javax.annotation.Resource;

import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.spup.commons.api.CommonResult;
import com.spup.core.dto.QuestionnaireListRequest;
import com.spup.core.service.ICommQuestionnaireAnswerService;
import com.spup.core.service.ICommQuestionnaireService;
import com.spup.data.entity.CommQuestionnaireAnswer;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "问卷管理")
@RestController
@RequestMapping(value = "/questionnaire")
public class QuestionnaireController {
    @Resource
    private ICommQuestionnaireService iCommQuestionnaireService;
    @Resource
    private ICommQuestionnaireAnswerService iCommQuestionnaireAnswerService;
    @Resource
    private ObjectMapper objectMapper;

    @Operation(summary = "问卷列表(分页)")
    @PostMapping(value="/listByPage")
    public CommonResult<?> listByPage (@RequestBody QuestionnaireListRequest param) {
        return CommonResult.succeeded(iCommQuestionnaireService.getPageList(param));
    }

    @Operation(summary = "查看问卷填报（分页）")
    @GetMapping(value={"/viewAnswer/"})
    public CommonResult<?> viewAnswer (@PathVariable Long questionnaireId,QuestionnaireListRequest param) {
        if(questionnaireId==null){
            questionnaireId = 1L;
        }
        if(param.getQuestionnaireId()==null){
            param.setQuestionnaireId(questionnaireId);
        }
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }
    @Operation(summary = "查看问卷填报（分页）")
    @PostMapping(value={"/viewAnswer/{questionnaireId}"})
    public CommonResult<?> viewAnswer2 (@PathVariable Long questionnaireId, @RequestBody QuestionnaireListRequest param) {
        if(questionnaireId==null){
            questionnaireId = 1L;
        }
        if(param.getQuestionnaireId()==null){
            param.setQuestionnaireId(questionnaireId);
        }
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }

    @Operation(summary = "查看问卷填报（分页）")
    @GetMapping(value={"/viewAnswer"})
    public CommonResult<?> viewAnswer3 (@RequestBody QuestionnaireListRequest param) {
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }
    @Operation(summary = "查看问卷填报详情")
    @GetMapping(value="/viewAnswerDetail/{answerId}")
    public CommonResult<?> viewAnswerDetail (@PathVariable long answerId) throws JsonProcessingException {

        return CommonResult.succeeded(iCommQuestionnaireAnswerService.getAnswerDetail(answerId));
    }
}
