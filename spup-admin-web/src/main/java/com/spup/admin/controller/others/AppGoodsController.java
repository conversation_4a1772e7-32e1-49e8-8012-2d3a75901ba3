package com.spup.admin.controller.others;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.spup.admin.service.IAppSurroundingGoodsService;
import com.spup.commons.api.CommonResult;
import com.spup.core.controller.BaseController;
import com.spup.core.dto.GoodsListRequest;
import com.spup.data.entity.AppSurroundingGoods;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "文创商品管理")
@RestController
@RequestMapping(value = "/goods")
public class AppGoodsController extends BaseController {
    @Resource
    private IAppSurroundingGoodsService iAppSurroundingGoodsService;


    @Operation(summary = "查询文创商品")
    @GetMapping(value="/listByPage")
    public CommonResult<?> listByPage (GoodsListRequest param) {
        logRequest("查询文创商品", param);
        return success(iAppSurroundingGoodsService.getListByPage(param));
    }

    @Operation(summary = "创建文创商品")
    @PostMapping(value="/createGoods")
    public CommonResult<?> createActivity (@RequestBody AppSurroundingGoods goods){
        logRequest("创建文创商品", goods);
        String unionid = requireAuthentication();
        iAppSurroundingGoodsService.create(goods,unionid);
        return success(goods);
    }

    @Operation(summary = "删除文创商品")
    @GetMapping(value="/deleteGoods/{id}")
    public CommonResult<?> deleteActivity (@PathVariable Long id){
        logRequest("删除文创商品", id);
        String openid = getCurrentOpenid();
        return success(iAppSurroundingGoodsService.delete(id,openid));
    }

    @Operation(summary = "更新文创商品", description = "id为必传字段，此接口支持编辑接口，上架，下架（goodsStatus:1上架；2下架）")
    @PostMapping(value="/updateGoods")
    public CommonResult<?> updateActivity (@RequestBody AppSurroundingGoods goods) {
        logRequest("更新文创商品", goods);
        String openid = getCurrentOpenid();

        return success(iAppSurroundingGoodsService.update(goods,openid));
    }

    @Operation(summary = "查看文创商品")
    @GetMapping(value="/viewGoods/{id}")
    public CommonResult<?> viewManageUser (@PathVariable Long id)  {

        return CommonResult.succeeded(iAppSurroundingGoodsService.view(id));
    }

}
