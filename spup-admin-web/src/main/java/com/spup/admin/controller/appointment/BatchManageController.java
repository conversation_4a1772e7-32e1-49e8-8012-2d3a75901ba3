package com.spup.admin.controller.appointment;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.spup.admin.dto.AppBatchDTO;
import com.spup.commons.api.CommonResult;
import com.spup.core.service.IAppBatchService;
import com.spup.data.entity.appointment.AppBatch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "场次名额设定")
@RestController
@RequestMapping(value = "/manage/batch")
public class BatchManageController {
    @Resource
    private IAppBatchService iAppBatchService;

    @Operation(summary = "查询场次")
    @GetMapping(value = "/query/{batchCategory}/{queryDate}")
    public CommonResult<List<AppBatch>> query(@PathVariable Byte batchCategory,
            @PathVariable String queryDate) {
        return CommonResult.succeeded(iAppBatchService.getListByDate(batchCategory, queryDate));
    }

    @Operation(summary = "名额保存")
    @PostMapping(value = "/save")
    public CommonResult<String> save(@RequestBody List<AppBatchDTO> batchDTOList, HttpServletRequest req) {
        String openid = (String) req.getSession().getAttribute("openid");
        for (int i = 0; i < batchDTOList.size(); i++) {
            AppBatchDTO appBatchDTO = batchDTOList.get(i);
            AppBatch batch = iAppBatchService.getByNo(appBatchDTO.getBatchNo(), appBatchDTO.getBatchCategory());
            Integer ticketTotal = batch.getTicketTotal();
            Integer ticketRemaining = batch.getTicketRemaining();
            if (appBatchDTO.getTicketTotal() < ticketTotal) {
                return CommonResult.failed("名额不可小于原来的名额数");
            }
            batch.setTicketTotal(appBatchDTO.getTicketTotal());
            batch.setTicketRemaining(ticketRemaining + appBatchDTO.getTicketTotal() - ticketTotal);
            batch.setUpdateBy(openid);
            iAppBatchService.save(batch);
        }
        return CommonResult.succeeded("");
    }
}
