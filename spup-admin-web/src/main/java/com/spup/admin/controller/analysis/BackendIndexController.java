package com.spup.admin.controller.analysis;


import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.admin.dto.HomeAnalysisExhibitionPermanentResponse;
import com.spup.admin.dto.HomeAnalysisExhibitionTemporaryResponse;
import com.spup.admin.dto.HomeAnalysisTotalResponse;
import com.spup.admin.dto.PersonalOfflineListRequest;
import com.spup.admin.dto.SuborderQueryRequest;
import com.spup.admin.service.analysis.IMpDatacubeArticleSummaryService;
import com.spup.admin.service.analysis.IMpDatacubeUserSummaryService;
import com.spup.admin.service.appointment.IAppAppointmentItemSuborderService;
import com.spup.admin.service.appointment.IAppAppointmentPersonalOfflineService;
import com.spup.admin.service.appointment.IAppAppointmentSuborderService;
import com.spup.commons.api.CommonResult;
import com.spup.core.dto.AppAdminTeamOrderListRequest;
import com.spup.core.dto.DateQueryRequest;
import com.spup.core.dto.QuestionnaireListRequest;
import com.spup.core.service.IAppAppointmentTeamOrderService;
import com.spup.core.service.IAppVisitGuideService;
import com.spup.core.service.ICommQuestionnaireAnswerService;
import com.spup.data.dao.sys.AppOperateLogDao;
import com.spup.data.entity.AppVisitGuide;
import com.spup.data.entity.CommQuestionnaireAnswer;
import com.spup.data.entity.appointment.AppAppointmentItemSuborder;
import com.spup.data.entity.appointment.AppAppointmentPersonalOffline;
import com.spup.data.entity.appointment.AppAppointmentSuborder;
import com.spup.data.entity.appointment.AppAppointmentTeamOrder;
import com.spup.enums.OrderCategoryEnum;
import com.spup.enums.OrderStatusEnum;
import com.spup.enums.TeamOrderStatusEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;


@Tag(name = "后台首页")
@RestController
@RequestMapping(value = "/index/data")
public class BackendIndexController {
    @Resource
    private IAppVisitGuideService iAppVisitGuideService;
    @Resource
    private IAppAppointmentSuborderService iAppAppointmentSuborderService;
    @Resource
    private IAppAppointmentPersonalOfflineService iPersonalOfflineService;
    @Resource
    private IAppAppointmentItemSuborderService iItemSuborderService;
    @Resource
    private IAppAppointmentTeamOrderService iTeamOrderService;
    @Resource
    private ICommQuestionnaireAnswerService answerService;
    @Resource
    private IMpDatacubeArticleSummaryService articleSummaryService;
    @Resource
    private IMpDatacubeUserSummaryService userSummaryService;
    @Resource
    private AppOperateLogDao operateLogDao;
    @Resource
    private ObjectMapper objectMapper;

    @Operation(summary = "数据总览")
    @PostMapping(value="/total")
    public CommonResult<?> total ()  {
        LocalDate start = LocalDate.of(LocalDate.now().getYear(),1,1);
        LocalDate end = LocalDate.now();
        HomeAnalysisTotalResponse result = new HomeAnalysisTotalResponse();

        List<AppAppointmentPersonalOffline> offlineList = getOffline(start,end);
        List<AppAppointmentTeamOrder> teamOrderList = getTeamOrderList(start,end);

        //个人参观 线上核销数据+线下个人登记数据
        long countTicket = countTicket(start,end);
        countTicket += offLineTicket(offlineList);

        //展项数据 , 线上核销+线下个人登记+线下团队登记+线上团队登记
        long countItem = countItem(start,end); //线上核销数据
        countItem += offLineItem(offlineList); //线下个人登记
        countItem += countTeamJoinItemPersons(teamOrderList); //线下团队登记+线上团队登记

        //团队人次数据，线上核销人次+线下登记人次
        long countTeamPersons = countTeamPersons(teamOrderList);

        result.setTicketTotal(countTicket);
        result.setItemTicketTotal(countItem);
        result.setTeamPersonsTotal(countTeamPersons);
        result.setMpArticleTotal(articleSummaryService.getArticleTotal(start,end));
        result.setMpArticleReadTotal(articleSummaryService.getArticleReadTotal(start,end));
        result.setMpUserTotal(userSummaryService.getUserTotal());
        result.setVisitUserTotal(operateLogDao.countByOperateTimeBetween(start.atTime(0,0,0),
                                                                     end.atTime(23,59,59)));
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "常设展预约数据")
    @PostMapping(value="/exhibition/permanent")
    public CommonResult<?> exhibitionPermanent (@RequestBody DateQueryRequest queryParam)  {
        HomeAnalysisExhibitionPermanentResponse result = new HomeAnalysisExhibitionPermanentResponse();
        queryParam.setStartDate(queryParam.getStartDate()==null?LocalDate.now():queryParam.getStartDate());
        queryParam.setEndDate(queryParam.getEndDate()==null?LocalDate.now():queryParam.getEndDate());

        List<AppAppointmentSuborder> suborderList = iAppAppointmentSuborderService.findByDateBetween(queryParam.getStartDate(), queryParam.getEndDate());

        //查询个人线上预约总数
        long countTicketTotal = suborderList.size();

        //查询个人线上核销总数
        long countTicketCheckIn = suborderList.stream()
                                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode())
                                .count();
        //线下登记人数
        PersonalOfflineListRequest param = new PersonalOfflineListRequest();
        BeanUtils.copyProperties(queryParam,param);
        List<AppAppointmentPersonalOffline> offlineList = getOffline(param);
        long offlinePersons = offlineList.stream().mapToLong(AppAppointmentPersonalOffline::getPersonNum).sum();

        List<AppAppointmentItemSuborder> itemList = iItemSuborderService.findByDateBetween(queryParam.getStartDate(), queryParam.getEndDate());
        long countItemTotal = itemList.size();
        long countItemCheckIn = itemList.stream()
                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode())
                .count();

        long offlineItemPersons = offlineList.stream().mapToLong(AppAppointmentPersonalOffline::getVisitFypdBatch).sum()*7;

        List<AppAppointmentTeamOrder> teamOrderList = iTeamOrderService.getTeamOrderByDate(
                queryParam.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
                , queryParam.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        List<AppAppointmentTeamOrder> teamOnlineOrderList =
                teamOrderList.stream()
                .filter(teamOrder -> teamOrder.getOrderCategory().byteValue() != OrderCategoryEnum.EXHIBITION_TEAM.getCode())
                .filter(teamOrder -> teamOrder.getMethod() == AppAppointmentTeamOrder.MethodEnum.ONLINE)
                .collect(Collectors.toList());


        long countTeamTotal = teamOnlineOrderList.size();

        long countTeamPersonsTotal = teamOnlineOrderList.stream().mapToLong(AppAppointmentTeamOrder::getVisitorsNum).sum();

        teamOnlineOrderList = teamOnlineOrderList.stream()
                .filter(teamOrder -> teamOrder.getOrderStatus() == TeamOrderStatusEnum.FINISHED.getCode()
                                    || teamOrder.getOrderStatus() == TeamOrderStatusEnum.CONFIRM.getCode())
                .collect(Collectors.toList());

        long countOnlineTeamCheckinTotal = teamOnlineOrderList.size();
        long countOnlineTeamCheckinPersonsTotal =
                teamOnlineOrderList.stream()
                        .mapToLong(AppAppointmentTeamOrder::getVisitorsNum).sum();

        List<AppAppointmentTeamOrder> teamOfflineOrderList =
                teamOrderList.stream()
                        .filter(teamOrder -> teamOrder.getOrderCategory().byteValue() != OrderCategoryEnum.EXHIBITION_TEAM.getCode())
                        .filter(teamOrder -> teamOrder.getMethod() == AppAppointmentTeamOrder.MethodEnum.OFFLINE)
                        .collect(Collectors.toList());
        long countOfflineTeamTotal = teamOfflineOrderList.size();
        long countOfflineTeamPersonsTotal = teamOfflineOrderList.stream().mapToLong(AppAppointmentTeamOrder::getVisitorsNum).sum();

        result.setOnlineTicketTotal(countTicketTotal);
        result.setOnlineCheckinTotal(countTicketCheckIn);
        result.setOfflineTicketTotal(offlinePersons);
        result.setTicketTotal(result.getOnlineCheckinTotal()+result.getOfflineTicketTotal());

        result.setOnlineItemTicketTotal(countItemTotal);
        result.setOnlineItemCheckinTotal(countItemCheckIn);
        result.setOfflineItemTicketTotal(offlineItemPersons);
        result.setItemTicketTotal(result.getOnlineItemCheckinTotal()+result.getOfflineItemTicketTotal());

        result.setOnlineTeamTicketTotal(countTeamTotal);
        result.setOnlineTeamPersonsTotal(countTeamPersonsTotal);
        result.setOnlineTeamCheckinTotal(countOnlineTeamCheckinTotal);
        result.setOnlineTeamCheckinPersonsTotal(countOnlineTeamCheckinPersonsTotal);

        result.setOfflineTeamTicketTotal(countOfflineTeamTotal);
        result.setOfflineTeamPersonsTotal(countOfflineTeamPersonsTotal);

        result.setTeamTotal(result.getOnlineTeamCheckinTotal()+result.getOfflineTeamTicketTotal());
        result.setTeamPersonsTotal(result.getOnlineTeamCheckinPersonsTotal()+result.getOfflineTeamPersonsTotal());
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "特展预约数据")
    @PostMapping(value="/exhibition/temporary")
    public CommonResult<?> exhibitionTemporary (@RequestBody DateQueryRequest queryParam)  {
        HomeAnalysisExhibitionTemporaryResponse result = new HomeAnalysisExhibitionTemporaryResponse();
        List<AppAppointmentTeamOrder> teamOrderList = iTeamOrderService.getTeamOrderByDate(
                queryParam.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
                , queryParam.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        teamOrderList =
                teamOrderList.stream()
                        .filter(teamOrder -> teamOrder.getOrderCategory().byteValue() == OrderCategoryEnum.EXHIBITION_TEAM.getCode())
                            .collect(Collectors.toList());
        long onlineTicketTotal = teamOrderList.size();
        long onlineTeamPersonsTotal = teamOrderList.stream().mapToLong(AppAppointmentTeamOrder::getVisitorsNum).sum();
        List<AppAppointmentTeamOrder> onlineTeamCheckinList = teamOrderList.stream()
                .filter(teamOrder -> teamOrder.getOrderStatus() == TeamOrderStatusEnum.FINISHED.getCode()
                                    || teamOrder.getOrderStatus() == TeamOrderStatusEnum.CONFIRM.getCode())
                .collect(Collectors.toList());
        long onlineTeamCheckinTotal = onlineTeamCheckinList.size();
        long onlineTeamCheckinPersonsTotal = onlineTeamCheckinList.stream().mapToLong(AppAppointmentTeamOrder::getVisitorsNum).sum();
        result.setOnlineTeamTicketTotal(onlineTicketTotal);
        result.setOnlineTeamPersonsTotal(onlineTeamPersonsTotal);
        result.setOnlineTeamCheckinTotal(onlineTeamCheckinTotal);
        result.setOnlineTeamCheckinPersonsTotal(onlineTeamCheckinPersonsTotal);
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "线上导览浏览数据")
    @PostMapping(value="/guide")
    public CommonResult<?> guide ()  {
        List<AppVisitGuide> result = iAppVisitGuideService.findAllSortByPageViews(10);
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "入馆总人次")
    @PostMapping(value="/inTotal")
    public CommonResult<?> inTotal (@RequestBody DateQueryRequest queryParam)  {
        List<AppAppointmentSuborder> suborderList = iAppAppointmentSuborderService.findByDateBetween(queryParam.getStartDate(), queryParam.getEndDate());

        //个人参观：常设展个人入馆人次+常设展线下登记人次+特展个人入馆总人次（0）+特展线下登记人次（0）
        long countTicketCheckIn = suborderList.stream()
                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode())
                .count();

        PersonalOfflineListRequest param = new PersonalOfflineListRequest();
        BeanUtils.copyProperties(queryParam,param);
        List<AppAppointmentPersonalOffline> offlineList = getOffline(param);
        long offlinePersons = offlineList.stream().mapToLong(AppAppointmentPersonalOffline::getPersonNum).sum();

        long countPersonalTotal = countTicketCheckIn+offlinePersons+0+0;

        //团队参观：常设展团队入馆总人次+常设展线下登记人次+特展个人入馆总人次（0）+特展线下登记人次（0）
        List<AppAppointmentTeamOrder> teamOrderList = iTeamOrderService.getTeamOrderByDate(
                queryParam.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
                , queryParam.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        List<AppAppointmentTeamOrder> teamOnlineOrderList =
                teamOrderList.stream()
                        .filter(teamOrder -> teamOrder.getOrderStatus()== TeamOrderStatusEnum.CONFIRM.getCode()
                                || teamOrder.getOrderStatus()== TeamOrderStatusEnum.FINISHED.getCode() )
                        .collect(Collectors.toList());

        long countTeamPersonsTotal = teamOnlineOrderList.stream().mapToLong(AppAppointmentTeamOrder::getVisitorsNum).sum();

        //展项：
        List<AppAppointmentItemSuborder> itemList = iItemSuborderService.findByDateBetween(queryParam.getStartDate(), queryParam.getEndDate());
        long countItemCheckIn = itemList.stream()
                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode())
                .count();

        Map<String,Long> result = new HashMap<>();
        result.put("countPersonalTotal",countPersonalTotal);
        result.put("countTeamPersonsTotal",countTeamPersonsTotal);
        result.put("countItemCheckIn",countItemCheckIn);
        return CommonResult.succeeded(result);
    }

    @Operation(summary = "问卷调查")
    @PostMapping(value="/feedback")
    public CommonResult<?> feedback ()  {
/*
        List<AppComments> result = iAppCommentsService.findAll(3);
*/
        QuestionnaireListRequest queryParam = new QuestionnaireListRequest();
        List<CommQuestionnaireAnswer> allAnswerList = answerService.getAllAnswer(1L, queryParam);
        List<CommQuestionnaireAnswer> result = allAnswerList.stream().limit(3).collect(Collectors.toList());
        return CommonResult.succeeded(result);
    }

    private long countTicket(LocalDate start,LocalDate end){
        SuborderQueryRequest suborderQueryParam = new SuborderQueryRequest();
        suborderQueryParam.setSuborderStatus(OrderStatusEnum.FINISHED.getCode());
        suborderQueryParam.setStartDate(start);
        suborderQueryParam.setEndDate(end);
        return countTicket(suborderQueryParam);
    }
    private long countTicket(SuborderQueryRequest suborderQueryParam){
        return  iAppAppointmentSuborderService.countByStatus(suborderQueryParam);
    }

    private long countItem(LocalDate start,LocalDate end){
        return iItemSuborderService.countByStatus(OrderStatusEnum.FINISHED.getCode(),start,end);
    }

    private long countTeamPersons(List<AppAppointmentTeamOrder> teamOrderList){
        return teamOrderList.stream()
                .filter(teamOrder -> teamOrder.getOrderStatus()==TeamOrderStatusEnum.CONFIRM.getCode() || teamOrder.getOrderStatus()==TeamOrderStatusEnum.FINISHED.getCode())
                .mapToLong(AppAppointmentTeamOrder::getVisitorsNum).sum();
    }
    private List<AppAppointmentTeamOrder> getTeamOrderList (LocalDate start,LocalDate end){
        AppAdminTeamOrderListRequest queryParamOfTeam = new AppAdminTeamOrderListRequest();
        queryParamOfTeam.setPageNum(1);
        queryParamOfTeam.setPageSize(Integer.MAX_VALUE);
        queryParamOfTeam.setStartDate(start);
        queryParamOfTeam.setEndDate(end);
        Page<AppAppointmentTeamOrder> teamOrdersPage = iTeamOrderService.getAdminTeamOrderList(queryParamOfTeam);
        return teamOrdersPage.getContent();
    }
    private long countTeamJoinItemPersons(List<AppAppointmentTeamOrder> teamOrderList){
        return teamOrderList.stream()
                .filter(teamOrder -> teamOrder.getOrderStatus()==TeamOrderStatusEnum.CONFIRM.getCode() || teamOrder.getOrderStatus()==TeamOrderStatusEnum.FINISHED.getCode())
                .map(teamOrder -> {
                    ObjectNode supplyInfo = teamOrder.getSupplyInfo();
                    if(supplyInfo==null){
                        return 0;
                    }
                    JsonNode jsonNode = supplyInfo.get("fypdBatchNum");
                    if(jsonNode==null){
                        return 0;
                    }
                    return  jsonNode.intValue() * 7;
                })
                .mapToLong(Integer::longValue).sum();
    }

    private long offLineTicket(List<AppAppointmentPersonalOffline> offlineList){
        return offlineList.stream().mapToLong(AppAppointmentPersonalOffline::getPersonNum).sum();
    }

    private long offLineItem(List<AppAppointmentPersonalOffline> offlineList){
        return offlineList.stream().mapToLong(AppAppointmentPersonalOffline::getVisitFypdBatch).sum()*7;
    }

    private List<AppAppointmentPersonalOffline> getOffline(LocalDate start,LocalDate end){
        PersonalOfflineListRequest queryParam = new PersonalOfflineListRequest();
        queryParam.setPageNum(1);
        queryParam.setPageSize(Integer.MAX_VALUE);
        queryParam.setStartDate(start);
        queryParam.setEndDate(end);
        return getOffline(queryParam);
    }
    private List<AppAppointmentPersonalOffline> getOffline(PersonalOfflineListRequest queryParam){
        Page<AppAppointmentPersonalOffline> personalOfflinePage = iPersonalOfflineService.getList(queryParam);
        return personalOfflinePage.getContent();
    }



}
