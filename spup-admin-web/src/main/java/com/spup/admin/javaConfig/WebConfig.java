package com.spup.admin.javaConfig;

import com.spup.core.config.BaseWebConfig;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

/**
 * Admin Module Web Configuration
 * Extends BaseWebConfig with admin-specific settings
 */
@Configuration
public class WebConfig extends BaseWebConfig {
    
    @Override
    protected void addModuleSpecificResourceHandlers(ResourceHandlerRegistry registry) {
        // Add admin-specific resource handlers here if needed
        // Example: registry.addResourceHandler("/admin-assets/**").addResourceLocations("classpath:/admin-assets/");
    }
}
