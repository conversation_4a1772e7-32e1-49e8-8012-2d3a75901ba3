package com.huangdou.activity.dto;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.spup.db.entity.activity.ActivityRound;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

public class ActivityRoundEntityDTO {
    private Long id;
    private String actRoundId;
    private String activityId;
    @NotBlank(message = "场次名称不能为空")
    private String actRoundInfo;
    @NotNull(message = "报名开始时间不能为空")
    private LocalDateTime actRoundSubmitStartDateTime;
    @NotNull(message = "报名结束时间不能为空")
    private LocalDateTime actRoundSubmitEndDateTime;
    @NotNull(message = "场次开始时间不能为空")
    private LocalDateTime actRoundStartDateTime;
    @NotNull(message = "场次结束时间不能为空")
    private LocalDateTime actRoundEndDateTime;

    private Integer actRoundSubmitNumber = 0;
    @NotNull(message = "最大报名人数不能为空")
    private Integer actRoundMaxSubmitNum;
    @Enumerated(EnumType.STRING)
    private ActivityRound.ActRoundStatusEnum status = ActivityRound.ActRoundStatusEnum.READY;
    @Enumerated(EnumType.STRING)
    private ActivityRound.ActRoundTypeEnum type = ActivityRound.ActRoundTypeEnum.NORMAL;
    private ObjectNode otherInfo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActRoundId() {
        return actRoundId;
    }

    public void setActRoundId(String actRoundId) {
        this.actRoundId = actRoundId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getActRoundInfo() {
        return actRoundInfo;
    }

    public void setActRoundInfo(String actRoundInfo) {
        this.actRoundInfo = actRoundInfo;
    }

    public LocalDateTime getActRoundSubmitStartDateTime() {
        return actRoundSubmitStartDateTime;
    }

    public void setActRoundSubmitStartDateTime(LocalDateTime actRoundSubmitStartDateTime) {
        this.actRoundSubmitStartDateTime = actRoundSubmitStartDateTime;
    }

    public LocalDateTime getActRoundSubmitEndDateTime() {
        return actRoundSubmitEndDateTime;
    }

    public void setActRoundSubmitEndDateTime(LocalDateTime actRoundSubmitEndDateTime) {
        this.actRoundSubmitEndDateTime = actRoundSubmitEndDateTime;
    }

    public LocalDateTime getActRoundStartDateTime() {
        return actRoundStartDateTime;
    }

    public void setActRoundStartDateTime(LocalDateTime actRoundStartDateTime) {
        this.actRoundStartDateTime = actRoundStartDateTime;
    }

    public LocalDateTime getActRoundEndDateTime() {
        return actRoundEndDateTime;
    }

    public void setActRoundEndDateTime(LocalDateTime actRoundEndDateTime) {
        this.actRoundEndDateTime = actRoundEndDateTime;
    }

    public Integer getActRoundSubmitNumber() {
        return actRoundSubmitNumber;
    }

    public void setActRoundSubmitNumber(Integer actRoundSubmitNumber) {
        this.actRoundSubmitNumber = actRoundSubmitNumber;
    }

    public Integer getActRoundMaxSubmitNum() {
        return actRoundMaxSubmitNum;
    }

    public void setActRoundMaxSubmitNum(Integer actRoundMaxSubmitNum) {
        this.actRoundMaxSubmitNum = actRoundMaxSubmitNum;
    }

    public ActivityRound.ActRoundStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ActivityRound.ActRoundStatusEnum status) {
        this.status = status;
    }

    public ActivityRound.ActRoundTypeEnum getType() {
        return type;
    }

    public void setType(ActivityRound.ActRoundTypeEnum type) {
        this.type = type;
    }

    public ObjectNode getOtherInfo() {
        return otherInfo;
    }

    public void setOtherInfo(ObjectNode otherInfo) {
        this.otherInfo = otherInfo;
    }
}
