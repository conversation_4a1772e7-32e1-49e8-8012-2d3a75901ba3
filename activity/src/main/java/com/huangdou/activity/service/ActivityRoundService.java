package com.huangdou.activity.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.huangdou.activity.dto.ActivityRoundCheckinDTO;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.activity.Activity;
import com.spup.db.entity.activity.ActivityRound;
import com.spup.db.entity.activity.ActivitySubmitCustomer;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

public interface ActivityRoundService {
     CommonResult<String> submit(List<ActivitySubmitCustomer> activityUser, String unionid, ActivityRound actRound) throws JsonProcessingException;
     Activity.ActStatusEnum getFreshedActStatus(String activId);
     void updateActivStatus(List<Activity> activList);
     ActivityRound.ActRoundStatusEnum getFreshActRoundStatus(String actRoundId);
     void updateActRoundStatus(List<ActivityRound> actRoundList);
     List<ActivityRoundCheckinDTO> getActRoundCheckInDto(String unionid);
     boolean canCustomerSubmitActRound(String roundId, String unionid);
     boolean canActRoundCancel(List<ActivitySubmitCustomer> subRecordList,ActivityRound actRound);
     boolean canActRoundCheckin(ActivityRound actRound);
     CommonResult<ActivitySubmitCustomer> checkInAction(@PathVariable String actRoundId, String unionid);
     CommonResult<String> cancelActRound(List<ActivitySubmitCustomer> subRecordList, ActivityRound actRound) throws JsonProcessingException;
}
