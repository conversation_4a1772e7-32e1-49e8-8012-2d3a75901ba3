package com.huangdou.activity.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.activity.Activity;
import com.spup.db.entity.activity.ActivityRound;
import com.spup.db.entity.activity.ActivitySubmitCustomer;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class ActivityRoundServiceChildImpl extends ActivityRoundServiceImpl {

    @Override
    @Transactional
    public CommonResult<String> submit(List<ActivitySubmitCustomer> activityCustomerList, String unionid,
            ActivityRound actRound) throws JsonProcessingException {

        String submitId = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS"));

        if (!canCustomerSubmitActRound(actRound.getActRoundId(), unionid)) {
            return CommonResult.failed("请勿重复报名");
        }
        long countChild = activityCustomerList.stream()
                .filter(subCustomer -> subCustomer.getAge() < 18)
                .count();
        activityCustomerList.stream().filter(subCustomer -> subCustomer.getAge() >= 18).count();

        if (countChild < 1) {
            return CommonResult.failed("未提交未成年人信息");
        }
        // 名额，可根据条件判断，这里指定1
        int addSubmitNumber = 1;
        if (actRound.getActRoundSubmitNumber() + addSubmitNumber > actRound.getActRoundMaxSubmitNum()) {
            return CommonResult.failed("名额不足");
        }

        Optional<Activity> activityOptional = actDao.getByActivityId(actRound.getActivityId());
        if (!activityOptional.isPresent()) {
            return CommonResult.failed("活动不存在");
        }

        int update = actRoundDao.updateSubmitNumber(addSubmitNumber, actRound.getId(), addSubmitNumber,
                addSubmitNumber);
        if (update == 0) {
            return CommonResult.failed("报名失败");
        }
        activityCustomerList.stream().forEach(customer -> {
            customer.setUnionid(unionid);
            customer.setStatus(ActivitySubmitCustomer.SubmitCustomerStatusEnum.SUBMITTED);
            customer.setType(parseCustomerTypeByAge(customer.getAge()));
            customer.setSubmitId(submitId);
            actCustomerDao.save(customer);
        });

        boolean needTicket = true;
        if (needTicket) {
            String orderNo = orderService.saveFromActivity(actRound, activityCustomerList, unionid);
            activityCustomerList.stream().forEach(customer -> {
                Optional<ActivitySubmitCustomer> customerOptional = actCustomerDao.findById(customer.getId());
                if (customerOptional.isPresent()) {
                    ActivitySubmitCustomer customer1 = customerOptional.get();
                    customer1.setCreateBy(orderNo);
                    actCustomerDao.save(customer1);
                }

            });
        }
        updateActRoundStatus(Collections.singletonList(actRound));

        return CommonResult.succeeded("报名成功");
    }
}
