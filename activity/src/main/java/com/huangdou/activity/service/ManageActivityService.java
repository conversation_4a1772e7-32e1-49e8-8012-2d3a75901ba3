package com.huangdou.activity.service;


import com.huangdou.activity.dto.ActivityRoundCheckinPojo;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.activity.Activity;
import com.spup.db.entity.activity.ActivityRound;

public interface ManageActivityService {
    CommonResult<?> checkActivityValid(Activity activity);
    CommonResult<?> checkActivityRoundValid(ActivityRound round);
    void checkInAction(ActivityRoundCheckinPojo checkinPojo);
}
