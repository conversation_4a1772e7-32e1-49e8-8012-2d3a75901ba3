package com.huangdou.activity.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huangdou.activity.dto.ActivityRoundCheckinPojo;
import com.huangdou.activity.dto.ActivityRoundEntityDTO;
import com.huangdou.activity.service.ManageActivityService;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.dao.activity.ActivityDao;
import com.spup.db.dao.activity.ActivityRoundDao;
import com.spup.db.dao.activity.ActivitySubmitCustomerDao;
import com.spup.db.entity.activity.Activity;
import com.spup.db.entity.activity.ActivityRound;
import com.spup.db.entity.activity.ActivitySubmitCustomer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Api(tags = "管理活动" )
@RestController
@RequestMapping(value="/manage")
public class ActivityManageController extends BaseController {

    @Resource
    private ActivityDao actDao;
    @Resource
    private ActivityRoundDao actRoundDao;
    @Resource
    private ActivitySubmitCustomerDao actCustomerDao;
    @Resource
    private ManageActivityService iManageActivityService;
    @Value("") //????没写完
    private String profile;
    @Resource
    private ObjectMapper mapper;

    @ApiOperation(value = "获取活动列表")
    @PostMapping(value="/activity/list")

    public CommonResult<List<Activity>> getActivityList () {
        List<Activity> result = (List<Activity>)actDao.findAll();
        updateActivStatus(result);
        result.sort((act1,act2) -> (int)(act2.getId()-act1.getId()));
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "预览列表")
    @PostMapping(value="/activity/preview/list")
    @Profile("pro")
    public CommonResult<List<Activity>> previewList () {
        List<Activity> result = (List<Activity>)actDao.findAll();
        updateActivStatus(result);
        result = result.stream()
                .filter(act -> act.getStatus() == Activity.ActStatusEnum.PREVIEW)
                .sorted((act1,act2) -> (int)(act2.getId()-act1.getId()))
                .collect(Collectors.toList());
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "预览")
    @PostMapping(value="/activity/preview/{activityId}")
    public CommonResult<List<Activity>> preview (@PathVariable String activityId) {
        List<Activity> result = (List<Activity>)actDao.findAll();
        updateActivStatus(result);
        result = result.stream()
                .filter(act -> act.getStatus() == Activity.ActStatusEnum.PREVIEW)
                .sorted((act1,act2) -> (int)(act2.getId()-act1.getId()))
                .collect(Collectors.toList());
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "后台签到")
    @PostMapping(value="/activityRound/checkin")
    public CommonResult<?> activityRound (@RequestBody ActivityRoundCheckinPojo checkinPojo) {
        iManageActivityService.checkInAction(checkinPojo);
        return CommonResult.succeeded("签到成功");
    }

    @ApiOperation(value = "后台获取活动信息")
    @PostMapping(value = "/activity/{activityId}")
    public CommonResult<Activity> getActivity (@PathVariable String activityId) {
        Optional<Activity> actOpt = actDao.getByActivityId(activityId);
        if(!actOpt.isPresent()){
            return CommonResult.failed("活动不存在");
        }
        Activity result = actOpt.get();
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "后台添加活动")
    @PostMapping(value = "/activity/add")
    public CommonResult<Activity> addActivity(@RequestBody Activity activity) {
        String actId = activity.getActivityId();
        if(StringUtils.hasLength(actId)){
            Optional<Activity> actOpt =  actDao.getByActivityId(actId);
            if(actOpt.isPresent()){
                return CommonResult.failed("活动已存在");
            }
        }
        try {
            Activity result = actDao.save(activity);
            updateActivStatus(Collections.singletonList(result));
            return CommonResult.succeeded(result);
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.failed("活动添加失败");
        }

    }

    @ApiOperation(value = "后台下线活动")
    @PostMapping(value = "/activity/deprecate/{activityId}")
    public CommonResult<?> deprecateActivity(@PathVariable String activityId) {
        Optional<Activity> actOpt = actDao.getByActivityId(activityId);
        if(!actOpt.isPresent()){
            return CommonResult.failed("活动不存在");
        }
        //活动下线，场次无法下线，否则上线时会把部分已下线的场次设置成上线
        Activity activity = actOpt.get();
        activity.setStatus(Activity.ActStatusEnum.DEPRECATED);
        Activity result = actDao.save(activity);
        //updateActivStatus(Collections.singletonList(activity));
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "后台上线活动")
    @PostMapping(value = "/activity/agree/{activityId}")
    public CommonResult<?> agreeActivity(@PathVariable String activityId) {
        Optional<Activity> actOpt = actDao.getByActivityId(activityId);
        if(!actOpt.isPresent()){
            return CommonResult.failed("活动不存在");
        }
        Activity activity = actOpt.get();
        if(activity.getStatus()== Activity.ActStatusEnum.PREVIEW){
            activity.setStartDateTime(LocalDateTime.now());
        }
        activity.setStatus(Activity.ActStatusEnum.READY);
        Activity result = actDao.save(activity);
        result.setStatus(getActStatus(result.getActivityId()));
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "后台删除活动")
    @PostMapping(value = "/activity/remove/{activityId}")
    @Transactional
    public CommonResult<?> removeActivity(@PathVariable String activityId) {
        Optional<Activity> activityOpt = actDao.getByActivityId(activityId);
        if(!activityOpt.isPresent()){
            return CommonResult.failed("活动不存在");
        }
        Activity activity = activityOpt.get();
        actDao.delete(activity);
        List<ActivityRound> actRoundList = actRoundDao.findByActivityId(activity.getActivityId());
        actRoundList.forEach(round -> {
            actRoundDao.delete(round);
            List<ActivitySubmitCustomer> customerList = actCustomerDao.findByActRoundId(round.getActRoundId());
            actCustomerDao.deleteAll(customerList);
        });
        //updateActivStatus(Collections.singletonList(activity));
        return CommonResult.succeeded("活动已删除");
    }

    @ApiOperation(value = "后台更新活动")
    @PostMapping(value = "/activity/update/{activityId}")
    public CommonResult<?> updateActivity(@PathVariable String activityId,@RequestBody Activity activity) {
         Optional<Activity> activityOpt = actDao.getByActivityId(activityId);
         if(!activityOpt.isPresent()){
             return CommonResult.failed("活动不存在");
         }
        Activity result = actDao.save(activity);
        updateActivStatus(Collections.singletonList(result));
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "后台获取场次信息")
    @PostMapping(value = "/activity/activityRound/{activityId}")
    public CommonResult<List<ActivityRoundEntityDTO>> getActivityRoundByActivity (@PathVariable String activityId) {
        Optional<Activity> actOpt = actDao.getByActivityId(activityId);
        if(!actOpt.isPresent()){
            return CommonResult.failed("活动不存在");
        }
        List<ActivityRound> roundList = actRoundDao.findByActivityId(activityId);
        updateActRoundStatus(roundList);
        roundList.sort((act1,act2) -> (int)(act2.getId()-act1.getId()));

        return CommonResult.succeeded(roundList.stream().map(round -> {
            ActivityRoundEntityDTO entityDTO = new ActivityRoundEntityDTO();
            BeanUtils.copyProperties(round,entityDTO);
            try {
                entityDTO.setOtherInfo((ObjectNode) mapper.readTree(round.getOtherInfo()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            return  entityDTO;
        }).collect(Collectors.toList()));
    }

    @ApiOperation("后台获取场次报名情况")
    @PostMapping({"/activityRound/{activityRoundId}"})
    public CommonResult<?> getActivityRoundCustomers(@PathVariable String activityRoundId) {
        Optional<ActivityRound> actRoundOpt = actRoundDao.findByActRoundId(activityRoundId);
        if (!actRoundOpt.isPresent()) {
            return CommonResult.failed("活动场次不存在");
        }
        ActivityRound activityRound = actRoundOpt.get();
        List<ActivityRound> roundList = actRoundDao.findByActivityId(activityRound.getActivityId());
        updateActRoundStatus(roundList);

        List<ActivitySubmitCustomer> result =actCustomerDao.findByActRoundId(activityRound.getActRoundId());
        result.sort((customer1,customer2) -> customer2.getCreateOn().compareTo(customer1.getCreateOn()));
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "后台更新场次信息")
    @PostMapping(value = "/activityRound/update/{actRoundId}")
    public CommonResult<?> updateActivity(@PathVariable String actRoundId, @RequestBody ActivityRoundEntityDTO roundEntityDTO) throws JsonProcessingException {
        if(!StringUtils.hasLength(actRoundId)){
            return CommonResult.failed("提交场次信息错误");
        }
        Optional<ActivityRound> actRoundOpt =  actRoundDao.findByActRoundId(actRoundId);
        if(!actRoundOpt.isPresent()){
            return CommonResult.failed("活动场次不存在");
        }
        ActivityRound actRound = new ActivityRound();
        BeanUtils.copyProperties(roundEntityDTO,actRound);
        actRound.setOtherInfo(mapper.writeValueAsString(roundEntityDTO.getOtherInfo()));

        CommonResult<?> roundCheckResult = iManageActivityService.checkActivityRoundValid(actRound);
        if(!roundCheckResult.getStatus()){
            return roundCheckResult;
        }
        ActivityRound result = actRoundDao.save(actRound);
        updateActRoundStatus(Collections.singletonList(result));
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "后台添加场次")
    @PostMapping(value = "/activityRound/add/{activityId}")
    public CommonResult<?> addActivityRound(@PathVariable String activityId,@Valid @RequestBody ActivityRoundEntityDTO roundEntityDTO) throws JsonProcessingException {
        Optional<Activity> actOpt = actDao.getByActivityId(activityId);
        if (!actOpt.isPresent()) {
            return CommonResult.failed("活动不存在");
        }
        ObjectNode otherInfo = roundEntityDTO.getOtherInfo();
        JsonNode jsonNode = otherInfo.get("needTicket");
        if(jsonNode==null || jsonNode.isEmpty() || jsonNode.intValue() == 1) {
            LocalDateTime startDateTime = roundEntityDTO.getActRoundStartDateTime();
            String time = startDateTime.format(DateTimeFormatter.ofPattern("HHmm"));
            if ("0930".compareTo(time) > 0 ||  "1600".compareTo(time) < 0) {
                return CommonResult.failed("场次时间需在09:30~16:00");
            }
        }
        Activity activity = actOpt.get();
        CommonResult<?> actCheckResult = iManageActivityService.checkActivityValid(activity);
        if(!actCheckResult.getStatus()){
            return actCheckResult;
        }
        ActivityRound round = new ActivityRound();
        BeanUtils.copyProperties(roundEntityDTO,round);
        round.setOtherInfo(mapper.writeValueAsString(roundEntityDTO.getOtherInfo()));

        CommonResult<?> roundCheckResult = iManageActivityService.checkActivityRoundValid(round);
        if(!roundCheckResult.getStatus()){
            return roundCheckResult;
        }
        if (!StringUtils.hasLength(round.getActivityId())) {
            round.setActivityId(activityId);
        }
        String newRoundId = createRoundId(activityId);
        round.setActRoundId(newRoundId);
        ActivityRound result = actRoundDao.save(round);
        updateActRoundStatus(Collections.singletonList(result));
        return CommonResult.succeeded(result);
    }

    private String createRoundId(String activityId) {
        List<ActivityRound> roundList = actRoundDao.findByActivityId(activityId);
        Optional<ActivityRound> maxRoundOpt = roundList.stream()
                .max((round1, round2) -> (int) (round1.getId() - round2.getId()));
        if(!maxRoundOpt.isPresent()){
            return activityId + "01";
        }
        ActivityRound round = maxRoundOpt.get();
        String roundId = round.getActRoundId();
        String roundIndex = roundId.substring(activityId.length());
        int newRountIndex = Integer.parseInt(roundIndex)+1;
        DecimalFormat df = new DecimalFormat("00");
        return activityId +df.format(newRountIndex);
    }

    @ApiOperation(value = "后台删除场次")
    @PostMapping(value = "/activityRound/remove/{actRoundId}")
    public CommonResult<String> removeActivityRound(@PathVariable String actRoundId) {
        ActivityRound actRound = actRoundDao.findByActRoundId(actRoundId).orElseThrow(()-> new IllegalStateException());
        actRoundDao.delete(actRound);
        List<ActivitySubmitCustomer> customerList = actCustomerDao.findByActRoundId(actRound.getActRoundId());
        actCustomerDao.deleteAll(customerList);
        //updateActRoundStatus(Collections.singletonList(actRound));
        return CommonResult.succeeded("活动场次已删除");
    }

    @ApiOperation(value = "后台下线场次")
    @PostMapping(value = "/activityRound/set/offline/{actRoundId}")
    public CommonResult<ActivityRound> deprecateRound(@PathVariable String actRoundId) {
        ActivityRound actRound = actRoundDao.findByActRoundId(actRoundId).orElseThrow(()-> new IllegalStateException());
        actRound.setStatus(ActivityRound.ActRoundStatusEnum.DEPRECATED);
        ActivityRound result = actRoundDao.save(actRound);
        //updateActRoundStatus(Collections.singletonList(actRound));
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "后台上线场次")
    @PostMapping(value = "/activityRound/set/online/{actRoundId}")
    public CommonResult<ActivityRound> agreeRound(@PathVariable String actRoundId) {
        ActivityRound actRound = actRoundDao.findByActRoundId(actRoundId).orElseThrow(()->new IllegalStateException());
        actRound.setStatus(ActivityRound.ActRoundStatusEnum.SUBMITTING);
        actRound.setActRoundSubmitStartDateTime(LocalDateTime.now());
        if (getActRoundStatus(actRound.getActRoundId()) != ActivityRound.ActRoundStatusEnum.SUBMITTING) {
            return CommonResult.failed("本场次暂不可上线");
        }
        ActivityRound result = actRoundDao.save(actRound);
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "场次停止报名")
    @PostMapping(value = "/activityRound/stopSubmit/{actRoundId}")
    public CommonResult<?> stopSubmit(@PathVariable String actRoundId) {
        Optional<ActivityRound> actRoundOpt = actRoundDao.findByActRoundId(actRoundId);
        if(!actRoundOpt.isPresent()){
            return CommonResult.failed("活动场次不存在");
        }
        ActivityRound actRound = actRoundOpt.get();
        actRound.setStatus(ActivityRound.ActRoundStatusEnum.SUBMITPAUSED);
        ActivityRound result = actRoundDao.save(actRound);
        //updateActRoundStatus(Collections.singletonList(actRound));
        return CommonResult.succeeded(result);
    }
    @ApiOperation(value = "场次再次启用报名")
    @PostMapping(value = "/activityRound/startSubmit/{actRoundId}")
    public CommonResult<?> startSubmit(@PathVariable String actRoundId) {
        Optional<ActivityRound> actRoundOpt = actRoundDao.findByActRoundId(actRoundId);
        if(!actRoundOpt.isPresent()){
            return CommonResult.failed("活动场次不存在");
        }
        ActivityRound actRound = actRoundOpt.get();
        actRound.setStatus(ActivityRound.ActRoundStatusEnum.SUBMITTING);
        ActivityRound result = actRoundDao.save(actRound);
        updateActRoundStatus(Collections.singletonList(result));
        return CommonResult.succeeded(result);
    }


}
