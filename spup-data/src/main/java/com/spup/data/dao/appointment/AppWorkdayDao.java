// package com.spup.data.dao.appointment;

// import java.util.List;
// import java.util.Optional;

// import org.springframework.data.jpa.repository.JpaRepository;
// import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
// import org.springframework.stereotype.Repository;

// import com.spup.data.entity.appointment.AppWorkday;

// @Repository
// public interface AppWorkdayDao  extends JpaRepository<AppWorkday, Long> , JpaSpecificationExecutor<AppWorkday> {
//     // List<AppWorkday> findByDayBetweenNoEpsent(String start,String end);

//     List<AppWorkday> findByDayBetween(String start,String end);

//     Optional<AppWorkday> getByDay(String day);
// }
