package com.spup.data.dao;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.spup.data.entity.RoundConfig;

public interface RoundDao extends JpaRepository<RoundConfig, Long> {
    Optional<RoundConfig> findByExhibitionIdAndRoundDate(String exhibitionId,  LocalDate roundDate);
    List<RoundConfig> findByexhibitionIdAndRoundDateBetween(String exhibitionId, LocalDate startDate, LocalDate endDate);
}
