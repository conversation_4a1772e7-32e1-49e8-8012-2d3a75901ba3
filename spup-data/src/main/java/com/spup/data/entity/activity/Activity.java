package com.spup.data.entity.activity;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;
import org.springframework.util.ObjectUtils;

import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "activity_info")
@SQLDelete(sql = "UPDATE activity_info SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class Activity {
    @Id
    // TODO: Verify table has AUTO_INCREMENT before deploying
    // Rollback: Change back to GenerationType.AUTO if issues occur
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @NotEmpty
    private String activityId;
    @NotEmpty
    private String activityName;
    @NotEmpty
    private String picUrl;
    @NotNull
    private String introductionInfo = "";
    @NotNull
    private LocalDateTime startDateTime;
    @NotNull
    private LocalDateTime endDateTime;
    @Enumerated(EnumType.STRING)
    private ActStatusEnum status = ActStatusEnum.PREVIEW;
    @Enumerated(EnumType.STRING)
    private ActTypeEnum type = ActTypeEnum.NORMAL;
    @NotNull
    private Integer deleted = 0;

    private String othersInfo;
    private String createBy;
    private LocalDateTime createOn;
    private String updateBy;
    private LocalDateTime updateOn;

    public enum ActStatusEnum {
        PREVIEW,READY, RUNNING, CLOSED, DEPRECATED;
    }
    public enum ActTypeEnum {
        NORMAL, CHILD,SPECIAL;
    }
    @PrePersist
    public void insert() {
        setCreateOn(LocalDateTime.now());
        setCreateBy(this.getClass().getSimpleName());
        setUpdateOn(LocalDateTime.now());
        setUpdateBy(this.getClass().getSimpleName());
        if (ObjectUtils.isEmpty(this.getActivityId())) {
            setActivityId(getType()+LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmss")));
        }
    }

    @PreUpdate
    public void update() {
        setUpdateOn(LocalDateTime.now());
        setUpdateBy(this.getClass().getSimpleName());
    }



    public String getOthersInfo() {
        return othersInfo;
    }

    public void setOthersInfo(String othersInfo) {
        this.othersInfo = othersInfo;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getIntroductionInfo() {
        return introductionInfo;
    }

    public void setIntroductionInfo(String introductionInfo) {
        this.introductionInfo = introductionInfo;
    }

    public LocalDateTime getStartDateTime() {
        return startDateTime;
    }

    public void setStartDateTime(LocalDateTime startDateTime) {
        this.startDateTime = startDateTime;
    }

    public LocalDateTime getEndDateTime() {
        return endDateTime;
    }

    public void setEndDateTime(LocalDateTime endDateTime) {
        this.endDateTime = endDateTime;
    }

    public ActStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ActStatusEnum status) {
        this.status = status;
    }

    public ActTypeEnum getType() {
        return type;
    }

    public void setType(ActTypeEnum type) {
        this.type = type;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateOn() {
        return createOn;
    }

    public void setCreateOn(LocalDateTime createOn) {
        this.createOn = createOn;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateOn() {
        return updateOn;
    }

    public void setUpdateOn(LocalDateTime updateOn) {
        this.updateOn = updateOn;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    @Override
    public String toString() {
        return "Activity{" +
                "id=" + id +
                ", activityId='" + activityId + '\'' +
                ", activityName='" + activityName + '\'' +
                ", picUrl='" + picUrl + '\'' +
                ", introductionInfo='" + introductionInfo + '\'' +
                ", startDateTime=" + startDateTime +
                ", endDateTime=" + endDateTime +
                ", status=" + status +
                ", type=" + type +
                ", deleted=" + deleted +
                ", othersInfo='" + othersInfo + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createOn=" + createOn +
                ", updateBy='" + updateBy + '\'' +
                ", updateOn=" + updateOn +
                '}';
    }
}
