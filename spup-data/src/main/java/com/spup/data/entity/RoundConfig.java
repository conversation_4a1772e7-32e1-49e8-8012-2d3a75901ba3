package com.spup.data.entity;

import java.time.LocalDate;
import java.time.LocalTime;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.boot.jaxb.mapping.spi.JaxbEntityListener;

import lombok.Getter;
import lombok.Setter;


@Entity
@Table( name ="round_config" )
@SQLDelete(sql = "UPDATE round_config SET deleted=1 WHERE id=?")
@Where(clause = "deleted=0")
@EntityListeners(JaxbEntityListener.class)
@DynamicUpdate
@DynamicInsert
@Getter
@Setter
public class RoundConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String exhibitionId;
    @Enumerated(EnumType.STRING)
    private RoundStatusEnum roundStatus;
    private LocalDate roundDate;

    private String roundId;
    private LocalTime startTime;
    private LocalTime endTime;
    private int deleted = 0;

    public enum RoundStatusEnum {
        open(),
        close;
    }
}
