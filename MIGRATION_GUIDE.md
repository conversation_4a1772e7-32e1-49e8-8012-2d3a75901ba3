# Migration Guide: AUTO to IDENTITY Strategy

## ⚠️ IMPORTANT: Read Before Deploying

This guide helps you safely migrate from `GenerationType.AUTO` to `GenerationType.IDENTITY` for existing tables with data.

## Pre-Migration Checklist

### 1. Check Current Table Structure

Run this query on your database:

```sql
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COLUMN_TYPE,
    EXTRA,
    AUTO_INCREMENT
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN INFORMATION_SCHEMA.TABLES t ON c.TABLE_NAME = t.TABLE_NAME AND c.TABLE_SCHEMA = t.TABLE_SCHEMA
WHERE c.TABLE_SCHEMA = DATABASE() 
  AND c.TABLE_NAME IN ('activity_info', 'activity_round_info', 'activity_submit_customer')
  AND c.COLUMN_NAME = 'id';
```

### 2. Interpret Results

| EXTRA Column | Status | Action Required |
|--------------|--------|-----------------|
| `auto_increment` | ✅ Safe | Proceed with deployment |
| `` (empty) | ⚠️ Risk | Run migration script first |

## Migration Scenarios

### Scenario A: Tables Already Have AUTO_INCREMENT ✅

If your query shows `EXTRA = 'auto_increment'`, you're safe to deploy. Hibernate likely created the tables correctly.

**Why this happens:**
- Hibernate's AUTO strategy often chooses IDENTITY for MySQL
- Tables were created with AUTO_INCREMENT from the beginning
- No migration needed

### Scenario B: Tables Don't Have AUTO_INCREMENT ⚠️

If your query shows `EXTRA = ''`, you need to migrate first.

**Migration Steps:**

1. **Backup Data**
```sql
CREATE TABLE activity_info_backup AS SELECT * FROM activity_info;
CREATE TABLE activity_round_info_backup AS SELECT * FROM activity_round_info;
CREATE TABLE activity_submit_customer_backup AS SELECT * FROM activity_submit_customer;
```

2. **Add AUTO_INCREMENT**
```sql
ALTER TABLE activity_info MODIFY COLUMN id BIGINT NOT NULL AUTO_INCREMENT;
ALTER TABLE activity_round_info MODIFY COLUMN id BIGINT NOT NULL AUTO_INCREMENT;
ALTER TABLE activity_submit_customer MODIFY COLUMN id BIGINT NOT NULL AUTO_INCREMENT;
```

3. **Set Starting Values**
```sql
-- For activity_info
SET @max_id = (SELECT IFNULL(MAX(id), 0) + 1 FROM activity_info);
SET @sql = CONCAT('ALTER TABLE activity_info AUTO_INCREMENT = ', @max_id);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- For activity_round_info
SET @max_id = (SELECT IFNULL(MAX(id), 0) + 1 FROM activity_round_info);
SET @sql = CONCAT('ALTER TABLE activity_round_info AUTO_INCREMENT = ', @max_id);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- For activity_submit_customer
SET @max_id = (SELECT IFNULL(MAX(id), 0) + 1 FROM activity_submit_customer);
SET @sql = CONCAT('ALTER TABLE activity_submit_customer AUTO_INCREMENT = ', @max_id);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
```

4. **Test Insert**
```sql
-- Test with a dummy record
INSERT INTO activity_info (activity_id, activity_name, pic_url, introduction_info, start_date_time, end_date_time, deleted) 
VALUES ('TEST001', 'Test Activity', 'test.jpg', 'Test', NOW(), NOW(), 0);

-- Check if ID was auto-generated
SELECT * FROM activity_info WHERE activity_id = 'TEST001';

-- Clean up
DELETE FROM activity_info WHERE activity_id = 'TEST001';
```

## Rollback Plan

If issues occur after deployment:

### 1. Immediate Rollback (Code)
```java
// Change back to AUTO strategy
@GeneratedValue(strategy = GenerationType.AUTO)
```

### 2. Database Rollback (if needed)
```sql
-- Restore from backup
DROP TABLE activity_info;
RENAME TABLE activity_info_backup TO activity_info;
-- Repeat for other tables
```

### 3. Recreate hibernate_sequence
```sql
CREATE TABLE hibernate_sequence (next_val BIGINT);
INSERT INTO hibernate_sequence VALUES ((SELECT MAX(id) + 1 FROM activity_info));
```

## Testing Checklist

Before deploying to production:

- [ ] Run structure check query
- [ ] Backup all affected tables
- [ ] Test migration in development
- [ ] Verify AUTO_INCREMENT works
- [ ] Test application startup
- [ ] Test creating new Activity records
- [ ] Verify IDs are auto-generated correctly

## Common Issues & Solutions

### Issue: "Field 'id' doesn't have a default value"
**Solution:** Table doesn't have AUTO_INCREMENT. Run migration script.

### Issue: "Duplicate entry for key 'PRIMARY'"
**Solution:** AUTO_INCREMENT starting value is too low. Increase it:
```sql
ALTER TABLE table_name AUTO_INCREMENT = (SELECT MAX(id) + 1 FROM table_name);
```

### Issue: Application won't start
**Solution:** Rollback to GenerationType.AUTO temporarily.

## Post-Migration Cleanup

After successful migration:

1. **Drop hibernate_sequence table**
```sql
DROP TABLE IF EXISTS hibernate_sequence;
```

2. **Remove backup tables**
```sql
DROP TABLE IF EXISTS activity_info_backup;
DROP TABLE IF EXISTS activity_round_info_backup;
DROP TABLE IF EXISTS activity_submit_customer_backup;
```

3. **Remove TODO comments from code**

## Contact

If you encounter issues during migration, check the rollback plan or revert to the previous version.
