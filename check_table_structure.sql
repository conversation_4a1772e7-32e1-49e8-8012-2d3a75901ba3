-- Check current table structure for Activity-related tables
-- Run these queries to verify if AUTO_INCREMENT is already set

-- Check activity_info table structure
SHOW CREATE TABLE activity_info;
DESCRIBE activity_info;

-- Check activity_round_info table structure  
SHOW CREATE TABLE activity_round_info;
DESCRIBE activity_round_info;

-- Check activity_submit_customer table structure
SHOW CREATE TABLE activity_submit_customer;
DESCRIBE activity_submit_customer;

-- Check current max IDs in these tables
SELECT 'activity_info' as table_name, MAX(id) as max_id FROM activity_info
UNION ALL
SELECT 'activity_round_info', MAX(id) FROM activity_round_info  
UNION ALL
SELECT 'activity_submit_customer', MAX(id) FROM activity_submit_customer;

-- Check if hibernate_sequence table exists
SHOW TABLES LIKE 'hibernate_sequence';
SELECT * FROM hibernate_sequence;
