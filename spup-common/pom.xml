<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.spup</groupId>
        <artifactId>spup-root</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>spup-common</artifactId>
    <packaging>jar</packaging>

    <name>SPUP Common</name>
    <description>Common utilities, exceptions, and shared components</description>

    <dependencies>
        <!-- Module-specific dependencies (common dependencies inherited from parent) -->

        <!-- JWT - For token handling utilities -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>

        <!-- <PERSON> dependencies for CommonResult serialization -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- Jasypt for encryption utilities -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
