package com.spup.commons.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * Base DTO for AppCustomerContactsRequest
 * Contains common fields shared across modules
 */
@Getter
@Setter
public abstract class BaseAppCustomerContactsRequest {
    
    /**
     * Common validation method
     */
    public boolean isValid() {
        return true;
    }
    
    /**
     * Module-specific validation (to be implemented by subclasses)
     */
    public abstract boolean validateModuleSpecific();
}
