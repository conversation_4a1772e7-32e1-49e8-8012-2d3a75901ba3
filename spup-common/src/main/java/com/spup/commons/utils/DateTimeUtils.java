package com.spup.commons.utils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Unified Date/Time Utilities
 * Consolidates AppointHelper functionality
 */
public class DateTimeUtils {
    
    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter STANDARD_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter STANDARD_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * Get days span for exhibition category
     * Consolidated from AppointHelper.getDaysOfSpan()
     */
    public static int getDaysOfSpan(Number exhibitionCategory) {
        switch (exhibitionCategory.intValue()) {
            case 1: return 4;
            case 2: return 15;
            case 4: return 1;
            case 16: return 15;
            case 17: return 1;
            default: return 30;
        }
    }
    
    /**
     * Get not available rules for exhibition category
     * Consolidated from AppointHelper.getNotAvailableRules()
     */
    public static List<DayOfWeek> getNotAvailableRules(Number exhibitionCategory) {
        List<DayOfWeek> result = new ArrayList<>();
        result.add(DayOfWeek.MONDAY);
        
        switch (exhibitionCategory.intValue()) {
            case 2:
            case 4:
                result.add(DayOfWeek.SATURDAY);
                result.add(DayOfWeek.SUNDAY);
                break;
            case 16:
                result.add(DayOfWeek.SATURDAY);
                result.add(DayOfWeek.SUNDAY);
                result.add(DayOfWeek.FRIDAY);
                result.add(DayOfWeek.THURSDAY);
                break;
        }
        return result;
    }
    
    /**
     * Check if a date is a workday for given exhibition category
     */
    public static boolean isWorkday(LocalDate date, Number exhibitionCategory) {
        List<DayOfWeek> notAvailableRules = getNotAvailableRules(exhibitionCategory);
        return !notAvailableRules.contains(date.getDayOfWeek());
    }
    
    /**
     * Get available dates within span for exhibition category
     */
    public static List<LocalDate> getAvailableDates(Number exhibitionCategory) {
        int daysOfSpan = getDaysOfSpan(exhibitionCategory);
        List<LocalDate> result = new ArrayList<>();
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(daysOfSpan);

        while (startDate.isBefore(endDate)) {
            if (isWorkday(startDate, exhibitionCategory)) {
                result.add(startDate);
            }
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    /**
     * Get available days with workday information for exhibition category
     * Consolidated from AppointHelper.getAvailableDays()
     */
    public static List<com.spup.commons.dto.AvailableDayResponse> getAvailableDays(Number exhibitionCategory) {
        int daysOfSpan = getDaysOfSpan(exhibitionCategory);
        List<com.spup.commons.dto.AvailableDayResponse> result = new ArrayList<>();
        List<DayOfWeek> notAvailableRules = getNotAvailableRules(exhibitionCategory);
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(daysOfSpan);

        while (startDate.isBefore(endDate)) {
            com.spup.commons.dto.AvailableDayResponse dayItem = new com.spup.commons.dto.AvailableDayResponse();
            dayItem.setDay(startDate.format(FORMATTER));
            if (notAvailableRules.contains(startDate.getDayOfWeek())) {
                dayItem.setIsWorkday(0);
                dayItem.setDayRemark("闭馆");
            } else {
                dayItem.setIsWorkday(1);
                dayItem.setDayRemark("");
            }
            result.add(dayItem);
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    /**
     * Parse date from yyyyMMdd format
     */
    public static LocalDate parseFromYyyyMmDd(String dateStr) {
        return LocalDate.parse(dateStr, FORMATTER);
    }

    /**
     * Parse date from standard format (yyyy-MM-dd)
     */
    public static LocalDate parseFromStandardDate(String dateStr) {
        return LocalDate.parse(dateStr, STANDARD_DATE_FORMATTER);
    }

    /**
     * Parse datetime from standard format (yyyy-MM-dd HH:mm:ss)
     */
    public static LocalDateTime parseFromStandardDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, STANDARD_DATETIME_FORMATTER);
    }

    /**
     * Format LocalDate to yyyyMMdd
     */
    public static String formatToYyyyMmDd(LocalDate date) {
        return date.format(FORMATTER);
    }

    /**
     * Format LocalDate to standard format (yyyy-MM-dd)
     */
    public static String formatToStandardDate(LocalDate date) {
        return date.format(STANDARD_DATE_FORMATTER);
    }

    /**
     * Format LocalDateTime to standard format (yyyy-MM-dd HH:mm:ss)
     */
    public static String formatToStandardDateTime(LocalDateTime dateTime) {
        return dateTime.format(STANDARD_DATETIME_FORMATTER);
    }

    /**
     * Get current date as yyyyMMdd
     */
    public static String getCurrentDateAsYyyyMmDd() {
        return LocalDate.now().format(FORMATTER);
    }

    /**
     * Get current datetime as yyyyMMddHHmmss
     */
    public static String getCurrentDateTimeAsYyyyMmDdHhMmSs() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    /**
     * Get current year as string
     */
    public static String getCurrentYear() {
        return String.valueOf(LocalDate.now().getYear());
    }

    /**
     * Get current month as string (01-12)
     */
    public static String getCurrentMonth() {
        return String.format("%02d", LocalDate.now().getMonthValue());
    }

    /**
     * Get current day as string (01-31)
     */
    public static String getCurrentDay() {
        return String.format("%02d", LocalDate.now().getDayOfMonth());
    }
}
