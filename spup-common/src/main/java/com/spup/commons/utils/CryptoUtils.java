package com.spup.commons.utils;

import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

import java.security.MessageDigest;
import java.util.*;

/**
 * Unified Cryptographic Utilities
 * Consolidates Sha256Util, SignUtil, and JasyptUtil functionality
 */
public class CryptoUtils {
    
    /**
     * Generate SHA-256 hash
     * Consolidated from Sha256Util.getSha256Hash()
     */
    public static String sha256Hash(String input, String charset) {
        try {
            MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
            byte[] hash = sha256.digest(input.getBytes(charset));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("SHA-256 hashing failed", e);
        }
    }
    
    /**
     * Sign parameters with SHA-256
     * Consolidated from SignUtil.signSHA256()
     */
    public static String signSHA256(Map<String, Object> params, String signKey) {
        List<String> keys = new ArrayList<>();
        for (String key : params.keySet()) {
            if (params.get(key) instanceof String) {
                keys.add(key);
            }
        }
        Collections.sort(keys);
        
        StringBuilder sign = new StringBuilder();
        for (String key : keys) {
            sign.append(params.get(key));
        }
        
        String sha256 = sha256Hash(sign.toString(), "UTF-8");
        return sha256Hash(sha256 + signKey, "UTF-8");
    }
    
    /**
     * Encrypt using Jasypt
     * Consolidated from JasyptUtil.encrypt()
     */
    public static String encrypt(String password, String plainText) {
        StringEncryptor encryptor = createEncryptor(password);
        return encryptor.encrypt(plainText);
    }
    
    /**
     * Decrypt using Jasypt
     * Consolidated from JasyptUtil.decrypt()
     */
    public static String decrypt(String password, String encryptedText) {
        StringEncryptor encryptor = createEncryptor(password);
        return encryptor.decrypt(encryptedText);
    }
    
    private static StringEncryptor createEncryptor(String password) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        
        config.setPassword(password);
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        
        encryptor.setConfig(config);
        return encryptor;
    }
}
