package com.spup.commons.utils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;


import com.spup.commons.dto.AvailableDayResponse;

public class AppointHelper {

    public static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    public static int getDaysOfSpan(Number exhibitionCategory) {
        int resultInt = 0;
        switch (exhibitionCategory.intValue()) {
            case 1:
                resultInt = 4;
                break;
            case 2:
                resultInt = 15;
                break;
            case 4:
                resultInt = 1;
                break;
            case 16:
                resultInt = 15;
                break;
            case 17:
                resultInt = 1;
                break;
            default:
                resultInt = 30;
                break;
        }
        return resultInt;
        
    }

    public static List<DayOfWeek> getNotAvailableRules(Number exhibitionCategory) {
        List<DayOfWeek> result = new ArrayList<>();
        result.add(DayOfWeek.MONDAY);
        switch (exhibitionCategory.intValue()) {
            case 1:
                break;
            case 2:
                result.add(DayOfWeek.SATURDAY);
                result.add(DayOfWeek.SUNDAY);
                break;
            case 4:
                result.add(DayOfWeek.SATURDAY);
                result.add(DayOfWeek.SUNDAY);
                break;
            case 16:
                result.add(DayOfWeek.SATURDAY);
                result.add(DayOfWeek.SUNDAY);
                result.add(DayOfWeek.FRIDAY);
                result.add(DayOfWeek.THURSDAY);
                break;
            default:
                break;
        }
        return result;
    }

    public static List<AvailableDayResponse> getAvailableDays(Number exhibitionCategory) {
        int daysOfSpan = getDaysOfSpan(exhibitionCategory);
        List<AvailableDayResponse> result = new ArrayList<>();
        List<DayOfWeek> notAvailableRules = getNotAvailableRules(exhibitionCategory);
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(daysOfSpan);
    
        while (startDate.isBefore(endDate)) {
            AvailableDayResponse dayItem = new AvailableDayResponse();
            dayItem.setDay(startDate.format(formatter));
            if(notAvailableRules.contains(startDate.getDayOfWeek())) {
                dayItem.setIsWorkday(0);
                dayItem.setDayRemark("闭馆");
            } else {
                dayItem.setIsWorkday(1);
                dayItem.setDayRemark("");
            }
            result.add(dayItem);
            startDate = startDate.plusDays(1);
        }
        return result;
    }

}
