package com.spup.commons.utils;

import java.util.*;

public class SignUtil {
    public static String signSHA256(Map<String, Object> param, String signKey) {
        List<String> keys = new ArrayList<>();
        for (String key : param.keySet()) {
            if (param.get( key) instanceof  String) {
                keys.add( key);
            }
        }
        Collections.sort( keys);
        String sign = "";
        for (String key : keys) {
            sign += param.get( key);
        }
        String sha256 = Sha256Util.getSha256Hash( sign,"UTF-8");
        return Sha256Util.getSha256Hash( sha256 + signKey,"UTF-8");
    }

    public static void main(String[] args) {
        Map<String, Object> param = new HashMap<>();
        param.put("supplier","xiansong");
        param.put("scanTime","20241104123700");
        param.put("scanResult","TANj784JsPUp20001sPUp20002411041236177573sPUp2000241104123617757301sPUp2000epD5CKjK");
        String signKey = "E7qO2xPQaKxl9w6KP5mA";
        String sign = signSHA256(param,signKey);
        System.out.println(sign);
    }
}
