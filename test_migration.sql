-- Test Migration Script
-- Run this in development environment first

-- 1. Backup current data
CREATE TABLE activity_info_backup AS SELECT * FROM activity_info;
CREATE TABLE activity_round_info_backup AS SELECT * FROM activity_round_info;
CREATE TABLE activity_submit_customer_backup AS SELECT * FROM activity_submit_customer;

-- 2. Check current structure
SHOW CREATE TABLE activity_info;
SHOW CREATE TABLE activity_round_info;
SHOW CREATE TABLE activity_submit_customer;

-- 3. Test if AUTO_INCREMENT is needed
-- Try to insert without specifying ID (this will fail if no AUTO_INCREMENT)
-- INSERT INTO activity_info (activity_id, activity_name, pic_url, introduction_info, start_date_time, end_date_time, deleted) 
-- VALUES ('TEST001', 'Test Activity', 'test.jpg', 'Test', NOW(), NOW(), 0);

-- 4. If the above fails, add AUTO_INCREMENT
-- ALTER TABLE activity_info MODIFY COLUMN id BIGINT NOT NULL AUTO_INCREMENT;
-- ALTER TABLE activity_round_info MODIFY COLUMN id BIGINT NOT NULL AUTO_INCREMENT;
-- ALTER TABLE activity_submit_customer MODIFY COLUMN id BIGINT NOT NULL AUTO_INCREMENT;

-- 5. Set AUTO_INCREMENT starting value
-- SET @max_id = (SELECT IFNULL(MAX(id), 0) + 1 FROM activity_info);
-- SET @sql = CONCAT('ALTER TABLE activity_info AUTO_INCREMENT = ', @max_id);
-- PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 6. Test insert again
-- INSERT INTO activity_info (activity_id, activity_name, pic_url, introduction_info, start_date_time, end_date_time, deleted) 
-- VALUES ('TEST002', 'Test Activity 2', 'test2.jpg', 'Test 2', NOW(), NOW(), 0);

-- 7. Verify new ID was auto-generated
-- SELECT * FROM activity_info WHERE activity_id LIKE 'TEST%';

-- 8. Clean up test data
-- DELETE FROM activity_info WHERE activity_id LIKE 'TEST%';

-- 9. If everything works, drop backup tables
-- DROP TABLE activity_info_backup;
-- DROP TABLE activity_round_info_backup;
-- DROP TABLE activity_submit_customer_backup;
