# SPUP项目Tomcat部署指南

## 📋 项目结构概览

重新组织后的项目结构：
```
spup-root/
├── spup-common/          # 公共工具类和配置
├── spup-data/           # 数据访问层
├── spup-core/           # 核心业务逻辑
├── spup-activity/       # 活动相关功能
├── spup-admin-web/      # 管理后台Web应用 (WAR)
├── spup-user-web/       # 用户端Web应用 (WAR)
└── contract-tests/      # 契约测试
```

## 🔧 编译步骤

### 1. 环境准备
确保已安装：
- **JDK 8+** (项目使用JDK 8)
- **Maven 3.6+**
- **Tomcat 9.x** (推荐)

### 2. 完整编译命令

#### 方式一：编译所有模块
```bash
# 在项目根目录执行
mvn clean install

# 或者跳过测试快速编译
mvn clean install -DskipTests
```

#### 方式二：分步编译（推荐用于调试）
```bash
# 1. 编译基础模块
mvn clean install -pl spup-common,spup-data,spup-core,spup-activity -DskipTests

# 2. 编译Web应用
mvn clean package -pl spup-admin-web,spup-user-web -DskipTests
```

#### 方式三：只编译Web应用
```bash
# 编译管理后台
mvn clean package -pl spup-admin-web -am -DskipTests

# 编译用户端
mvn clean package -pl spup-user-web -am -DskipTests
```

### 3. 编译输出
编译成功后，WAR文件位置：
- **管理后台**: `spup-admin-web/target/spup-admin.war`
- **用户端**: `spup-user-web/target/spup.war`

## 🚀 Tomcat部署

### 1. 部署方式选择

#### 方式A：直接复制WAR文件（推荐）
```bash
# 停止Tomcat
$TOMCAT_HOME/bin/shutdown.sh

# 清理旧部署
rm -rf $TOMCAT_HOME/webapps/spup*
rm -rf $TOMCAT_HOME/work/Catalina/localhost/spup*

# 复制新WAR文件
cp spup-admin-web/target/spup-admin.war $TOMCAT_HOME/webapps/
cp spup-user-web/target/spup.war $TOMCAT_HOME/webapps/

# 启动Tomcat
$TOMCAT_HOME/bin/startup.sh
```

#### 方式B：使用Maven Tomcat插件
在根pom.xml中添加插件配置，然后：
```bash
# 部署到本地Tomcat
mvn tomcat7:deploy -pl spup-admin-web
mvn tomcat7:deploy -pl spup-user-web
```

### 2. 访问地址
部署成功后：
- **管理后台**: http://localhost:8080/spup-admin
- **用户端**: http://localhost:8080/spup

## ⚙️ 配置文件设置

### 1. 数据库配置
确保以下配置文件正确：
- `spup-admin-web/src/main/resources/application.yml`
- `spup-user-web/src/main/resources/application.yml`

### 2. 环境特定配置
```yaml
# application-prod.yml (生产环境)
spring:
  datasource:
    url: **************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  
  jpa:
    hibernate:
      ddl-auto: validate  # 生产环境使用validate
```

## 🛠 自动化部署脚本

### 创建部署脚本
```bash
# 创建deploy.sh
cat > deploy.sh << 'EOF'
#!/bin/bash

# 配置变量
TOMCAT_HOME="/opt/tomcat"
PROJECT_ROOT="/path/to/your/project"

echo "🚀 开始SPUP项目部署..."

# 1. 编译项目
echo "📦 编译项目..."
cd $PROJECT_ROOT
mvn clean install -DskipTests

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

# 2. 停止Tomcat
echo "🛑 停止Tomcat..."
$TOMCAT_HOME/bin/shutdown.sh
sleep 5

# 3. 清理旧部署
echo "🧹 清理旧部署..."
rm -rf $TOMCAT_HOME/webapps/spup*
rm -rf $TOMCAT_HOME/work/Catalina/localhost/spup*

# 4. 部署新WAR文件
echo "📋 部署新WAR文件..."
cp spup-admin-web/target/spup-admin.war $TOMCAT_HOME/webapps/
cp spup-user-web/target/spup.war $TOMCAT_HOME/webapps/

# 5. 启动Tomcat
echo "🚀 启动Tomcat..."
$TOMCAT_HOME/bin/startup.sh

echo "✅ 部署完成！"
echo "管理后台: http://localhost:8080/spup-admin"
echo "用户端: http://localhost:8080/spup"
EOF

chmod +x deploy.sh
```

## 🔍 故障排除

### 1. 编译问题
```bash
# 清理Maven缓存
mvn dependency:purge-local-repository

# 重新下载依赖
mvn clean install -U
```

### 2. 部署问题
```bash
# 查看Tomcat日志
tail -f $TOMCAT_HOME/logs/catalina.out

# 查看应用日志
tail -f $TOMCAT_HOME/logs/localhost.*.log
```

### 3. 常见错误解决

#### ClassNotFoundException
- 检查依赖是否正确打包到WAR文件中
- 确认所有模块版本一致

#### 端口冲突
```bash
# 检查端口占用
netstat -tlnp | grep :8080

# 修改Tomcat端口
vim $TOMCAT_HOME/conf/server.xml
```

#### 内存不足
```bash
# 增加Tomcat内存
export CATALINA_OPTS="-Xms512m -Xmx2048m -XX:PermSize=256m -XX:MaxPermSize=512m"
```

## 📊 监控和维护

### 1. 健康检查
```bash
# 检查应用状态
curl -I http://localhost:8080/spup/health
curl -I http://localhost:8080/spup-admin/health
```

### 2. 日志监控
```bash
# 实时监控应用日志
tail -f $TOMCAT_HOME/logs/catalina.out | grep -E "(ERROR|WARN|Exception)"
```

### 3. 性能监控
- 使用JVisualVM监控JVM性能
- 配置应用性能监控(APM)工具

## 🔄 持续集成建议

### Jenkins Pipeline示例
```groovy
pipeline {
    agent any
    stages {
        stage('Build') {
            steps {
                sh 'mvn clean install -DskipTests'
            }
        }
        stage('Test') {
            steps {
                sh 'mvn test'
            }
        }
        stage('Deploy') {
            steps {
                sh './deploy.sh'
            }
        }
    }
}
```

## 📝 最佳实践

1. **版本管理**: 使用Git标签管理发布版本
2. **配置外化**: 敏感配置使用环境变量
3. **日志管理**: 配置日志轮转和归档
4. **备份策略**: 定期备份数据库和配置文件
5. **监控告警**: 设置应用和服务器监控告警

## 🎯 快速部署命令总结

```bash
# 完整部署流程（一键执行）
mvn clean install -DskipTests && \
$TOMCAT_HOME/bin/shutdown.sh && \
sleep 5 && \
rm -rf $TOMCAT_HOME/webapps/spup* && \
cp spup-admin-web/target/spup-admin.war $TOMCAT_HOME/webapps/ && \
cp spup-user-web/target/spup.war $TOMCAT_HOME/webapps/ && \
$TOMCAT_HOME/bin/startup.sh && \
echo "部署完成！"
```
