package com.spup.service.appointment.impl;

import com.spup.counter.Item4Counter;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Quick concurrency test that can be run immediately without Spring context
 * Tests the Item4Counter's thread safety improvements
 */
public class QuickConcurrencyTest {

    /**
     * Test Item4Counter thread safety with ConcurrentHashMap
     */
    @Test
    void testItem4CounterConcurrency() throws Exception {
        Item4Counter counter = new Item4Counter();
        String batchNo = "TEST_BATCH";
        
        // Initialize counter with test data using reflection
        initializeCounterForTest(counter, batchNo, 20);
        
        int numberOfThreads = 50;
        int seatsToBook = 15; // Less than available to test partial booking
        
        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completeLatch = new CountDownLatch(numberOfThreads);
        
        AtomicInteger successfulAllocations = new AtomicInteger(0);
        AtomicInteger failedAllocations = new AtomicInteger(0);
        
        // Create concurrent booking attempts
        for (int i = 0; i < numberOfThreads; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    startLatch.await();
                    
                    // Try to book a seat
                    String subOrderNo = "ORDER_" + threadId + "_01";
                    Byte seatNo = counter.getNo(batchNo, subOrderNo);
                    
                    if (seatNo != -1) {
                        successfulAllocations.incrementAndGet();
                        System.out.println("Thread " + threadId + " got seat: " + seatNo);
                    } else {
                        failedAllocations.incrementAndGet();
                        System.out.println("Thread " + threadId + " failed to get seat");
                    }
                    
                } catch (Exception e) {
                    failedAllocations.incrementAndGet();
                    System.err.println("Thread " + threadId + " error: " + e.getMessage());
                } finally {
                    completeLatch.countDown();
                }
            });
        }
        
        System.out.println("Starting " + numberOfThreads + " threads to book from 20 available seats...");
        
        long startTime = System.currentTimeMillis();
        startLatch.countDown(); // Start all threads
        
        assertTrue(completeLatch.await(10, TimeUnit.SECONDS), "Test should complete within 10 seconds");
        long endTime = System.currentTimeMillis();
        
        executor.shutdown();
        
        // Verify results
        System.out.println("\n=== RESULTS ===");
        System.out.println("Successful allocations: " + successfulAllocations.get());
        System.out.println("Failed allocations: " + failedAllocations.get());
        System.out.println("Total operations: " + (successfulAllocations.get() + failedAllocations.get()));
        System.out.println("Test duration: " + (endTime - startTime) + "ms");
        System.out.println("Remaining seats: " + counter.getSeatAvailableCount(batchNo));
        
        // Assertions
        assertEquals(numberOfThreads, successfulAllocations.get() + failedAllocations.get());
        assertTrue(successfulAllocations.get() <= 20, "Cannot allocate more seats than available");
        assertEquals(20 - successfulAllocations.get(), counter.getSeatAvailableCount(batchNo),
                    "Remaining seats should be correct");
        
        // Verify no duplicate seat allocations
        verifyNoDuplicateSeats(counter, batchNo);
        
        System.out.println("✅ Concurrency test passed!");
    }

    /**
     * Test canBook method thread safety
     */
    @Test
    void testCanBookConcurrency() throws Exception {
        Item4Counter counter = new Item4Counter();
        String batchNo = "CANBOOK_TEST";
        
        initializeCounterForTest(counter, batchNo, 5);
        
        int numberOfThreads = 20;
        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch completeLatch = new CountDownLatch(numberOfThreads);
        
        AtomicInteger canBookCount = new AtomicInteger(0);
        AtomicInteger cannotBookCount = new AtomicInteger(0);
        
        for (int i = 0; i < numberOfThreads; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    // Each thread checks if it can book 2 seats
                    boolean canBook = counter.canBook(batchNo, 2);
                    
                    if (canBook) {
                        canBookCount.incrementAndGet();
                    } else {
                        cannotBookCount.incrementAndGet();
                    }
                    
                    // Simulate some processing time
                    Thread.sleep(10);
                    
                } catch (Exception e) {
                    System.err.println("Thread " + threadId + " error: " + e.getMessage());
                } finally {
                    completeLatch.countDown();
                }
            });
        }
        
        assertTrue(completeLatch.await(5, TimeUnit.SECONDS));
        executor.shutdown();
        
        System.out.println("CanBook results - Can book: " + canBookCount.get() + 
                          ", Cannot book: " + cannotBookCount.get());
        
        // All threads should get consistent results
        assertEquals(numberOfThreads, canBookCount.get() + cannotBookCount.get());
        
        System.out.println("✅ CanBook concurrency test passed!");
    }

    /**
     * Test seat release and reallocation
     */
    @Test
    void testSeatReleaseAndReallocation() throws Exception {
        Item4Counter counter = new Item4Counter();
        String batchNo = "RELEASE_TEST";
        
        initializeCounterForTest(counter, batchNo, 3);
        
        // Book all seats
        Byte seat1 = counter.getNo(batchNo, "ORDER1");
        Byte seat2 = counter.getNo(batchNo, "ORDER2");
        Byte seat3 = counter.getNo(batchNo, "ORDER3");
        
        assertEquals(0, counter.getSeatAvailableCount(batchNo), "All seats should be booked");
        
        // Release one seat
        counter.releaseNo(batchNo, seat2);
        assertEquals(1, counter.getSeatAvailableCount(batchNo), "One seat should be available");
        
        // Test concurrent reallocation
        ExecutorService executor = Executors.newFixedThreadPool(5);
        CountDownLatch completeLatch = new CountDownLatch(5);
        AtomicInteger successCount = new AtomicInteger(0);
        
        for (int i = 0; i < 5; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    Byte seat = counter.getNo(batchNo, "REALLOC_" + threadId);
                    if (seat != -1) {
                        successCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    System.err.println("Reallocation error: " + e.getMessage());
                } finally {
                    completeLatch.countDown();
                }
            });
        }
        
        assertTrue(completeLatch.await(5, TimeUnit.SECONDS));
        executor.shutdown();
        
        assertEquals(1, successCount.get(), "Only one thread should successfully reallocate the seat");
        assertEquals(0, counter.getSeatAvailableCount(batchNo), "No seats should remain available");
        
        System.out.println("✅ Seat release and reallocation test passed!");
    }

    private void initializeCounterForTest(Item4Counter counter, String batchNo, int totalSeats) throws Exception {
        // Use reflection to initialize the counter's internal map
        Field subOrderNoMapField = Item4Counter.class.getDeclaredField("subOrderNoMap");
        subOrderNoMapField.setAccessible(true);
        
        Map<String, Map<Byte, String>> subOrderNoMap = new ConcurrentHashMap<>();
        Map<Byte, String> seatMap = new ConcurrentHashMap<>();
        
        for (int i = 1; i <= totalSeats; i++) {
            seatMap.put((byte) i, "");
        }
        
        subOrderNoMap.put(batchNo, seatMap);
        subOrderNoMapField.set(counter, subOrderNoMap);
        
        System.out.println("Initialized counter with " + totalSeats + " seats for batch: " + batchNo);
    }

    private void verifyNoDuplicateSeats(Item4Counter counter, String batchNo) throws Exception {
        Field subOrderNoMapField = Item4Counter.class.getDeclaredField("subOrderNoMap");
        subOrderNoMapField.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, Map<Byte, String>> subOrderNoMap = 
            (Map<String, Map<Byte, String>>) subOrderNoMapField.get(counter);
        
        Map<Byte, String> seatMap = subOrderNoMap.get(batchNo);
        
        // Check for duplicate suborder numbers
        Map<String, Byte> subOrderToSeat = new ConcurrentHashMap<>();
        
        for (Map.Entry<Byte, String> entry : seatMap.entrySet()) {
            String subOrderNo = entry.getValue();
            if (subOrderNo != null && !subOrderNo.isEmpty()) {
                Byte existingSeat = subOrderToSeat.put(subOrderNo, entry.getKey());
                assertNull(existingSeat, "Duplicate suborder number found: " + subOrderNo + 
                          " assigned to seats " + existingSeat + " and " + entry.getKey());
            }
        }
        
        System.out.println("✅ No duplicate seat allocations found");
    }
}
