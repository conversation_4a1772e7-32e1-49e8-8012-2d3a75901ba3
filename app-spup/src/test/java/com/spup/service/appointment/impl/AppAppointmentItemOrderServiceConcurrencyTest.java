package com.spup.service.appointment.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huangdou.commons.api.CommonResult;
import com.spup.counter.Item4Counter;
import com.spup.db.dao.appointment.AppAppointmentItemOrderDao;
import com.spup.db.dao.appointment.AppAppointmentItemSuborderDao;
import com.spup.db.dao.appointment.AppBatchDao;
import com.spup.db.entity.appointment.AppBatch;
import com.spup.dto.AppAppointmentItemOrderRequest;
import com.spup.enums.BatchCategoryEnum;
import com.spup.enums.BatchStatusEnum;
import com.spup.service.appointment.IAppBatchService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Concurrency tests for AppAppointmentItemOrderServiceImpl
 * Tests the thread safety of the newSave() method under high concurrency
 */
@ExtendWith(MockitoExtension.class)
public class AppAppointmentItemOrderServiceConcurrencyTest {

    @Mock
    private AppAppointmentItemOrderDao appAppointmentItemOrderDao;
    
    @Mock
    private AppAppointmentItemSuborderDao appAppointmentItemSuborderDao;
    
    @Mock
    private AppBatchDao appBatchDao;
    
    @Mock
    private IAppBatchService iAppBatchService;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @InjectMocks
    private AppAppointmentItemOrderServiceImpl appointmentService;
    
    private Item4Counter item4Counter;
    private AppBatch testBatch;
    private String testBatchNo = "BATCH001";
    private String testUnionid = "user123";

    @BeforeEach
    void setUp() throws Exception {
        // Create real Item4Counter for testing
        item4Counter = new Item4Counter();
        ReflectionTestUtils.setField(appointmentService, "item4Counter", item4Counter);
        
        // Setup test batch
        testBatch = new AppBatch();
        testBatch.setBatchNo(testBatchNo);
        testBatch.setBatchDate("20241208");
        testBatch.setBatchStartTime("09:00");
        testBatch.setBatchEndTime("10:00");
        testBatch.setBatchStatus(BatchStatusEnum.RUNNING.getCode());
        testBatch.setTicketTotal(10); // 10 seats available
        testBatch.setTicketRemaining(10);
        
        // Mock DAO responses
        when(appBatchDao.getByBatchNoAndBatchCategory(testBatchNo, BatchCategoryEnum.ITEM_FYPD.getCode()))
            .thenReturn(Optional.of(testBatch));
        when(appAppointmentItemSuborderDao.findByOnwerUnionid(anyString()))
            .thenReturn(new ArrayList<>());
        
        // Setup ObjectMapper mock for JSON parsing with proper return values
        ArrayNode arrayNode = mock(ArrayNode.class);
        ObjectNode personNode = mock(ObjectNode.class);
        com.fasterxml.jackson.databind.JsonNode nameNode = mock(com.fasterxml.jackson.databind.JsonNode.class);
        com.fasterxml.jackson.databind.JsonNode phoneNode = mock(com.fasterxml.jackson.databind.JsonNode.class);
        com.fasterxml.jackson.databind.JsonNode idCategoryNode = mock(com.fasterxml.jackson.databind.JsonNode.class);
        com.fasterxml.jackson.databind.JsonNode idNoNode = mock(com.fasterxml.jackson.databind.JsonNode.class);

        when(objectMapper.readTree(anyString())).thenReturn(arrayNode);
        when(arrayNode.size()).thenReturn(1);
        when(arrayNode.isEmpty()).thenReturn(false);
        when(arrayNode.get(0)).thenReturn(personNode);

        when(personNode.findValue("name")).thenReturn(nameNode);
        when(personNode.findValue("phone")).thenReturn(phoneNode);
        when(personNode.findValue("idcardCategory")).thenReturn(idCategoryNode);
        when(personNode.findValue("idcardNo")).thenReturn(idNoNode);

        when(nameNode.asText()).thenReturn("Test User");
        when(phoneNode.asText()).thenReturn("1234567890");
        when(idCategoryNode.numberValue()).thenReturn(1);
        when(idCategoryNode.asText()).thenReturn("1");
        when(idNoNode.asText()).thenReturn("123456789012345678");
        
        // Initialize Item4Counter with test data
        initializeItem4Counter();
    }

    private void initializeItem4Counter() {
        // Use reflection to set up the counter's internal map
        Map<String, Map<Byte, String>> subOrderNoMap = new ConcurrentHashMap<>();
        Map<Byte, String> seatMap = new ConcurrentHashMap<>();
        
        // Initialize 10 seats (1-10)
        for (int i = 1; i <= 10; i++) {
            seatMap.put((byte) i, "");
        }
        subOrderNoMap.put(testBatchNo, seatMap);
        
        ReflectionTestUtils.setField(item4Counter, "subOrderNoMap", subOrderNoMap);
    }

    /**
     * Test 1: High Concurrency Booking Test
     * Simulates 50 concurrent users trying to book seats when only 10 are available
     */
    @Test
    void testHighConcurrencyBooking() throws Exception {
        int numberOfThreads = 50;
        int availableSeats = 10;
        
        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completeLatch = new CountDownLatch(numberOfThreads);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        List<String> results = Collections.synchronizedList(new ArrayList<>());
        
        // Create booking tasks
        for (int i = 0; i < numberOfThreads; i++) {
            final int userId = i;
            executor.submit(() -> {
                try {
                    startLatch.await(); // Wait for all threads to be ready
                    
                    AppAppointmentItemOrderRequest request = createTestRequest();
                    String unionid = "user" + userId;
                    
                    CommonResult<?> result = appointmentService.newSave(request, unionid);
                    
                    if (result.getStatus()) {
                        successCount.incrementAndGet();
                        results.add("SUCCESS: " + unionid);
                    } else {
                        failureCount.incrementAndGet();
                        results.add("FAILED: " + unionid + " - " + result.getMessage());
                    }
                    
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    results.add("ERROR: user" + userId + " - " + e.getMessage());
                } finally {
                    completeLatch.countDown();
                }
            });
        }
        
        // Start all threads simultaneously
        startLatch.countDown();
        
        // Wait for all threads to complete (max 30 seconds)
        assertTrue(completeLatch.await(30, TimeUnit.SECONDS), "Test should complete within 30 seconds");
        
        executor.shutdown();
        
        // Verify results
        System.out.println("=== CONCURRENCY TEST RESULTS ===");
        System.out.println("Success count: " + successCount.get());
        System.out.println("Failure count: " + failureCount.get());
        System.out.println("Available seats: " + availableSeats);
        
        results.forEach(System.out::println);
        
        // Assertions
        assertEquals(numberOfThreads, successCount.get() + failureCount.get(), 
            "Total operations should equal number of threads");
        assertEquals(availableSeats, successCount.get(), 
            "Success count should equal available seats (no overselling)");
        assertEquals(numberOfThreads - availableSeats, failureCount.get(), 
            "Failure count should equal rejected bookings");
        
        // Verify no seat conflicts
        verifyNoSeatConflicts();
    }

    /**
     * Test 2: Seat Allocation Consistency Test
     * Verifies that each seat is allocated to exactly one booking
     */
    @Test
    void testSeatAllocationConsistency() throws Exception {
        int numberOfBookings = 5;
        ExecutorService executor = Executors.newFixedThreadPool(numberOfBookings);
        CountDownLatch completeLatch = new CountDownLatch(numberOfBookings);
        
        Set<Byte> allocatedSeats = Collections.synchronizedSet(new HashSet<>());
        List<String> successfulBookings = Collections.synchronizedList(new ArrayList<>());
        
        for (int i = 0; i < numberOfBookings; i++) {
            final int userId = i;
            executor.submit(() -> {
                try {
                    AppAppointmentItemOrderRequest request = createTestRequest();
                    String unionid = "user" + userId;
                    
                    CommonResult<?> result = appointmentService.newSave(request, unionid);
                    
                    if (result.getStatus()) {
                        successfulBookings.add(unionid);
                        // In a real test, you'd capture the allocated seat number
                        // For now, we'll verify through the counter's state
                    }
                    
                } catch (Exception e) {
                    System.err.println("Error in booking for user" + userId + ": " + e.getMessage());
                } finally {
                    completeLatch.countDown();
                }
            });
        }
        
        assertTrue(completeLatch.await(10, TimeUnit.SECONDS));
        executor.shutdown();
        
        // Verify all successful bookings got unique seats
        assertEquals(numberOfBookings, successfulBookings.size());
        verifyNoSeatConflicts();
    }

    /**
     * Test 3: Rollback Test
     * Simulates failure scenarios to ensure proper cleanup
     */
    @Test
    void testRollbackOnFailure() throws Exception {
        // Mock a failure scenario
        doThrow(new RuntimeException("Database error"))
            .when(appAppointmentItemOrderDao).save(any());
        
        AppAppointmentItemOrderRequest request = createTestRequest();
        
        // Capture initial state
        int initialAvailableSeats = item4Counter.getSeatAvailableCount(testBatchNo);
        
        // Attempt booking (should fail and rollback)
        assertThrows(RuntimeException.class, () -> {
            appointmentService.newSave(request, testUnionid);
        });
        
        // Verify seats were released
        int finalAvailableSeats = item4Counter.getSeatAvailableCount(testBatchNo);
        assertEquals(initialAvailableSeats, finalAvailableSeats, 
            "Available seats should be restored after rollback");
    }

    private AppAppointmentItemOrderRequest createTestRequest() {
        AppAppointmentItemOrderRequest request = new AppAppointmentItemOrderRequest();
        request.setBatchNo(testBatchNo);
        request.setContacts("[{\"name\":\"Test User\",\"phone\":\"1234567890\",\"idcardCategory\":1,\"idcardNo\":\"123456789012345678\"}]");
        request.setCategory(BatchCategoryEnum.ITEM_FYPD.getCode());
        return request;
    }

    private void verifyNoSeatConflicts() {
        // Access the counter's internal state to verify no conflicts
        Map<String, Map<Byte, String>> subOrderNoMap = 
            (Map<String, Map<Byte, String>>) ReflectionTestUtils.getField(item4Counter, "subOrderNoMap");
        
        Map<Byte, String> seatMap = subOrderNoMap.get(testBatchNo);
        Set<String> usedSubOrderNos = new HashSet<>();
        
        for (Map.Entry<Byte, String> entry : seatMap.entrySet()) {
            String subOrderNo = entry.getValue();
            if (subOrderNo != null && !subOrderNo.isEmpty()) {
                assertFalse(usedSubOrderNos.contains(subOrderNo), 
                    "Duplicate suborder number found: " + subOrderNo);
                usedSubOrderNos.add(subOrderNo);
            }
        }
        
        System.out.println("Seat allocation verification passed. Unique bookings: " + usedSubOrderNos.size());
    }
}
