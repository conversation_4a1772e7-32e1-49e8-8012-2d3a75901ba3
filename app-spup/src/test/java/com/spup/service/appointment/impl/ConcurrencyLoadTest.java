package com.spup.service.appointment.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.huangdou.commons.api.CommonResult;
import com.spup.dto.AppAppointmentItemOrderRequest;
import com.spup.enums.BatchCategoryEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Load testing for concurrent booking scenarios
 * This test requires a running Spring context and database
 * 
 * To run: Remove @Disabled annotation and ensure test database is available
 */
@SpringBootTest
@SpringJUnitConfig
@Disabled("Enable only for load testing with real database")
public class ConcurrencyLoadTest {

    @Autowired
    private AppAppointmentItemOrderServiceImpl appointmentService;
    
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Stress Test: 100 concurrent users trying to book 20 available seats
     * This simulates a real-world high-demand scenario
     */
    @Test
    void stressTestHighDemandBooking() throws Exception {
        int totalUsers = 100;
        int availableSeats = 20;
        String batchNo = "STRESS_BATCH_" + System.currentTimeMillis();
        
        // Setup test batch (you'd need to create this in your test database)
        setupTestBatch(batchNo, availableSeats);
        
        ExecutorService executor = Executors.newFixedThreadPool(totalUsers);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completeLatch = new CountDownLatch(totalUsers);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        AtomicLong maxResponseTime = new AtomicLong(0);
        AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
        
        List<BookingResult> results = Collections.synchronizedList(new ArrayList<>());
        
        System.out.println("Starting stress test with " + totalUsers + " users for " + availableSeats + " seats");
        
        for (int i = 0; i < totalUsers; i++) {
            final int userId = i;
            executor.submit(() -> {
                try {
                    startLatch.await();
                    
                    long startTime = System.currentTimeMillis();
                    
                    AppAppointmentItemOrderRequest request = createLoadTestRequest(batchNo);
                    String unionid = "loadtest_user_" + userId;
                    
                    CommonResult<?> result = appointmentService.newSave(request, unionid);
                    
                    long endTime = System.currentTimeMillis();
                    long responseTime = endTime - startTime;
                    
                    // Update metrics
                    totalResponseTime.addAndGet(responseTime);
                    maxResponseTime.updateAndGet(current -> Math.max(current, responseTime));
                    minResponseTime.updateAndGet(current -> Math.min(current, responseTime));
                    
                    BookingResult bookingResult = new BookingResult();
                    bookingResult.userId = userId;
                    bookingResult.unionid = unionid;
                    bookingResult.success = result.getStatus();
                    bookingResult.message = result.getMessage();
                    bookingResult.responseTime = responseTime;
                    bookingResult.timestamp = startTime;
                    
                    results.add(bookingResult);
                    
                    if (result.getStatus()) {
                        successCount.incrementAndGet();
                    } else {
                        failureCount.incrementAndGet();
                    }
                    
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                    BookingResult errorResult = new BookingResult();
                    errorResult.userId = userId;
                    errorResult.success = false;
                    errorResult.message = "Exception: " + e.getMessage();
                    results.add(errorResult);
                } finally {
                    completeLatch.countDown();
                }
            });
        }
        
        long testStartTime = System.currentTimeMillis();
        startLatch.countDown(); // Start all threads
        
        boolean completed = completeLatch.await(60, TimeUnit.SECONDS);
        long testEndTime = System.currentTimeMillis();
        
        executor.shutdown();
        
        // Generate detailed report
        generateLoadTestReport(results, successCount.get(), failureCount.get(), 
                             totalResponseTime.get(), maxResponseTime.get(), 
                             minResponseTime.get(), testEndTime - testStartTime,
                             totalUsers, availableSeats);
        
        // Assertions for load test
        assertTrue(completed, "Load test should complete within 60 seconds");
        assertEquals(totalUsers, successCount.get() + failureCount.get());
        assertTrue(successCount.get() <= availableSeats, "No overselling should occur");
        assertTrue(successCount.get() > 0, "At least some bookings should succeed");
    }

    /**
     * Performance Benchmark Test
     * Measures throughput and response times under different load levels
     */
    @Test
    void performanceBenchmarkTest() throws Exception {
        int[] loadLevels = {10, 25, 50, 100};
        
        System.out.println("=== PERFORMANCE BENCHMARK TEST ===");
        
        for (int load : loadLevels) {
            System.out.println("\n--- Testing with " + load + " concurrent users ---");
            
            String batchNo = "PERF_BATCH_" + load + "_" + System.currentTimeMillis();
            setupTestBatch(batchNo, load); // Ensure enough seats
            
            long startTime = System.currentTimeMillis();
            runPerformanceTest(load, batchNo);
            long endTime = System.currentTimeMillis();
            
            System.out.println("Total time for " + load + " users: " + (endTime - startTime) + "ms");
            System.out.println("Throughput: " + (load * 1000.0 / (endTime - startTime)) + " bookings/second");
        }
    }

    private void runPerformanceTest(int userCount, String batchNo) throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(userCount);
        CountDownLatch completeLatch = new CountDownLatch(userCount);
        
        for (int i = 0; i < userCount; i++) {
            final int userId = i;
            executor.submit(() -> {
                try {
                    AppAppointmentItemOrderRequest request = createLoadTestRequest(batchNo);
                    appointmentService.newSave(request, "perf_user_" + userId);
                } catch (Exception e) {
                    System.err.println("Performance test error for user " + userId + ": " + e.getMessage());
                } finally {
                    completeLatch.countDown();
                }
            });
        }
        
        completeLatch.await(30, TimeUnit.SECONDS);
        executor.shutdown();
    }

    private void setupTestBatch(String batchNo, int totalSeats) {
        // In a real test, you would:
        // 1. Create a test batch in the database
        // 2. Initialize the Item4Counter with the batch
        // 3. Set up proper test data
        
        System.out.println("Setting up test batch: " + batchNo + " with " + totalSeats + " seats");
        // Implementation depends on your test setup
    }

    private AppAppointmentItemOrderRequest createLoadTestRequest(String batchNo) {
        AppAppointmentItemOrderRequest request = new AppAppointmentItemOrderRequest();
        request.setBatchNo(batchNo);
        request.setContacts("[{\"name\":\"Load Test User\",\"phone\":\"1234567890\",\"idcardCategory\":1,\"idcardNo\":\"123456789012345678\"}]");
        request.setCategory(BatchCategoryEnum.ITEM_FYPD.getCode());
        return request;
    }

    private void generateLoadTestReport(List<BookingResult> results, int successCount, 
                                      int failureCount, long totalResponseTime, 
                                      long maxResponseTime, long minResponseTime, 
                                      long totalTestTime, int totalUsers, int availableSeats) {
        
        System.out.println("\n=== LOAD TEST REPORT ===");
        System.out.println("Total Users: " + totalUsers);
        System.out.println("Available Seats: " + availableSeats);
        System.out.println("Successful Bookings: " + successCount);
        System.out.println("Failed Bookings: " + failureCount);
        System.out.println("Success Rate: " + (successCount * 100.0 / totalUsers) + "%");
        System.out.println("Total Test Time: " + totalTestTime + "ms");
        
        if (successCount > 0) {
            System.out.println("Average Response Time: " + (totalResponseTime / totalUsers) + "ms");
            System.out.println("Max Response Time: " + maxResponseTime + "ms");
            System.out.println("Min Response Time: " + (minResponseTime == Long.MAX_VALUE ? 0 : minResponseTime) + "ms");
        }
        
        System.out.println("Throughput: " + (totalUsers * 1000.0 / totalTestTime) + " requests/second");
        
        // Check for data consistency
        boolean overselling = successCount > availableSeats;
        System.out.println("Overselling Detected: " + (overselling ? "YES ❌" : "NO ✅"));
        
        if (overselling) {
            System.err.println("CRITICAL: Overselling detected! " + successCount + " bookings for " + availableSeats + " seats");
        }
        
        // Response time distribution
        results.sort(Comparator.comparing(r -> r.responseTime));
        if (!results.isEmpty()) {
            int p50Index = results.size() / 2;
            int p95Index = (int) (results.size() * 0.95);
            int p99Index = (int) (results.size() * 0.99);
            
            System.out.println("Response Time Percentiles:");
            System.out.println("  P50: " + results.get(p50Index).responseTime + "ms");
            System.out.println("  P95: " + results.get(p95Index).responseTime + "ms");
            System.out.println("  P99: " + results.get(p99Index).responseTime + "ms");
        }
    }

    private static class BookingResult {
        int userId;
        String unionid;
        boolean success;
        String message;
        long responseTime;
        long timestamp;
    }

    private void assertTrue(boolean condition, String message) {
        if (!condition) {
            throw new AssertionError(message);
        }
    }

    private void assertEquals(int expected, int actual) {
        if (expected != actual) {
            throw new AssertionError("Expected: " + expected + ", but was: " + actual);
        }
    }
}
