# server:
#   port: 8080
#   servlet:
#     context-path: /spup

# jasypt:
#   encryptor:
#     password: ${JASPYT_ENCRYPTOR_PASSWORD}

spring:
  profiles:
    active: dev
  jmx:
    enabled: false
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  jpa:
    #关闭懒加载 否则通过id查询有问题
    properties:
      hibernate:
        "[enable_lazy_load_no_trans]": true
        dialect: org.hibernate.dialect.MySQL8Dialect
        "[format_sql]": true
    generate-ddl: true
    database-platform: org.hibernate.dialect.MySQL8Dialect
  # DevTools configuration for hot reload
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
    livereload:
      enabled: true

# JWT Configuration
jwt:
  sign:
    string: '<EMAIL>'
  expires: 60

appointment:
  config:
    all:
      weekdayList:
        - MONDAY
      holidayList:
    L20230906T20230915:
      weekdayList:
        - SUNDAY
        - SATURDAY
      holidayList:
    fypd:
      weekdayList:
      holidayList:
    team:
      weekdayList:
        - SUNDAY
        - SATURDAY
        - MONDAY
      holidayList:
    L20240716T20241231:
      weekdayList:
      holidayList:
    L20250326151436123:
      weekdayList:
        - MONDAY
        - THURSDAY
        - FRIDAY
        - SATURDAY
        - SUNDAY
      holidayList:

scheduler:
  CreateTempExhibitionBatchTask:
    cron: "0 55 21 * * ?"
  OpenTempExhibitionBatchTask:
    cron: "0 51 21 * * ?"
