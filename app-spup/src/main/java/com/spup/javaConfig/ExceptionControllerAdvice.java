package com.spup.javaConfig;

import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.api.ResultCodeEnum;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestControllerAdvice(basePackages = "com.spup")
public class ExceptionControllerAdvice {
    private static final Logger logger = LoggerFactory.getLogger(ExceptionControllerAdvice.class);

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public CommonResult<?> handleValidException(MethodArgumentNotValidException e, HttpServletResponse response) {

        logger.error("数据校验出现问题：{}，异常类型：{}", e.getMessage(), e.getClass());

        // Check if response is already committed to avoid IllegalStateException
        if (response.isCommitted()) {
            logger.warn("Response already committed, cannot handle validation exception: {}", e.getMessage());
            return null;
        }

        // ExceptionHandler里面可以获取BindingResult，通过BindingResult对象获取实际的错误信息
        BindingResult bindingResult = e.getBindingResult();
        StringBuffer stringBuffer = new StringBuffer();
        bindingResult.getFieldErrors().forEach(item -> {
            String message = item.getDefaultMessage();
            stringBuffer.append(message + ";");
        });

        return CommonResult.failed(stringBuffer + "");
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    public CommonResult<?> handleIllegalArgumentExceptions(IllegalArgumentException e, HttpServletResponse response) {
        logger.error("Arguments is invalid.");

        // Check if response is already committed to avoid IllegalStateException
        if (response.isCommitted()) {
            logger.warn("Response already committed, cannot handle illegal argument exception: {}", e.getMessage());
            return null;
        }

        return CommonResult.failed(ResultCodeEnum.VALIDATION_FAILED);
    }

    @ExceptionHandler(value = Exception.class)
    public CommonResult<?> handleGeneralExceptions(Exception e, HttpServletRequest request, HttpServletResponse response) {
        try {
            logger.error("=== ORIGINAL EXCEPTION: {} ===", e.getClass().getSimpleName());
            logger.error("Request URL: {}", request.getRequestURL());
            logger.error("Request Method: {}", request.getMethod());
            logger.error("Exception message: {}", e.getMessage());
            logger.error("系统异常 - Full stack trace:", e);

            // Log the cause chain
            Throwable cause = e.getCause();
            int level = 1;
            while (cause != null && level <= 5) {
                logger.error("Caused by (level {}): {} - {}", level, cause.getClass().getSimpleName(), cause.getMessage());
                cause = cause.getCause();
                level++;
            }

            // Check if response is already committed to avoid IllegalStateException
            if (response.isCommitted()) {
                logger.warn("Response already committed, cannot handle exception: {}", e.getMessage());
                return null;
            }

            return CommonResult.failed("系统异常");
        } catch (Exception handlerException) {
            logger.error("=== EXCEPTION IN EXCEPTION HANDLER ===");
            logger.error("Original exception was: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            logger.error("Handler exception:", handlerException);
            return createSafeErrorResponse("系统异常", response);
        }
    }

    @ExceptionHandler(value = Throwable.class)
    public CommonResult<?> handleException(Throwable throwable, HttpServletRequest request, HttpServletResponse response) {
        try {
            logger.error("=== ORIGINAL THROWABLE: {} ===", throwable.getClass().getSimpleName());
            logger.error("Request URL: {}", request.getRequestURL());
            logger.error("Request Method: {}", request.getMethod());
            logger.error("系统错误 - Full details:", throwable);

            // Check if response is already committed to avoid IllegalStateException
            if (response.isCommitted()) {
                logger.warn("Response already committed, cannot handle throwable: {}", throwable.getMessage());
                return null;
            }

            return CommonResult.failed("系统错误");
        } catch (Exception handlerException) {
            logger.error("=== EXCEPTION IN EXCEPTION HANDLER ===");
            logger.error("Original throwable was: {} - {}", throwable.getClass().getSimpleName(), throwable.getMessage());
            logger.error("Handler exception:", handlerException);
            return createSafeErrorResponse("系统错误", response);
        }
    }

    /**
     * Creates a safe error response that doesn't trigger additional exceptions
     */
    private CommonResult<?> createSafeErrorResponse(String message, HttpServletResponse response) {
        try {
            if (response.isCommitted()) {
                logger.warn("Cannot create safe error response - response already committed");
                return null;
            }
            return CommonResult.failed(message);
        } catch (Exception e) {
            logger.error("Even safe error response failed:", e);
            // Return the most basic response possible using the protected constructor
            return CommonResult.failed();
        }
    }
}
