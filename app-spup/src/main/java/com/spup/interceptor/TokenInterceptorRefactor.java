package com.spup.interceptor;

import com.auth0.jwt.exceptions.AlgorithmMismatchException;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.api.ResultCodeEnum;
import com.huangdou.commons.utils.JWTUtil;
import com.spup.service.IAppOperateLogService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import java.io.IOException;
import java.io.PrintWriter;

@Slf4j
// @Component
public class TokenInterceptorRefactor implements HandlerInterceptor {
    @Autowired
    private JWTUtil jwtUtil;
    @Autowired
    private IAppOperateLogService iAppOperateLogService;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler) throws Exception {
        // return true;
        // Token is not used in current implementation but kept for future JWT
        // validation
        String token = request.getHeader("Authorization");
        String code = request.getParameter("code");
        log.info("code is -> " + code);
        log.info("token is -> " + token);
        if (request.getMethod().equals(RequestMethod.OPTIONS.name())) {
            log.info("**********options请求");
            return true;
        }
        Boolean DEBUG = false;
        log.info("entering token interceptor {}",request.getRequestURI());
        String requestURI = request.getRequestURI();
        String requestHost = request.getRemoteHost();
        if ("0:0:0:0:0:0:0:1".equals(requestHost)) {
            DEBUG = true;
        }

        if (DEBUG && requestURI.startsWith("/spup/miniprogram/jscode2session")) {
            log.info("Allowing login request: {}", requestURI);
            return true;
        }
        CommonResult<?> commonResult = null;
        String unionid = null;
        String openid = null;
        if (DEBUG) {
            unionid = "ojqzL0-hOlek3HMyLjhvKjTfDnnA";
            openid = "ouHc26KEbNy6EzNOTMv-RdR1SrnA";
            log.info("TokenInterceptor debug unionid -> " + unionid);
            log.info("TokenInterceptor debug openid -> " + openid);
        } else {
            // 1.校验JWT字符串
            if(!StringUtils.hasLength(token) && !StringUtils.hasLength(token)) {
                commonResult = CommonResult.failed(ResultCodeEnum.REQUEST_TOKEN_EMPTY); 
                String jsonObjectStr = objectMapper.writeValueAsString(commonResult);
                returnJson(response, jsonObjectStr);
                return false;            
            }

            try {
                DecodedJWT decodedJWT = jwtUtil.decodeToken(token);
                Claim unionidClaim = decodedJWT.getClaim("unionid");
                Claim openidClaim = decodedJWT.getClaim("openid");
                unionid = unionidClaim.asString();
                openid = openidClaim.asString();
                log.info("TokenInterceptor unionid from token -> " + unionid);
                log.info("TokenInterceptor openid from token -> " + openid);
                
            } catch (SignatureVerificationException | AlgorithmMismatchException e) {
                log.info("invalid signature or algorithm mismatch");
                e.printStackTrace();
                commonResult = CommonResult.failed(ResultCodeEnum.GET_TOKEN_KEY_FAILED);
            } catch (TokenExpiredException e) {
                log.info("token expired");
                e.printStackTrace();
                commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
            } catch (Exception e) {
                log.info("token parse failed");
                e.printStackTrace();
                commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_FAILED);
            }
            // String s_unionid = (String)request.getSession().getAttribute("unionid");

        }

        String s_unionid = null;
        HttpSession session = request.getSession(false);
        if(session == null) {
            session = request.getSession();
            session.setAttribute("unionid", unionid);
            session.setAttribute("openid", openid);

        } else {
            s_unionid = (String) session.getAttribute("unionid");
            if (!s_unionid.equals(unionid)) {
                //update unionid
                session.setAttribute("unionid", unionid);
                session.setAttribute("openid", openid);
            }
        }
        return true;

        // try {
            // if(!unionid.asString().equals(s_unionid)) {
            // request.getSession().setAttribute("unionid", unionid.asString());
            // request.getSession().setAttribute("openid", openid.asString());
            // }

        //     String s_unionid = null;
        //     HttpSession session = request.getSession(false);
        //     if (session != null) {
        //         s_unionid = (String) session.getAttribute("unionid");
        //         log.info("TokenInterceptor session unionid is " + s_unionid);
        //     } else {
        //         log.info("TokenInterceptor session is null");
        //     }

        //     if (!unionid.equals(s_unionid) || session == null) {
        //         request.getSession().setAttribute("unionid", unionid);
        //         request.getSession().setAttribute("openid", openid);
        //         log.info("TokenInterceptor********************** set session");
        //     }
        //     return true;
        // } catch (SignatureVerificationException | AlgorithmMismatchException e) {
        //     log.info("invalid signature or algorithm mismatch");
        //     e.printStackTrace();
        //     commonResult = CommonResult.failed(ResultCodeEnum.GET_TOKEN_KEY_FAILED);
        // } catch (TokenExpiredException e) {
        //     log.info("token expired");
        //     e.printStackTrace();
        //     commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        // } catch (Exception e) {
        //     log.info("token parse failed");
        //     e.printStackTrace();
        //     commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_FAILED);
        // }
        // String jsonObjectStr = objectMapper.writeValueAsString(commonResult);
        // returnJson(response, jsonObjectStr);
        // return false;
    }

    @Override
    public void postHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler, @Nullable ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler, @Nullable Exception ex) {
        log.info("entering afterCompletion of token interceptor");
        try {
            if (!response.isCommitted()) { // Add check before saving log
                iAppOperateLogService.saveLog(request);
            }
            iAppOperateLogService.saveLog(request);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void returnJson(HttpServletResponse response, String json) throws Exception {
        PrintWriter writer = null;
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try {
            writer = response.getWriter();
            writer.print(json);

        } catch (IOException e) {
        } finally {
            if (writer != null)
                writer.close();
        }
    }
}
