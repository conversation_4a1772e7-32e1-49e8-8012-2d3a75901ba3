package com.spup.service.appointment.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.utils.NumberGenerator;
import com.spup.counter.Item4Counter;
import com.spup.db.dao.appointment.AppAppointmentItemOrderDao;
import com.spup.db.dao.appointment.AppAppointmentItemSuborderDao;
import com.spup.db.dao.appointment.AppBatchDao;
import com.spup.db.entity.appointment.AppAppointmentItemOrder;
import com.spup.db.entity.appointment.AppAppointmentItemSuborder;
import com.spup.db.entity.appointment.AppBatch;
import com.spup.dto.AppAppointmentItemOrderRequest;
import com.spup.dto.AppAppointmentItemOrderResponse;
import com.spup.dto.OrderRequest;
import com.spup.enums.*;
import com.spup.service.appointment.IAppAppointmentItemOrderService;
import com.spup.service.appointment.IAppBatchService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;


import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AppAppointmentItemOrderServiceImpl implements IAppAppointmentItemOrderService {
    @Autowired
    private AppAppointmentItemOrderDao appAppointmentItemOrderDao;
    @Autowired
    private AppAppointmentItemSuborderDao appAppointmentItemSuborderDao;

    @Autowired
    private AppBatchDao appBatchDao;

    @Autowired
    private IAppBatchService iAppBatchService;

    @Autowired
    private Item4Counter item4Counter;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional
    public CommonResult<?> save(AppAppointmentItemOrderRequest orderRequest, String unionid)
            throws JsonProcessingException, JsonMappingException {
        
        String batchNo = orderRequest.getBatchNo();
        String contacts = orderRequest.getContacts();
        // 数据校验
        if (!StringUtils.hasLength(batchNo)) {
            return CommonResult.failed("场次为空");
        }
        
        // get batch
        Optional<AppBatch> batchOptional = appBatchDao.getByBatchNoAndBatchCategory(batchNo,
                BatchCategoryEnum.ITEM_FYPD.getCode());
        if (!batchOptional.isPresent()) {
            batchOptional = appBatchDao.getByBatchNoAndBatchCategory(batchNo,
                    BatchCategoryEnum.TEMP_ITEM_FYPD.getCode());
            if (!batchOptional.isPresent()) {
                return CommonResult.failed("场次不正确");
            }
        }
        AppBatch batch = batchOptional.get();

        if (batch.getBatchStatus() == BatchStatusEnum.CLOSED.getCode()) {
            return CommonResult.failed("场次已关闭");
        }

        ArrayNode node = (ArrayNode) objectMapper.readTree(contacts);
        int personNum = node.size();
        if (node.isEmpty()) {
            return CommonResult.failed("预约人为空");
        }
        if (personNum > 2) {
            return CommonResult.failed("预约人最多预约2人");
        }

        // 校验已预约人
        String batchDate = batch.getBatchDate();
        List<AppAppointmentItemSuborder> subordersByUnionid = appAppointmentItemSuborderDao.findByOnwerUnionid(unionid);
        List<AppAppointmentItemSuborder> todaySuborders = subordersByUnionid.stream()
                .filter(suborder -> suborder.getBatchDate().equals(batchDate))
                .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.SUCCESS.getCode()
                        || suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode())
                .collect(Collectors.toList());

        if (personNum + todaySuborders.size() > 2) {
            return CommonResult.failed("您已预约了" + todaySuborders.size() + "人, 每日最多可预约2人");
        }

        /**
         * 数据处理
         */
        DecimalFormat df = new DecimalFormat("00");

        LocalDateTime now = LocalDateTime.now();
        String orderNo = NumberGenerator.getOrderNo();

        // why not get the getNoHasBook() from item4Counter and compare with personNum first ???
        // then we can directly return failed result of the last seat is book.

        if(!item4Counter.canBook(batch.getBatchNo(),personNum)){
            return CommonResult.failed("名额不足");
        }
        
        Byte[] seatNos = new Byte[personNum];
        //todo 这里有个bug，用令牌形式分发座位号，虽然之前判断了是否余票充足，但是到这里以后抢座位时，如果是2人，只抢到一个座位时，会出现抢到的座位没有释放的情况。
        for (int i = 0; i < personNum; i++) {
            String subOrderNo = orderNo + df.format((i + 1));
            Byte seatNo = item4Counter.getNo(batch.getBatchNo(), subOrderNo);
            if (seatNo == -1) {
                //todo 第一个问题，这里如果没有位置了，需要回滚，而不是只是返回名额已被占用。不能用return;
                //todo 第二个问题，如果没有抢到全部座位，需要释放已抢的座位
                for (int j = 0; j < seatNos.length; j++) {
                    if(seatNos[j] != null && seatNos[j]>0) {
                        item4Counter.releaseNo(batch.getBatchNo(),seatNos[j]);
                    }
                }
                return CommonResult.failed("名额不足");
                //throw new RuntimeException("名额不足");
            }
            seatNos[i] = seatNo;
        }


        AppAppointmentItemOrder order = new AppAppointmentItemOrder();
        order.setOrderNo(orderNo);
        order.setBatchNo(batch.getBatchNo());
        order.setBatchDate(batch.getBatchDate());
        order.setBatchStartTime(batch.getBatchStartTime());
        order.setBatchEndTime(batch.getBatchEndTime());
        order.setOwnerUnionid(unionid);

        order.setOrderStatus(ItemOrderStatusEnum.SUCCESS.getCode());
        order.setOrderCategory(OrderCategoryEnum.getEnum(orderRequest.getCategory()).getCode());

        order.setCreateBy(unionid);
        order.setUpdateBy(unionid);
        order.setCreateTime(now);
        order.setUpdateTime(now);

        appAppointmentItemOrderDao.save(order);

        try {
            for (int i = 0; i < personNum; i++) {
                JsonNode jsonNode = node.get(i);
                AppAppointmentItemSuborder suborder = new AppAppointmentItemSuborder();

                suborder.setOrderNo(order.getOrderNo());
                suborder.setSuborderNo(order.getOrderNo() + df.format((i + 1)));

                suborder.setBatchNo(order.getBatchNo());
                suborder.setBatchDate(order.getBatchDate());
                suborder.setBatchStartTimeStr(order.getBatchStartTime());
                suborder.setBatchEndTimeStr(order.getBatchEndTime());

                suborder.setOnwerUnionid(order.getOwnerUnionid());
                suborder.setContactsName(jsonNode.findValue("name").asText());
                suborder.setContactsPhone(jsonNode.findValue("phone").asText());
                suborder.setContactsIdcardCategory(jsonNode.findValue("idcardCategory").numberValue().byteValue());
                suborder.setContactsIdcardNo(jsonNode.findValue("idcardNo").asText());

                suborder.setSeatNo(seatNos[i]);

                suborder.setSuborderStatus(OrderStatusEnum.SUCCESS.getCode());

                suborder.setCreateBy(unionid);
                suborder.setUpdateBy(unionid);
                suborder.setCreateTime(now);
                suborder.setUpdateTime(now);
                appAppointmentItemSuborderDao.save(suborder);
            }

            iAppBatchService.updateRemaining(batchNo, BatchCategoryEnum.ITEM_FYPD.getCode(), -1 * personNum);
            iAppBatchService.updateRemaining(batchNo, BatchCategoryEnum.TEMP_ITEM_FYPD.getCode(), -1 * personNum);
        } catch (Exception e){
            for (int j = 0; j < seatNos.length; j++) {
                 if(seatNos[j] != null && seatNos[j] > 0) {
                     item4Counter.releaseNo(order.getBatchNo(),seatNos[j]);
                 }
            }
            throw new RuntimeException("@预约失败");
        }
        return CommonResult.succeeded("");

    }

    /**
     * Improved version of save method with proper concurrency control
     * Fixes race conditions and ensures atomic seat allocation
     */
    @Override
    @Transactional(isolation = Isolation.SERIALIZABLE, rollbackFor = Exception.class)
    public CommonResult<?> newSave(AppAppointmentItemOrderRequest orderRequest, String unionid)
            throws JsonProcessingException, JsonMappingException {

        String batchNo = orderRequest.getBatchNo();
        String contacts = orderRequest.getContacts();

        // 数据校验
        if (!StringUtils.hasLength(batchNo)) {
            return CommonResult.failed("场次为空");
        }

        // Use synchronized block for the entire booking process to prevent race conditions
        synchronized (this.getClass()) {
            // 获取批次信息
            Optional<AppBatch> batchOptional = appBatchDao.getByBatchNoAndBatchCategory(batchNo,
                    BatchCategoryEnum.ITEM_FYPD.getCode());
            if (!batchOptional.isPresent()) {
                batchOptional = appBatchDao.getByBatchNoAndBatchCategory(batchNo,
                        BatchCategoryEnum.TEMP_ITEM_FYPD.getCode());
                if (!batchOptional.isPresent()) {
                    return CommonResult.failed("场次不正确");
                }
            }

            AppBatch batch = batchOptional.get();
            if (batch.getBatchStatus() == BatchStatusEnum.CLOSED.getCode()) {
                return CommonResult.failed("场次已关闭");
            }

            // 解析预约人信息
            ArrayNode node = (ArrayNode) objectMapper.readTree(contacts);
            int personNum = node.size();
            if (node.isEmpty()) {
                return CommonResult.failed("预约人为空");
            }
            if (personNum > 2) {
                return CommonResult.failed("预约人最多预约2人");
            }

            // 校验已预约人 - 使用数据库锁确保一致性
            String batchDate = batch.getBatchDate();
            List<AppAppointmentItemSuborder> subordersByUnionid = appAppointmentItemSuborderDao.findByOnwerUnionid(unionid);
            List<AppAppointmentItemSuborder> todaySuborders = subordersByUnionid.stream()
                    .filter(suborder -> suborder.getBatchDate().equals(batchDate))
                    .filter(suborder -> suborder.getSuborderStatus() == OrderStatusEnum.SUCCESS.getCode()
                            || suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode())
                    .collect(Collectors.toList());

            if (personNum + todaySuborders.size() > 2) {
                return CommonResult.failed("您已预约了" + todaySuborders.size() + "人, 每日最多可预约2人");
            }

            // 原子性检查和分配座位
            Byte[] seatNos = atomicSeatAllocation(batch.getBatchNo(), personNum);
            if (seatNos == null) {
                return CommonResult.failed("名额不足");
            }

            // 数据处理
            DecimalFormat df = new DecimalFormat("00");
            LocalDateTime now = LocalDateTime.now();
            String orderNo = NumberGenerator.getOrderNo();

            // 创建主订单
            AppAppointmentItemOrder order = createMainOrder(orderRequest, batch, unionid, orderNo, now);

            try {
                // 保存主订单
                appAppointmentItemOrderDao.save(order);

                // 创建子订单
                createSubOrders(node, order, seatNos, personNum, df, now, unionid);

                // 更新剩余票数
                updateRemainingTickets(batchNo, personNum);

                return CommonResult.succeeded("");

            } catch (Exception e) {
                // 发生异常时释放已分配的座位
                releaseSeatNumbers(batch.getBatchNo(), seatNos);
                throw new RuntimeException("预约失败: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 原子性座位分配 - 确保要么全部分配成功，要么全部失败
     */
    private Byte[] atomicSeatAllocation(String batchNo, int personNum) {
        // 在item4Counter的同步块内进行原子性检查和分配
        synchronized (item4Counter) {
            // 先检查是否有足够座位
            if (item4Counter.getNoHasBook(batchNo) < personNum) {
                return null;
            }

            Byte[] seatNos = new Byte[personNum];
            DecimalFormat df = new DecimalFormat("00");
            String tempOrderNo = "TEMP_" + System.currentTimeMillis();

            // 尝试分配所有座位
            for (int i = 0; i < personNum; i++) {
                String subOrderNo = tempOrderNo + df.format((i + 1));
                Byte seatNo = item4Counter.getNo(batchNo, subOrderNo);
                if (seatNo == -1) {
                    // 分配失败，释放已分配的座位
                    for (int j = 0; j < i; j++) {
                        if (seatNos[j] != null && seatNos[j] > 0) {
                            item4Counter.releaseNo(batchNo, seatNos[j]);
                        }
                    }
                    return null;
                }
                seatNos[i] = seatNo;
            }

            return seatNos;
        }
    }

    /**
     * 创建主订单
     */
    private AppAppointmentItemOrder createMainOrder(AppAppointmentItemOrderRequest orderRequest,
                                                   AppBatch batch, String unionid, String orderNo, LocalDateTime now) {
        AppAppointmentItemOrder order = new AppAppointmentItemOrder();
        order.setOrderNo(orderNo);
        order.setBatchNo(batch.getBatchNo());
        order.setBatchDate(batch.getBatchDate());
        order.setBatchStartTime(batch.getBatchStartTime());
        order.setBatchEndTime(batch.getBatchEndTime());
        order.setOwnerUnionid(unionid);
        order.setOrderStatus(ItemOrderStatusEnum.SUCCESS.getCode());
        order.setOrderCategory(OrderCategoryEnum.getEnum(orderRequest.getCategory()).getCode());
        order.setCreateBy(unionid);
        order.setUpdateBy(unionid);
        order.setCreateTime(now);
        order.setUpdateTime(now);
        return order;
    }

    /**
     * 创建子订单
     */
    private void createSubOrders(ArrayNode node, AppAppointmentItemOrder order, Byte[] seatNos,
                               int personNum, DecimalFormat df, LocalDateTime now, String unionid) {
        for (int i = 0; i < personNum; i++) {
            JsonNode jsonNode = node.get(i);
            AppAppointmentItemSuborder suborder = new AppAppointmentItemSuborder();

            suborder.setOrderNo(order.getOrderNo());
            suborder.setSuborderNo(order.getOrderNo() + df.format((i + 1)));
            suborder.setBatchNo(order.getBatchNo());
            suborder.setBatchDate(order.getBatchDate());
            suborder.setBatchStartTimeStr(order.getBatchStartTime());
            suborder.setBatchEndTimeStr(order.getBatchEndTime());
            suborder.setOnwerUnionid(order.getOwnerUnionid());
            suborder.setContactsName(jsonNode.findValue("name").asText());
            suborder.setContactsPhone(jsonNode.findValue("phone").asText());
            suborder.setContactsIdcardCategory(jsonNode.findValue("idcardCategory").numberValue().byteValue());
            suborder.setContactsIdcardNo(jsonNode.findValue("idcardNo").asText());
            suborder.setSeatNo(seatNos[i]);
            suborder.setSuborderStatus(OrderStatusEnum.SUCCESS.getCode());
            suborder.setCreateBy(unionid);
            suborder.setUpdateBy(unionid);
            suborder.setCreateTime(now);
            suborder.setUpdateTime(now);

            appAppointmentItemSuborderDao.save(suborder);
        }
    }

    /**
     * 更新剩余票数
     */
    private void updateRemainingTickets(String batchNo, int personNum) {
        iAppBatchService.updateRemaining(batchNo, BatchCategoryEnum.ITEM_FYPD.getCode(), -1 * personNum);
        iAppBatchService.updateRemaining(batchNo, BatchCategoryEnum.TEMP_ITEM_FYPD.getCode(), -1 * personNum);
    }

    /**
     * 释放座位号
     */
    private void releaseSeatNumbers(String batchNo, Byte[] seatNos) {
        if (seatNos != null) {
            for (Byte seatNo : seatNos) {
                if (seatNo != null && seatNo > 0) {
                    item4Counter.releaseNo(batchNo, seatNo);
                }
            }
        }
    }

    @Override
    public CommonResult<?> getList(String unionid) {
        LocalDate halfYearBefore = LocalDate.now().minusDays(180);
        List<AppAppointmentItemOrder> appAppointmentOrders = appAppointmentItemOrderDao.findByOwnerUnionid(unionid);
        appAppointmentOrders.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));

        List<AppAppointmentItemOrderResponse> orderResponses = appAppointmentOrders.stream()
                .filter(order -> halfYearBefore.format(DateTimeFormatter.ofPattern("yyyyMMdd")).compareTo(order.getBatchDate())<=0)
                .map(order -> {
            AppAppointmentItemOrderResponse orderResponse = new AppAppointmentItemOrderResponse();
            BeanUtils.copyProperties(order, orderResponse);
            List<AppAppointmentItemSuborder> appAppointmentSuborders = appAppointmentItemSuborderDao
                    .findByOrderNo(order.getOrderNo());
            orderResponse.setSuborders(appAppointmentSuborders);
            return orderResponse;
        }).collect(Collectors.toList());

        return CommonResult.succeeded(orderResponses);
    }

    @Override
    public CommonResult<?> cancel(String orderNo, String unionid) {

        Optional<AppAppointmentItemOrder> orderOptional = appAppointmentItemOrderDao.findByOrderNo(orderNo);
        if (!orderOptional.isPresent()) {
            return CommonResult.failed("订单未找到");
        }

        AppAppointmentItemOrder order = orderOptional.get();
        if (!unionid.equals(order.getOwnerUnionid())) {
            return CommonResult.failed("无权操作");
        }
        order.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
        order.setUpdateBy(unionid);
        appAppointmentItemOrderDao.save(order);

        List<AppAppointmentItemSuborder> suborders = appAppointmentItemSuborderDao.findByOrderNo(orderNo);
        suborders.forEach(suborder -> {
            suborder.setSuborderStatus(OrderStatusEnum.CANCELED.getCode());
            suborder.setUpdateBy(unionid);
            appAppointmentItemSuborderDao.save(suborder);

            item4Counter.releaseNo(suborder.getBatchNo(), suborder.getSeatNo());
        });
        int updateNum = suborders.size();
        // 更新时刻表
        AppBatch appBatch = iAppBatchService.updateRemaining(order.getBatchNo(), BatchCategoryEnum.ITEM_FYPD.getCode(),
                updateNum);
        if (appBatch == null) {
            iAppBatchService.updateRemaining(order.getBatchNo(), BatchCategoryEnum.TEMP_ITEM_FYPD.getCode(), updateNum);
        }
        return CommonResult.succeeded(updateNum);
    }

    @Override
    public CommonResult<?> breaked(String orderNo, String unionid) {
        Optional<AppAppointmentItemOrder> orderOptional = appAppointmentItemOrderDao.findByOrderNo(orderNo);
        if (!orderOptional.isPresent()) {
            return CommonResult.failed("订单未找到");
        }

        AppAppointmentItemOrder order = orderOptional.get();
        order.setOrderStatus(OrderStatusEnum.BREAKED.getCode());
        order.setUpdateBy(unionid);
        appAppointmentItemOrderDao.save(order);

        List<AppAppointmentItemSuborder> suborders = appAppointmentItemSuborderDao.findByOrderNo(orderNo);
        suborders.forEach(suborder -> {
            if (suborder.getSuborderStatus().shortValue() == OrderStatusEnum.SUCCESS.getCode()) {
                suborder.setSuborderStatus(OrderStatusEnum.BREAKED.getCode());
                suborder.setUpdateBy(unionid);
                appAppointmentItemSuborderDao.save(suborder);
                item4Counter.releaseNo(suborder.getBatchNo(), suborder.getSeatNo());
            }
        });
        int updateNum = suborders.size();
        iAppBatchService.updateRemaining(order.getBatchNo(), OrderCategoryEnum.ITEM_FYPD.getCode(), updateNum);

        return CommonResult.succeeded(updateNum);
    }

    @Override
    public CommonResult<?> delete(String orderNo, String unionid) {
        Optional<AppAppointmentItemOrder> orderOptional = appAppointmentItemOrderDao.findByOrderNo(orderNo);
        if (!orderOptional.isPresent()) {
            return CommonResult.failed("订单未找到");
        }
        AppAppointmentItemOrder order = orderOptional.get();
        if (!unionid.equals(order.getOwnerUnionid())) {
            return CommonResult.failed("无权操作");
        }
        order.setUpdateBy(unionid);
        appAppointmentItemOrderDao.delete(order);

        List<AppAppointmentItemSuborder> suborders = appAppointmentItemSuborderDao.findByOrderNo(orderNo);
        suborders.forEach(suborder -> {
            suborder.setUpdateBy(unionid);
            appAppointmentItemSuborderDao.delete(suborder);
        });

        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> checkout(OrderRequest orderRequest, String unionid) {
        String suborderNo = orderRequest.getSubOrderId();
        Optional<AppAppointmentItemSuborder> suborderOptional = appAppointmentItemSuborderDao
                .findBySuborderNo(suborderNo);
        if (!suborderOptional.isPresent()) {
            return CommonResult.failed("未找到该预约信息");
        }
        AppAppointmentItemSuborder suborder = suborderOptional.get();
        if (suborder.getSuborderStatus() == OrderStatusEnum.CANCELED.getCode()) {
            return CommonResult.failed("该预约已取消");
        }
        if (suborder.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode()) {
            return CommonResult.failed("该预约已核销");
        }
        suborder.setSuborderStatus(OrderStatusEnum.FINISHED.getCode());
        suborder.setUpdateTime(LocalDateTime.now());
        appAppointmentItemSuborderDao.save(suborder);

        List<AppAppointmentItemSuborder> suborders = appAppointmentItemSuborderDao.findByOrderNo(suborder.getOrderNo());
        boolean result = suborders.stream()
                .allMatch(item -> item.getSuborderStatus() == OrderStatusEnum.FINISHED.getCode());
        if (result) {
            Optional<AppAppointmentItemOrder> orderOptional = appAppointmentItemOrderDao
                    .findByOrderNo(suborder.getOrderNo());
            AppAppointmentItemOrder order = orderOptional.get();
            order.setOrderStatus(OrderStatusEnum.FINISHED.getCode());
            order.setOrderNo(suborder.getOrderNo());
            order.setUpdateTime(LocalDateTime.now());
            appAppointmentItemOrderDao.save(order);
        }
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> getSuborderDetail(String suborderNo) {
        Optional<AppAppointmentItemSuborder> suborderOptional = appAppointmentItemSuborderDao
                .findBySuborderNo(suborderNo);
        if (!suborderOptional.isPresent()) {
            return CommonResult.failed("未找到该预约信息");
        }
        AppAppointmentItemSuborder suborder = suborderOptional.get();
        return CommonResult.succeeded(suborder);
    }
}
