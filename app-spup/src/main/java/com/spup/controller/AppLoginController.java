package com.spup.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.utils.JWTUtil;
import com.spup.db.entity.authority.AppCustomer;
import com.spup.service.IAppCustomerService;
import com.spup.service.IAppOperateLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.Objects;

@Api(tags = "登录")
@RestController
@RequestMapping("/miniprogram")
@PropertySource("classpath:/miniprogram.properties")
public class AppLoginController {
    private static final Logger logger = LoggerFactory.getLogger(AppLoginController.class);

    @Autowired
    private JWTUtil jwtUtil;

    @Autowired
    private Environment env;

    @Autowired
    private IAppCustomerService iAppCustomerService;

    @Resource
    private IAppOperateLogService iAppOperateLogService;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "换取登录信息")
    @GetMapping(value="/jscode2session/{code}")
    public CommonResult<?> jscode2session (@PathVariable String code, HttpServletRequest req) throws UnsupportedEncodingException, JsonProcessingException {

        logger.info("entry into app jscode2session, code: {}", code);
        //智能导览小程序
        String appid = env.getProperty("miniProgram.appid");
        String secret = env.getProperty("miniProgram.secret");

        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code";
        // String responseStr = HttpRequestUtil.httpGet();
        ResponseEntity<String> responseEntity  = restTemplate.getForEntity(url, String.class);
        String responseStr = responseEntity.getBody();

        // Create mock return code
        // responseStr = "{\"openid\":\"ouHc26KEbNy6EzNOTMv-RdR1SrnA\",\"session_key\":\"SESSIONKEY\",\"unionid\":\"ojqzL0-hOlek3HMyLjhvKjTfDnnA\"}";

        ObjectNode objectNode = (ObjectNode)objectMapper.readTree(responseStr);

        if(Objects.isNull(objectNode.get("errcode"))){
            String unionid = objectNode.get("unionid").asText();
            String openid = objectNode.get("openid").asText();

            //持久化客户信息
            AppCustomer customer = iAppCustomerService.save(unionid,openid);
            ObjectNode node = objectMapper.createObjectNode();
            node.put("unionid", unionid);
            node.put("openid", openid);
            node.put("isNew", customer!=null?1:0);
            //token, jwt
            node.put("token",jwtUtil.getToken(unionid,openid));
            logger.info("小程序认证成功：{}",node.asText());
            try {
                iAppOperateLogService.saveLog(req);
            } catch (Exception e){
                e.printStackTrace();
            }

            return CommonResult.succeeded(node);
        } else {
            Integer errcode = objectNode.get("errcode").intValue();
            logger.info("小程序认证失败：{}",errcode);
            return CommonResult.failed(1000,errcode+":"+objectNode.get("errmsg").asText());

        }

    }

}
