package com.spup.controller;

import java.util.Optional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.spup.service.IAppOperateLogService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
@RequestMapping("/anniversary")
public class AnniversaryActivityMapController {

    final static String STATE = "123";

    @Resource
    ObjectMapper objectMapper;
    @Resource
    RestTemplate restTemplate;
    @Resource
    IAppOperateLogService operationLog;

    @GetMapping("/activityMap")
    public String showActivityMap(@NotBlank String code, @NotBlank String state, @NotNull HttpServletRequest request)
            throws Exception {

        log.info("entering activity map");
        if (!STATE.equals(state)) {
            log.info("wrong state, faked request entered not through qrcode");
            throw new IllegalArgumentException();
        }
        String unioinid = code2session(code).orElseThrow(() -> new Exception());
        HttpSession session = request.getSession();
        session.setAttribute("unionid", unioinid);

        try {
            operationLog.saveLog(request);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return "activity-map";
    }

    private Optional<String> code2session(@NotBlank String code) {
//https://api.weixin.qq.com/sns/jscode2session
        StringBuilder urlBuilder = new StringBuilder("https://api.weixin.qq.com/sns/oauth2/access_token");
        String url = urlBuilder.append("?appid=").append("wxb971efaed01158f4")    //wx1fe676e844bb15e6
                .append("&secret=").append("8359f543f4c6df82c7a15c190262f28b")    //65f96a8d986e7418b01e31d5090d6fba
                .append("&code=").append(code)
                .append("&grant_type=authorization_code").toString();

        try {
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            String responseStr = responseEntity.getBody();
            JsonNode resultJson = objectMapper.readTree(responseStr);
            log.info("response ->"  + responseStr);
            if (resultJson.get("errcode") == null) {
                // String access_token = resultJson.get("access_token").asText();
                String openid = resultJson.get("openid").textValue();
                // String unionid = resultJson.get("unionid").textValue();
                return Optional.of(openid);
            } else {
                throw new Exception();
            }
        } catch (Exception e) {
            log.error("get token with code failed.");
            return Optional.empty();
        }
    }
}
