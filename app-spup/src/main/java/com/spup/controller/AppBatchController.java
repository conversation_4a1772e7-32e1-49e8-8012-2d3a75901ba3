package com.spup.controller;

import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.utils.DateTimeUtil;
import com.spup.counter.Item4Counter;
import com.spup.db.entity.appointment.AppBatch;
import com.spup.db.entity.appointment.AppWorkday;
import com.spup.db.entity.sys.AppConfig;
import com.spup.enums.BatchCategoryEnum;
import com.spup.enums.OrderCategoryEnum;
import com.spup.enums.WorkdayEnum;
import com.spup.service.IAppointmentService;
import com.spup.service.appointment.IAppBatchService;
import com.spup.service.IAppConfigService;
import com.spup.service.IAppWorkdayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Api(tags = "场次查询")
@RestController
@RequestMapping("/appointment")
public class AppBatchController {

    @Resource
    private IAppBatchService iAppBatchService;

    @Resource
    private IAppWorkdayService iAppWorkdayService;

    @Resource
    private IAppConfigService iAppConfigService;
    @Resource
    private IAppointmentService iAppointmentService;

    @Resource
    private Item4Counter item4Counter;
    // private int week = 1;

    @ApiOperation(value = "获取预约时间")
    @RequestMapping(value = "/getTime/{category}", method = RequestMethod.GET)
    public CommonResult<?> getTime(@PathVariable Number category) throws ParseException {
        Calendar start_c = Calendar.getInstance();

        Calendar end_c = Calendar.getInstance();
        end_c.add(Calendar.DAY_OF_MONTH, 3);

        // int start_week = start_c.get(Calendar.DAY_OF_WEEK);
        if (category.byteValue() == OrderCategoryEnum.TEAM.getCode().byteValue()) {
            start_c.add(Calendar.DAY_OF_MONTH, 7);
            end_c.add(Calendar.DAY_OF_MONTH, 7);
        }

        String startDate = DateTimeUtil.getTime(DateTimeUtil.PATTERN_7, start_c);
        String endDate = DateTimeUtil.getTime(DateTimeUtil.PATTERN_7, end_c);

        return CommonResult.succeeded(iAppBatchService.getListByDate(category.byteValue(), startDate, endDate));
    }

    @ApiOperation(value = "获取可预约日期范围")
    @RequestMapping(value = "/getAvailableTime/{category}", method = RequestMethod.GET)
    public CommonResult<?> getAvailableTime(@PathVariable Number category) {
        log.info("/getAvailableTime/category:" + category);
        Map<String, Object> configsByGroup = iAppConfigService.getConfigsByGroup("appointment.category." + category);
        System.out.println("date config:"+configsByGroup);
        String exhibitionId = "team";
        if (category.intValue() == BatchCategoryEnum.TICKET.getCode().intValue()) {
            exhibitionId = "ticket";
        }
        // TODO configByGroup 中没有offset.day
        // 默认查询30天数据，然后再进行筛选
        LocalDate date = LocalDate.now();
        LocalDate endDate = date.plusDays(Integer.parseInt((String) configsByGroup.get("offset.day"))
                + Integer.parseInt((String) configsByGroup.get("appointment.days")));
        date = date.plusDays(Integer.parseInt((String) configsByGroup.get("offset.day")));

        String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String yyyyMMdd_end = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        List<AppWorkday> listByDate = iAppWorkdayService.getListByDate(yyyyMMdd, yyyyMMdd_end);
        for (int i = 0; i < listByDate.size(); i++) {
            AppWorkday workDay = listByDate.get(i);
            String day = workDay.getDay();
            LocalDate dateOfDay = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 判断场馆是否是闭馆
            int status = workDay.getIsWorkday();
            System.out.println(workDay.getDay()+" status:"+status);
            if (status == WorkdayEnum.OPEN_DAY.getCode()) {
                workDay.setDayRemark("");
                //判断日期是否已经设置过规则，设置过规则的，按规则处理
                workDay.setIsWorkday(iAppWorkdayService.getDayStatus(workDay,exhibitionId));
                if(workDay.getIsWorkday()==WorkdayEnum.REST_DAY.getCode()){
                    workDay.setDayRemark("闭馆");
                }
            } else {
                //判断是否是节假日或者周一
                Boolean availableByHoliday = iAppointmentService.isAvailableByHoliday(exhibitionId, dateOfDay);
                Boolean availableByHoliday2 = iAppointmentService.isAvailableByHoliday("all", dateOfDay);
                if (!availableByHoliday || !availableByHoliday2) {
                    if (!StringUtils.hasLength(workDay.getDayRemark())) {
                        workDay.setDayRemark("闭馆");
                    }
                } else { //如果不是节假日或者周一，所有的关闭状态，显示已约满
                    if (!StringUtils.hasLength(workDay.getDayRemark())) {
                        workDay.setDayRemark("已约满");
                    }
                }
            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("pageShowDays", configsByGroup.get("appointment.default_show_days"));
        result.put("dayList", listByDate);
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "获取可预约日期范围(针对临展)")
    @RequestMapping(value = "/getAvailableTime/{exhibitionNo}/{type}", method = RequestMethod.GET)
    public CommonResult<?> getAvailableTime(@PathVariable String exhibitionNo, @PathVariable Number type) {
        log.info("/getAvailableTime/category/type:" + exhibitionNo);
        
        Map<String, Object> configsByGroup = iAppConfigService
                .getConfigsByGroup("appointment.category.16." + exhibitionNo);
        // 默认查询30天数据，然后再进行筛选
        LocalDate date = LocalDate.now();
        LocalDate endDate = date.plusDays(Integer.parseInt((String) configsByGroup.get("offset.day"))
                + Integer.parseInt((String) configsByGroup.get("appointment.days")));
        date = date.plusDays(Integer.parseInt((String) configsByGroup.get("offset.day")));

        String yyyyMMdd = date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String yyyyMMdd_end = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        List<AppWorkday> listByDate = iAppWorkdayService.getListByDate(yyyyMMdd, yyyyMMdd_end);
        for (int i = 0; i < listByDate.size(); i++) {
            AppWorkday workDay = listByDate.get(i);
            String day = workDay.getDay();
            workDay.setIsWorkday(1);
            LocalDate dateOfDay = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 判断场馆是否是闭馆
            int status = workDay.getIsWorkday();
            System.out.println(workDay.getDay()+" status:"+status);
            if (status == WorkdayEnum.OPEN_DAY.getCode()) {
                workDay.setDayRemark("");
                //判断日期是否已经设置过规则，设置过规则的，按规则处理
                workDay.setIsWorkday(iAppWorkdayService.getDayStatus(workDay,exhibitionNo));
                if(workDay.getIsWorkday()==WorkdayEnum.REST_DAY.getCode()){
                    workDay.setDayRemark("闭馆");
                }
            } else {
                //判断是否是节假日或者周一
                Boolean availableByHoliday = iAppointmentService.isAvailableByHoliday(exhibitionNo, dateOfDay);
                if (!availableByHoliday ) {
                    if (!StringUtils.hasLength(workDay.getDayRemark())) {
                        workDay.setDayRemark("闭馆");
                    }
                } else { //如果不是配置中的假日，所有的关闭状态，显示已约满
                    if (!StringUtils.hasLength(workDay.getDayRemark())) {
                        workDay.setDayRemark("已约满");
                    }
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("pageShowDays", configsByGroup.get("appointment.default_show_days"));
        result.put("dayList", listByDate);
        return CommonResult.succeeded(result);
    }

    @ApiOperation(value = "获取选择日期预约情况", notes = "category：1门票,2团队,4展项；date格式：yyyyMMdd")
    @RequestMapping(value = "/getDetailByDate/{category}/{date}", method = RequestMethod.GET)
    public CommonResult<List<AppBatch>> getDetailByDate(@PathVariable Byte category, @PathVariable String date) {
        List<AppBatch> listByDate = iAppBatchService.getListByDate(category, date);

        Optional<AppWorkday> workdayOpt = iAppWorkdayService.getByDate(date);
        AppWorkday workday = workdayOpt.orElse(null);

        String exhibitionId = "team";
        if (category.intValue() == BatchCategoryEnum.TICKET.getCode().intValue()) {
            exhibitionId = "ticket";
        }

        int status = iAppWorkdayService.getDayStatus(workday, exhibitionId);
        listByDate.stream()
                .sorted(Comparator.comparing(AppBatch::getBatchStartTime))
                .forEach(batch -> {
                    if (status == 0) {
                        batch.setBatchStatus((byte) 0);
                    }
                });
        return CommonResult.succeeded(listByDate);
    }

    @ApiOperation(value = "获取选择日期预约情况", notes = "category：16临展；date格式：yyyyMMdd")
    @RequestMapping(value = "/getDetailByDate/{category}/{exhibitionNo}/{dateStr}", method = RequestMethod.GET)
    public CommonResult<List<AppBatch>> getDetailByDate(@PathVariable Byte category, @PathVariable String exhibitionNo,
            @PathVariable String dateStr) {

        // if (exhibitionNo.equals("fypd") && "20250524".equals(dateStr) || "20250525".equals(dateStr)) {
        //     return CommonResult.succeeded(getAvailableTimeSpanOf2AnniversaryOfFypd(exhibitionNo, category, dateStr));
        // }
        List<AppBatch> listByDate = iAppBatchService.getListByDate(exhibitionNo, category, dateStr);

        AppWorkday workday = new AppWorkday();
        workday.setDay(dateStr);
        workday.setIsWorkday(1);
        int status = iAppWorkdayService.getDayStatus(workday, exhibitionNo);
        listByDate.stream()
                .sorted(Comparator.comparing(AppBatch::getBatchStartTime))
                .forEach(batch -> {
                    if (status == 0) {
                        batch.setBatchStatus((byte) 0);
                    }
                    //如果是飞阅浦东，还需检验缓存座位数据联合判断剩余数，不用数据库的字段返回可预约票数
                    if("fypd".equals(exhibitionNo)){
                        int availableSeatCount = item4Counter.getSeatAvailableCount(batch.getBatchNo());
                        batch.setTicketRemaining(availableSeatCount);
                    }
                });
        return CommonResult.succeeded(listByDate);
    }

    private List<AppBatch> getAvailableTimeSpanOf2AnniversaryOfFypd(String exhibitionNo, Byte category,
            String dateStr) {
        List<AppBatch> listByDate = iAppBatchService.getListByDate(exhibitionNo, category, dateStr);
        // LocalTime startDayTime = LocalTime.of(9, 30, 00);
        // LocalTime endDayTime = LocalTime.of(15, 30, 00);
        LocalTime midDayTime = LocalTime.of( 12, 30, 10);
        //  LocalDateTime dateTime = LocalDateTime.now();
        //LocalTime dateTime = LocalTime.of(14,35,00);
        LocalTime dateTime = LocalTime.now();

        // if not in the available time span, return null
        /*if (dateTime.isBefore(startDayTime) || dateTime.isAfter(endDayTime)) {
            return Collections.emptyList();
        }*/
        if (dateTime.isBefore(midDayTime)) {
            // before mid dayu
            return listByDate.stream().filter(appbatch -> {
                String timeStr = appbatch.getBatchStartTime();
                return (timeStr.startsWith("10") || timeStr.startsWith("11") || timeStr.startsWith("12"));
            }).collect(Collectors.toList());
        }

        if (dateTime.isAfter(midDayTime)) {
            return listByDate.stream().filter(appbatch -> {
                String timeStr = appbatch.getBatchStartTime();
                return (timeStr.startsWith("13") || timeStr.startsWith("14") || timeStr.startsWith("15"));
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @RequestMapping(value = "/initTicket/{startDate}_{endDate}")
    public CommonResult<?> initTicket(@PathVariable String startDate, @PathVariable String endDate) {
        LocalDate startDateForLocal = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate endDateForLocal = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        return CommonResult.succeeded(iAppBatchService.initTicket(startDateForLocal, endDateForLocal));
    }

    @RequestMapping(value = "/initTeam/{startDate}_{endDate}")
    public CommonResult<?> initTeam(@PathVariable String startDate, @PathVariable String endDate) {
        LocalDate startDateForLocal = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate endDateForLocal = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        return CommonResult.succeeded(iAppBatchService.initTeam(startDateForLocal, endDateForLocal));
    }

    @RequestMapping(value = "/initItem/{startDate}_{endDate}")
    public CommonResult<?> initItem(@PathVariable String startDate, @PathVariable String endDate) {
        LocalDate startDateForLocal = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate endDateForLocal = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        return CommonResult.succeeded(iAppBatchService.initItem(startDateForLocal, endDateForLocal));
    }

    @RequestMapping(value = "/config")
    public CommonResult<?> config() {
        return CommonResult.succeeded(iAppConfigService.getAll());
    }
    @RequestMapping(value = "/config/set/{id}/{value}")
    public CommonResult<?> updateConfig(@PathVariable Long id,
                                        @PathVariable String value) {
        Optional<AppConfig> configOpt = iAppConfigService.findById(id);
        if(!configOpt.isPresent()){
            return CommonResult.failed("未找到");
        }
        AppConfig config = configOpt.get();
        config.setRuleValue(value);
        return CommonResult.succeeded(iAppConfigService.save(config));
    }
    // @GetMapping("/initItem")
    // public void getMethodName() {

    // log.info("starting configure tasks");
    // itemTask.configureTasks();
    // }
}
