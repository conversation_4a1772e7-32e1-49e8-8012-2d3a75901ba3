package com.spup.controller;

import com.huangdou.commons.api.CommonResult;
import com.spup.dto.OrderRequest;
import com.spup.enums.OrderCategoryEnum;
import com.spup.service.IOrderService;
import com.spup.service.appointment.IAppAppointmentTeamOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

@Api(tags = "核销")
@RestController
@RequestMapping("/check")
public class OrderCheckoutController {

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private IAppAppointmentTeamOrderService iAppAppointmentTeamOrderService;

    @ApiOperation(value = "核销")
    @PostMapping(value = "/checkout")
    public CommonResult<?> checkout(@RequestBody OrderRequest orderRequest, HttpServletRequest request) {
        String unionid = (String) request.getSession().getAttribute("unionid");

        OrderCategoryEnum _enum = OrderCategoryEnum.getEnum(orderRequest.getOrderCategory());

        IOrderService service = (IOrderService) applicationContext.getBean(_enum.getServiceClass());

        return service.checkout(orderRequest, unionid);
    }

    @ApiOperation(value = "获取预约记录明细")
    @GetMapping(value = "/get/{orderCategory}/{subOrderNo}")
    public CommonResult<?> get(@PathVariable Byte orderCategory, @PathVariable String subOrderNo)
            throws ParseException {

        OrderCategoryEnum _enum = OrderCategoryEnum.getEnum(orderCategory);

        IOrderService service = (IOrderService) applicationContext.getBean(_enum.getServiceClass());

        return service.getSuborderDetail(subOrderNo);
    }

    @ApiOperation(value = "获取当日预约来访团队")
    @GetMapping(value = "/getTeamOrder")
    public CommonResult<?> getTeamOrder() {

        return iAppAppointmentTeamOrderService.getTodayTeamOrder();
    }

}
