package com.spup.counter;

import com.spup.db.dao.appointment.AppAppointmentItemSuborderDao;
import com.spup.db.entity.appointment.AppAppointmentItemSuborder;
import com.spup.db.entity.appointment.AppBatch;
import com.spup.enums.BatchCategoryEnum;
import com.spup.enums.OrderCategoryEnum;
import com.spup.enums.OrderStatusEnum;
import com.spup.service.appointment.IAppBatchService;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class Item4Counter {
    @Resource
    private IAppBatchService iAppBatchService;
    @Resource
    private AppAppointmentItemSuborderDao appAppointmentItemSuborderDao;
    private Map<String, Map<Byte, String>> subOrderNoMap = new ConcurrentHashMap<>();

    public void initMap() {
        synchronized (subOrderNoMap) {
            subOrderNoMap.clear();
            String yyyyMMdd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            Map<String, List<AppBatch>> listByDate = iAppBatchService
                    .getListByDate(OrderCategoryEnum.ITEM_FYPD.getCode(), yyyyMMdd, yyyyMMdd);
            List<AppBatch> list = listByDate.get(yyyyMMdd);
            if (list == null)
                return;
            for (AppBatch appBatch : list) {
                String batchNo = appBatch.getBatchNo();
                Integer ticketTotal = appBatch.getTicketTotal();
                Map<Byte, String> map = new ConcurrentHashMap<>();
                for (int i = 0; i < ticketTotal; i++) {
                    map.put((byte) (i + 1), "");
                }
                subOrderNoMap.put(batchNo, map);
            }
        }
        loadTempBatch();
    }

    public void loadTempBatch() {
        synchronized (subOrderNoMap) {
            String yyyyMMdd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            Map<String, List<AppBatch>> listByDate = iAppBatchService
                    .getListByDate(BatchCategoryEnum.TEMP_ITEM_FYPD.getCode(), yyyyMMdd, yyyyMMdd);
            List<AppBatch> list = listByDate.get(yyyyMMdd);
            if (list != null) {
                for (AppBatch appBatch : list) {
                    String batchNo = appBatch.getBatchNo();
                    Integer ticketTotal = appBatch.getTicketTotal();
                    Map<Byte, String> map = new ConcurrentHashMap<>();
                    for (int i = 0; i < ticketTotal; i++) {
                        map.put((byte) (i + 1), "");
                    }
                    subOrderNoMap.put(batchNo, map);
                }
            }
        }
    }

    public void clear() {
        initMap();
    }

    public void loadHasBook() {
        String yyyyMMdd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<AppAppointmentItemSuborder> subordersByDate = appAppointmentItemSuborderDao.findByBatchDate(yyyyMMdd);
        for (AppAppointmentItemSuborder itemSuborder : subordersByDate) {
            if (itemSuborder.getSuborderStatus() == OrderStatusEnum.CANCELED.getCode()
                    || itemSuborder.getSuborderStatus() == OrderStatusEnum.BREAKED.getCode()) {
                continue;
            }
            String batchNo = itemSuborder.getBatchNo();
            Map<Byte, String> map = subOrderNoMap.get(batchNo);
            map.put(itemSuborder.getSeatNo(), itemSuborder.getSuborderNo());
        }
    }

    public Byte getNo(String batchNo, String subOrderNo) {
        synchronized (subOrderNoMap) {
            Map<Byte, String> map = subOrderNoMap.get(batchNo);
            Iterator<Byte> iterator = map.keySet().iterator();
            while (iterator.hasNext()) {
                Byte key = iterator.next();
                String value = map.get(key);
                if (value == null || value.isEmpty()) {
                    map.put(key, subOrderNo);
                    return key;
                }
            }
            return -1;
        }
    }

    public int getSeatAvailableCount(String batchNo) {
        synchronized (subOrderNoMap) {
            Map<Byte, String> map = subOrderNoMap.get(batchNo);
            // return 0 means all of the seat is booked, and no any seat available;
            // but could not get any info from subOrderNoMap by the batchNo means other
            // reason,
            // so it is better to return -1, or throw exception;
            if (map == null)
                return 0;
            Iterator<Byte> iterator = map.keySet().iterator();
            int noHasBook = 0;
            while (iterator.hasNext()) {
                Byte key = iterator.next();
                String value = map.get(key);
                if (!StringUtils.hasLength(value)) {
                    noHasBook++;
                }
            }
            return noHasBook;
        }
    }

    public Boolean canBook(String batchNo, int seatNum) {
        synchronized (subOrderNoMap) {
            return getSeatAvailableCount(batchNo) >= seatNum;
        }
    }

    public void releaseNo(String batchNo, byte no) {
        synchronized (subOrderNoMap) {
            Map<Byte, String> map = subOrderNoMap.get(batchNo);
            map.put(no, "");
        }
    }

}
