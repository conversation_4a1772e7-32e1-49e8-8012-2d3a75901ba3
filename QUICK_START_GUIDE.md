# SPUP项目快速开始指南

## 🚀 重新组织后的项目快速编译部署

### 📋 项目结构
```
spup-root/
├── spup-common/          # 公共工具和配置
├── spup-data/           # 数据访问层
├── spup-core/           # 核心业务逻辑
├── spup-activity/       # 活动相关功能
├── spup-admin-web/      # 管理后台 → spup-admin.war
├── spup-user-web/       # 用户端应用 → spup.war
└── scripts/             # 部署脚本
```

## ⚡ 快速命令

### 1. 最快编译方式
```bash
# 使用Makefile (推荐)
make package

# 或使用Maven直接命令
mvn clean package -DskipTests
```

### 2. 分模块编译
```bash
# 只编译管理后台
make admin

# 只编译用户端
make user

# 编译基础模块
mvn clean install -pl spup-common,spup-data,spup-core,spup-activity -DskipTests
```

### 3. 一键部署
```bash
# 编译并部署到Tomcat
make deploy

# 或分步执行
make package
make deploy-tomcat
```

## 🔧 详细编译步骤

### 方式一：使用Makefile (最简单)
```bash
# 查看所有可用命令
make help

# 快速编译所有模块
make package

# 部署到Tomcat
make deploy-tomcat

# 检查部署状态
make status

# 查看日志
make logs
```

### 方式二：使用Maven命令
```bash
# 完整编译流程
mvn clean install -DskipTests

# 只编译Web应用
mvn clean package -pl spup-admin-web,spup-user-web -am -DskipTests

# 并行编译(更快)
mvn clean package -DskipTests -T 1C
```

### 方式三：使用自定义脚本
```bash
# 使用快速编译脚本
./scripts/quick-build.sh

# 使用部署脚本
./scripts/deploy-tomcat.sh
```

## 📦 编译输出

编译成功后，WAR文件位置：
- **管理后台**: `spup-admin-web/target/spup-admin.war`
- **用户端**: `spup-user-web/target/spup.war`

## 🚀 Tomcat部署

### 自动部署 (推荐)
```bash
# 设置Tomcat环境变量
export TOMCAT_HOME=/opt/tomcat

# 一键部署
make deploy-tomcat
```

### 手动部署
```bash
# 1. 停止Tomcat
$TOMCAT_HOME/bin/shutdown.sh

# 2. 清理旧部署
rm -rf $TOMCAT_HOME/webapps/spup*

# 3. 复制WAR文件
cp spup-admin-web/target/spup-admin.war $TOMCAT_HOME/webapps/
cp spup-user-web/target/spup.war $TOMCAT_HOME/webapps/

# 4. 启动Tomcat
$TOMCAT_HOME/bin/startup.sh
```

## 🌐 访问地址

部署成功后：
- **管理后台**: http://localhost:8080/spup-admin
- **用户端**: http://localhost:8080/spup

## 🔍 故障排除

### 编译问题
```bash
# 清理Maven缓存
mvn dependency:purge-local-repository

# 强制更新依赖
mvn clean install -U -DskipTests

# 检查Java版本
java -version  # 需要JDK 8+
```

### 部署问题
```bash
# 检查Tomcat状态
make status

# 查看日志
make logs

# 检查端口占用
netstat -tlnp | grep :8080
```

### 常见错误
1. **ClassNotFoundException**: 检查依赖是否正确打包
2. **端口冲突**: 修改Tomcat端口或停止占用进程
3. **内存不足**: 增加Tomcat内存设置

## ⚙️ 环境配置

### 开发环境
```bash
# 设置环境变量
export JAVA_HOME=/path/to/jdk8
export MAVEN_HOME=/path/to/maven
export TOMCAT_HOME=/path/to/tomcat

# 添加到PATH
export PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$PATH
```

### 数据库配置
确保数据库配置正确：
- `spup-admin-web/src/main/resources/application.yml`
- `spup-user-web/src/main/resources/application.yml`

## 📊 性能优化

### 编译优化
```bash
# 并行编译
mvn clean package -DskipTests -T 1C

# 离线模式(如果依赖已下载)
mvn clean package -DskipTests -o

# 跳过不必要的插件
mvn clean package -DskipTests -Dmaven.javadoc.skip=true
```

### Tomcat优化
```bash
# 增加内存
export CATALINA_OPTS="-Xms512m -Xmx2048m"

# 启用JMX监控
export CATALINA_OPTS="$CATALINA_OPTS -Dcom.sun.management.jmxremote"
```

## 🎯 常用命令总结

```bash
# 开发阶段
make package          # 编译打包
make admin           # 只编译管理后台
make user            # 只编译用户端

# 部署阶段
make deploy-tomcat   # 部署到本地Tomcat
make status          # 检查部署状态
make logs            # 查看日志

# 完整流程
make deploy          # 编译+部署+检查状态

# 故障排除
make clean           # 清理编译文件
mvn dependency:tree  # 查看依赖树
```

## 🔄 持续集成

### Jenkins Pipeline示例
```groovy
pipeline {
    agent any
    stages {
        stage('Build') {
            steps {
                sh 'make package'
            }
        }
        stage('Deploy') {
            steps {
                sh 'make deploy-tomcat'
            }
        }
        stage('Verify') {
            steps {
                sh 'make status'
            }
        }
    }
}
```

## 📝 最佳实践

1. **使用Makefile**: 简化常用操作
2. **并行编译**: 使用 `-T 1C` 参数
3. **跳过测试**: 开发阶段使用 `-DskipTests`
4. **环境分离**: 使用不同的配置文件
5. **日志监控**: 定期检查应用日志
6. **版本管理**: 使用Git标签管理发布版本

## 🆘 获取帮助

```bash
# 查看Makefile帮助
make help

# 查看Maven帮助
mvn help:help

# 查看项目结构
tree -L 2

# 检查依赖
mvn dependency:tree
```

---

**快速开始**: `make package && make deploy-tomcat && make status` 🚀
