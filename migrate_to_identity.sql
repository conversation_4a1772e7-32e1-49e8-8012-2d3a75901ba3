-- Migration Script: Change AUTO to IDENTITY Strategy
-- Run this BEFORE deploying the code changes

-- Step 1: Check current table structures
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_KEY,
    EXTRA
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('activity_info', 'activity_round_info', 'activity_submit_customer')
  AND COLUMN_NAME = 'id';

-- Step 2: Add AUTO_INCREMENT if not present
-- Only run these if the EXTRA column above doesn't show 'auto_increment'

-- For activity_info table
-- ALTER TABLE activity_info MODIFY COLUMN id BIGINT NOT NULL AUTO_INCREMENT;

-- For activity_round_info table  
-- ALTER TABLE activity_round_info MODIFY COLUMN id BIGINT NOT NULL AUTO_INCREMENT;

-- For activity_submit_customer table
-- ALTER TABLE activity_submit_customer MODIFY COLUMN id BIGINT NOT NULL AUTO_INCREMENT;

-- Step 3: Set AUTO_INCREMENT starting value to avoid conflicts
-- Get current max IDs and set AUTO_INCREMENT to max_id + 1

-- For activity_info
SET @max_id = (SELECT IFNULL(MAX(id), 0) + 1 FROM activity_info);
SET @sql = CONCAT('ALTER TABLE activity_info AUTO_INCREMENT = ', @max_id);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- For activity_round_info
SET @max_id = (SELECT IFNULL(MAX(id), 0) + 1 FROM activity_round_info);
SET @sql = CONCAT('ALTER TABLE activity_round_info AUTO_INCREMENT = ', @max_id);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- For activity_submit_customer
SET @max_id = (SELECT IFNULL(MAX(id), 0) + 1 FROM activity_submit_customer);
SET @sql = CONCAT('ALTER TABLE activity_submit_customer AUTO_INCREMENT = ', @max_id);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 4: Verify the changes
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COLUMN_TYPE,
    EXTRA,
    AUTO_INCREMENT
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN INFORMATION_SCHEMA.TABLES t ON c.TABLE_NAME = t.TABLE_NAME AND c.TABLE_SCHEMA = t.TABLE_SCHEMA
WHERE c.TABLE_SCHEMA = DATABASE() 
  AND c.TABLE_NAME IN ('activity_info', 'activity_round_info', 'activity_submit_customer')
  AND c.COLUMN_NAME = 'id';

-- Step 5: Clean up hibernate_sequence table (optional)
-- Only drop this after confirming everything works
-- DROP TABLE IF EXISTS hibernate_sequence;
