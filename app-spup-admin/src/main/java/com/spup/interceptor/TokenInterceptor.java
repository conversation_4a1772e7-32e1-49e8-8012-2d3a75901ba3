package com.spup.interceptor;

import com.auth0.jwt.exceptions.AlgorithmMismatchException;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.api.ResultCodeEnum;
import com.huangdou.commons.utils.JWTUtil;
import com.spup.db.entity.authority.AppManageUser;
import com.spup.service.IAppManageUserService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Slf4j
@Component
public class TokenInterceptor implements HandlerInterceptor {

    @Resource
    private JWTUtil jwtUtil;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Value("${manager.unionid: ojqzL0-hOlek3HMyLjhvKjTfDnnA}")
    private String managerUnionid;

    @Resource
    private IAppManageUserService manageUserService;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler) throws Exception {

        // Skip OPTIONS requests
        if (RequestMethod.OPTIONS.name().equals(request.getMethod())) {
            log.debug("OPTIONS request, skipping token validation");
            return true;
        }

        // Skip login endpoints
        String servletPath = request.getServletPath();
        if (servletPath.startsWith("/login/")) {
            log.debug("Login endpoint, bypassing validation: {}", servletPath);
            return true;
        }

        // Development mode bypass
        if (isDevelopmentMode(request)) {
            log.debug("Development mode, bypassing token validation");
            setDefaultSession(request);
            return true;
        }

        // Validate JWT token
        String token = request.getHeader("Authorization");
        if (token == null) {
            log.warn("Missing token in request header");
            writeErrorResponse(response, CommonResult.failed(ResultCodeEnum.REQUEST_TOKEN_EMPTY));
            return false;
        }

        try {
            return validateTokenAndSetSession(token, request);
        } catch (Exception e) {
            log.warn("Token validation failed: {}", e.getMessage());
            writeErrorResponse(response, getErrorResult(e));
            return false;
        }
    }

    @Override
    public void postHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler,
            @Nullable ModelAndView modelAndView) {
        // No post-processing needed
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
            @NonNull Object handler,
            @Nullable Exception ex) {
        // No cleanup needed
    }

    private boolean isDevelopmentMode(HttpServletRequest request) {
        return request.getServerName().contains("localhost") && !"pro".equals(activeProfile);
    }

    private void setDefaultSession(HttpServletRequest request) {
        request.getSession().setAttribute("unionid", managerUnionid);
    }

    private boolean validateTokenAndSetSession(String token, HttpServletRequest request) throws Exception {
        DecodedJWT decodedJWT = jwtUtil.decodeToken(token);

        String unionid = decodedJWT.getClaim("unionid").asString();
        String openid = decodedJWT.getClaim("openid").asString();

        log.debug("Token validation for unionid: {}", unionid);

        // Update session if needed
        String sessionUnionid = (String) request.getSession().getAttribute("unionid");
        if (!unionid.equals(sessionUnionid)) {
            request.getSession().setAttribute("unionid", unionid);
            request.getSession().setAttribute("openid", openid);
        }

        // Verify user exists
        AppManageUser manageUser = manageUserService.getUserByUnionid(unionid);
        if (manageUser == null) {
            throw new SecurityException("User not found for unionid: " + unionid);
        }

        log.debug("User validation successful: {}", manageUser.getName());
        return true;
    }

    private CommonResult<ResultCodeEnum> getErrorResult(Exception e) {
        if (e instanceof SignatureVerificationException) {
            log.warn("Invalid JWT signature");
            return CommonResult.failed(ResultCodeEnum.GET_TOKEN_KEY_FAILED);
        } else if (e instanceof TokenExpiredException) {
            log.warn("JWT token expired");
            return CommonResult.failed(ResultCodeEnum.AUTHORIZED_FAILED);
        } else if (e instanceof AlgorithmMismatchException) {
            log.warn("JWT algorithm mismatch");
            return CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        } else {
            log.warn("JWT token validation failed: {}", e.getMessage());
            return CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        }
    }

    private void writeErrorResponse(HttpServletResponse response, CommonResult<ResultCodeEnum> result) {
        try {
            String json = objectMapper.writeValueAsString(result);
            returnJson(response, json);
        } catch (Exception ex) {
            log.error("Failed to write error response", ex);
        }
    }

    private void returnJson(HttpServletResponse response, String json) throws Exception {
        // Check if response is already committed to avoid IllegalStateException
        if (response.isCommitted()) {
            log.warn("Response is already committed, cannot write JSON response");
            return;
        }

        PrintWriter writer = null;
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try {
            writer = response.getWriter();
            writer.print(json);
            writer.flush(); // Ensure data is written
        } catch (IOException e) {
            log.error("Error writing JSON response", e);
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (Exception e) {
                    log.error("Error closing writer", e);
                }
            }
        }
    }
}
