package com.spup.controller.appointment;

import com.huangdou.commons.api.CommonResult;
import com.spup.dto.AppBatchSetDTO;
import com.spup.service.appointment.IAppBatchSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@Api(tags = "场次时间设定",position = 8)
@RestController
@RequestMapping(value = "/batchSet")
public class AppBatchSetController {
    @Resource
    private IAppBatchSetService iAppBatchSetService;

    @ApiOperation(value = "查询最新设定",notes="便于用户修改后续")
    @GetMapping(value="/getLastSet/{batchCategory}")
    public CommonResult<?> listByPage (@PathVariable Byte batchCategory)  {
        return CommonResult.succeeded(iAppBatchSetService.getLastSet(batchCategory));
    }

    @ApiOperation(value = "创建新设定规则")
    @PostMapping(value="/createSet")
    public CommonResult<?> createSet (@RequestBody AppBatchSetDTO appBatchSetDTO, HttpServletRequest req)  {
        String openid = (String)req.getSession().getAttribute("openid");
        iAppBatchSetService.save(appBatchSetDTO,openid);
        return CommonResult.succeeded(appBatchSetDTO);
    }
}
