package com.spup.controller.others;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huangdou.commons.api.CommonResult;
import com.spup.db.entity.CommQuestionnaireAnswer;
import com.spup.dto.QuestionnaireListRequest;
import com.spup.service.ICommQuestionnaireAnswerService;
import com.spup.service.ICommQuestionnaireService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;

import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "问卷管理" , position = 6)
@RestController
@RequestMapping(value = "/questionnaire")
public class QuestionnaireController {
    @Resource
    private ICommQuestionnaireService iCommQuestionnaireService;
    @Resource
    private ICommQuestionnaireAnswerService iCommQuestionnaireAnswerService;
    @Resource
    private ObjectMapper objectMapper;

    @ApiOperation(value = "问卷列表(分页)")
    @PostMapping(value="/listByPage")
    public CommonResult<?> listByPage (@RequestBody QuestionnaireListRequest param) {
        return CommonResult.succeeded(iCommQuestionnaireService.getPageList(param));
    }

    @ApiOperation(value = "查看问卷填报（分页）")
    @GetMapping(value={"/viewAnswer/"})
    public CommonResult<?> viewAnswer (@PathVariable Long questionnaireId,QuestionnaireListRequest param) {
        if(questionnaireId==null){
            questionnaireId = 1L;
        }
        if(param.getQuestionnaireId()==null){
            param.setQuestionnaireId(questionnaireId);
        }
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }
    @ApiOperation(value = "查看问卷填报（分页）")
    @PostMapping(value={"/viewAnswer/{questionnaireId}"})
    public CommonResult<?> viewAnswer2 (@PathVariable Long questionnaireId, @RequestBody QuestionnaireListRequest param) {
        if(questionnaireId==null){
            questionnaireId = 1L;
        }
        if(param.getQuestionnaireId()==null){
            param.setQuestionnaireId(questionnaireId);
        }
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }

    @ApiOperation(value = "查看问卷填报（分页）")
    @GetMapping(value={"/viewAnswer"})
    public CommonResult<?> viewAnswer3 (@RequestBody QuestionnaireListRequest param) {
        Page<CommQuestionnaireAnswer> pageList = iCommQuestionnaireAnswerService.getPageList(param);
        return CommonResult.succeeded(pageList);
    }
    @ApiOperation(value = "查看问卷填报详情")
    @GetMapping(value="/viewAnswerDetail/{answerId}")
    public CommonResult<?> viewAnswerDetail (@PathVariable long answerId) throws JsonProcessingException {

        return CommonResult.succeeded(iCommQuestionnaireAnswerService.getAnswerDetail(answerId));
    }
}
