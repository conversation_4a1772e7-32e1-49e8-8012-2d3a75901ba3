package com.spup.controller.others;

import com.huangdou.commons.api.CommonResult;
import com.spup.service.appointment.IAppAppointmentInstructionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.Map;

@Api(tags = "参观须知等更新", position = 4)
@RestController
@RequestMapping(value = "/instructions")
public class AppInstructionsController {
    @Resource
    private IAppAppointmentInstructionsService iAppAppointmentInstructionsService;

    @ApiOperation(value = "查询")
    @GetMapping(value = "/get")
    public CommonResult<?> get() throws UnsupportedEncodingException {
        return CommonResult.succeeded(iAppAppointmentInstructionsService.get());
    }

    @ApiOperation(value = "更新")
    @PostMapping(value = "/update")
    public CommonResult<?> update(@RequestBody Map<String, String> map, HttpServletRequest req)
            throws UnsupportedEncodingException {
        String audienceNotice = (String) map.get("audienceNotice");
        String visitingInstructions = (String) map.get("visitingInstructions");
        String admissionNotice = (String) map.get("admissionNotice");

        String unionid = (String) req.getSession().getAttribute("unionid");
        return CommonResult.succeeded(iAppAppointmentInstructionsService.update(audienceNotice, visitingInstructions,
                admissionNotice, unionid));
    }
}
