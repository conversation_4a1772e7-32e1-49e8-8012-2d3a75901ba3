package com.spup.controller.auth;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.utils.JWTUtil;
import com.spup.db.entity.authority.AppManageUser;
import com.spup.service.IAppManageUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Api(tags = "登录")
@RestController
@RequestMapping(value = "/login")
public class AppLoginController {
    private static final Logger logger = LoggerFactory.getLogger(AppLoginController.class);

    @Resource
    private JWTUtil jwtUtil;
    @Resource
    private RestTemplate restTemplate;

    @Value("${wx.open.appId}")
    private String openAppid;
    @Value("${wx.open.secret}")
    private String openSecret;

    @Resource
    private IAppManageUserService appManageUserService;
    @Resource
    private ObjectMapper objectMapper;

    @ApiOperation(value = "登录")
    @GetMapping(value = "/oauth2/access_token/{code}")
    public CommonResult<?> jscode2session(@PathVariable String code) {
        try {
            logger.info("OAuth2 login with code: {}", code);
            String url = String.format(
                    "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
                    openAppid, openSecret, code);

            // call OAuth2 api to get access_token and openid, the response result maybe like :
            // {
            // "access_token":
            // "93_s-0FFwMV3qohqMdgxJxCVLBwad253-fsRra2BBGhYxnt8BkZ_zd1KmlTLSy1mO2l0zrEswT9d3FZoQzr8bcSNnUAQrnDdnxZGmS_6copsfo",
            // "expires_in": 7200,
            // "refresh_token":
            // "93_hVLNw-1kPhfRAlT517K0FxuGoub5jKmKLOai07AjKCOd0LaTKhGOa0mfMse1Hv_Gci6g1jW42gVvL0KgoVvdZqiBtuhCRxWMCEIOAjVN4A8",
            // "openid": "oRdSH1ODxhtZndpbbJy0wqwrCnc4",
            // "scope": "snsapi_login",
            // "unionid": "ojqzL0-hOlek3HMyLjhvKjTfDnnA"
            // }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            String responseStr = responseEntity.getBody();
            logger.info("responseStr: {}", responseStr);

            if (responseStr == null || responseStr.trim().isEmpty()) {
                logger.error("Empty response from WeChat API");
                return CommonResult.failed(1000, "WeChat API returned empty response");
            }

            ObjectNode jsonObject = (ObjectNode) objectMapper.readTree(responseStr);
            if (jsonObject.get("errcode") == null) {

                String openid = jsonObject.has("openid") ? jsonObject.get("openid").textValue() : null;
                String unionid = jsonObject.has("unionid") ? jsonObject.get("unionid").textValue() : null;

                if (openid == null || unionid == null) {
                    logger.error("Missing openid or unionid in WeChat response: openid={}, unionid={}", openid,
                            unionid);
                    return CommonResult.failed(1000, "Invalid WeChat response: missing openid or unionid");
                }

                logger.info("用户登录openid：" + openid + ",unionid：" + unionid);

                AppManageUser userByOpenId = appManageUserService.getUserByUnionid(unionid);
                if (userByOpenId == null) {
                    logger.warn("User not found for unionid: {}", unionid);
                    return CommonResult.failed(1000, "用户不存在");
                }
                logger.info("用户登录成功：{}", userByOpenId);

                String token = jwtUtil.getToken(unionid, openid, 780);
                logger.info("token is generated: {}", token);

                ObjectNode returnObj = objectMapper.createObjectNode();
                returnObj.put("openid", openid);
                returnObj.put("unionid", unionid);
                returnObj.put("token", token);
                returnObj.put("validTime", 780);

                // Only include manageUser if it can be safely serialized
                try {
                    returnObj.set("manageUser", objectMapper.valueToTree(userByOpenId));
                } catch (Exception jsonEx) {
                    logger.warn("Failed to serialize manageUser, excluding from response: {}", jsonEx.getMessage());
                    // Continue without manageUser in response
                }

                return CommonResult.succeeded(returnObj);
            } else {
                Integer errcode = jsonObject.has("errcode") ? jsonObject.get("errcode").intValue() : -1;
                String errmsg = jsonObject.has("errmsg") ? jsonObject.get("errmsg").textValue() : "Unknown error";
                logger.info("用户登录失败：{}", errcode);
                return CommonResult.failed(1000, errcode + ":" + errmsg);
            }
        } catch (Exception e) {
            logger.error("Error in OAuth2 login process", e);
            return CommonResult.failed(1000, "Login process failed: " + e.getMessage());
        }
    }

    @ApiOperation(value = "扫码获取openid")
    @GetMapping(value = "/jscode2openid/{code}")
    public CommonResult<?> jscode2openid(@PathVariable String code) {
        try {
            logger.info("pad-admin entry into jscode2session,code: {}", code);

            String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + openAppid + "&secret=" + openSecret
                    + "&js_code=" + code + "&grant_type=authorization_code";
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            String responseStr = responseEntity.getBody();

            if (responseStr == null || responseStr.trim().isEmpty()) {
                logger.error("Empty response from WeChat API for jscode2openid");
                return CommonResult.failed(1000, "WeChat API returned empty response");
            }

            ObjectNode jsonObject = (ObjectNode) objectMapper.readTree(responseStr);
            if (jsonObject.get("errcode") == null) {
                String openid = jsonObject.has("openid") ? jsonObject.get("openid").textValue() : null;
                String unionid = jsonObject.has("unionid") ? jsonObject.get("unionid").textValue() : null;

                if (openid == null) {
                    logger.error("Missing openid in WeChat response");
                    return CommonResult.failed(1000, "Invalid WeChat response: missing openid");
                }

                ObjectNode returnObj = objectMapper.createObjectNode();
                returnObj.put("openid", openid);
                if (unionid != null) {
                    returnObj.put("unionid", unionid);
                }
                return CommonResult.succeeded(returnObj);
            } else {
                Integer errcode = jsonObject.has("errcode") ? jsonObject.get("errcode").intValue() : -1;
                String errmsg = jsonObject.has("errmsg") ? jsonObject.get("errmsg").textValue() : "Unknown error";
                return CommonResult.failed(1000, errcode + ":" + errmsg);
            }
        } catch (Exception e) {
            logger.error("Error in jscode2openid process", e);
            return CommonResult.failed(1000, "jscode2openid process failed: " + e.getMessage());
        }
    }

}
