package com.spup.service.appointment.impl;

import com.huangdou.commons.api.CommonResult;
import com.huangdou.commons.utils.NumberGenerator;
import com.spup.db.dao.appointment.AppAppointmentTeamOrderDao;
import com.spup.db.entity.appointment.AppAppointmentTeamOrder;
import com.spup.dto.AppTeamOrderListRequest;
import com.spup.dto.OrderRequest;
import com.spup.enums.OrderCategoryEnum;
import com.spup.enums.OrderStatusEnum;
import com.spup.enums.TeamOrderStatusEnum;
import com.spup.service.appointment.IAppAppointmentTeamOrderService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import java.util.Optional;

@Slf4j
@Service
public class AppAppointmentTeamOrderServiceImpl implements IAppAppointmentTeamOrderService {
    @Resource
    private AppAppointmentTeamOrderDao appAppointmentTeamOrderDao;

    @Override
    public CommonResult<?> delete(String orderNo,String unionid) {
        Optional<AppAppointmentTeamOrder> orderOptional =  appAppointmentTeamOrderDao.findByOrderNo(orderNo);
        if(!orderOptional.isPresent()){
            return CommonResult.failed("订单未找到");
        }
        AppAppointmentTeamOrder order = orderOptional.get();
        order.setDeleted((byte)1);
        order.setUpdateBy(unionid);
        appAppointmentTeamOrderDao.save(order);

        return CommonResult.succeeded(1);
    }


    @Override
    public List<AppAppointmentTeamOrder> getTeamOrderByDate(String startDate, String endDate) {
        return appAppointmentTeamOrderDao.findByBatchDateBetween(startDate,endDate);
    }

    @Override
    public AppAppointmentTeamOrder update(AppAppointmentTeamOrder modifyOrder) {
        return null;
    }

    @Override
    public AppAppointmentTeamOrder save(AppAppointmentTeamOrder teamOrder) {
        if(!StringUtils.hasLength(teamOrder.getOrderNo())){
            teamOrder.setOrderNo(createOrderNo());
        }
        if(teamOrder.getOrderCategory()==null) {
            if (StringUtils.hasLength(teamOrder.getExhibitionNo())) {
                teamOrder.setOrderCategory(OrderCategoryEnum.EXHIBITION_TEAM.getCode());
            } else {
                teamOrder.setOrderCategory(OrderCategoryEnum.TEAM.getCode());
            }
        }
        teamOrder.setOrderStatus(TeamOrderStatusEnum.CONFIRM.getCode());
        return appAppointmentTeamOrderDao.save(teamOrder);
    }
    @Override
    public AppAppointmentTeamOrder view(Long id) {
        Optional<AppAppointmentTeamOrder> teamOrderOptional = appAppointmentTeamOrderDao.findById(id);
        return teamOrderOptional.orElse(null);
    }

    @Override
    public CommonResult<?> checkout(OrderRequest orderRequest,String unionid) {
        String suborderNo = orderRequest.getSuborderId();
        Optional<AppAppointmentTeamOrder> orderOptional = appAppointmentTeamOrderDao.findByOrderNo(suborderNo);
        if(!orderOptional.isPresent()){
            return CommonResult.failed("未找到该预约信息");
        }
        AppAppointmentTeamOrder order = orderOptional.get();
        if(order.getOrderStatus() == OrderStatusEnum.CANCELED.getCode()){
            return CommonResult.failed("该预约已取消");
        }
        if(order.getOrderStatus() == OrderStatusEnum.FINISHED.getCode()){
            return CommonResult.failed("该预约已核销");
        }


        order.setOrderStatus(OrderStatusEnum.FINISHED.getCode());
        order.setUpdateTime(LocalDateTime.now());

        appAppointmentTeamOrderDao.save(order);
        return CommonResult.succeeded("");
    }

    @Override
    public CommonResult<?> getSuborderDetail(String subOrderId) {
        return null;
    }


    @Override
    public Page<AppAppointmentTeamOrder> getList(AppTeamOrderListRequest queryParam) {
        log.info("🔍 BREAKPOINT TEST: queryParam:{}", queryParam);
        log.info("🔍 BREAKPOINT TEST: Method=getList, Thread={}, Time={}",
                Thread.currentThread().getName(), System.currentTimeMillis());
        log.info("🔍 BREAKPOINT TEST: Application=app-spup-admin, Module=AppAppointmentTeamOrderServiceImpl");
        log.info("🔍 BREAKPOINT TEST: JVM Debug Port Check - look for -agentlib:jdwp in JVM args");
        Pageable pageable = PageRequest.of(queryParam.getPageNum()-1, queryParam.getPageSize());
        Specification<AppAppointmentTeamOrder> spec = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            // Add exhibitionNo filter if provided
            if(StringUtils.hasLength(queryParam.getExhibitionNo())){
                list.add(cb.equal(root.get("exhibitionNo"), queryParam.getExhibitionNo()));
            }

            // Add orderStatus filter if provided
            if(queryParam.getOrderStatus() != null){
                list.add(cb.equal(root.get("orderStatus"), queryParam.getOrderStatus()));
            }

            if(queryParam.getStartDate()!=null){
                list.add(cb.greaterThanOrEqualTo(root.get("batchDate"), queryParam.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))));//筛选
            }
            if(queryParam.getEndDate()!=null){
                list.add(cb.lessThanOrEqualTo(root.get("batchDate"),queryParam.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))));//筛选
            }
            if(StringUtils.hasLength(queryParam.getTeamName())){
                list.add(cb.like(root.get("owerUnit"),"%"+queryParam.getTeamName()+"%"));//筛选
            }
            if(queryParam.getMethod()!=null){
                list.add(cb.equal(root.get("method"),queryParam.getMethod()));//筛选
            }

            query.orderBy(cb.desc(root.get("batchDate")));//排序

            // Handle empty predicate list safely
            if(list.isEmpty()) {
                return cb.conjunction(); // Returns a predicate that is always true
            }

            Predicate[] arr = new Predicate[list.size()];
            return cb.and(list.toArray(arr));
        };

        Page<AppAppointmentTeamOrder> result = appAppointmentTeamOrderDao.findAll(spec, pageable);
        return result;
    }

    private String createOrderNo(){
        return "offline"+ NumberGenerator.getOrderNo();
    }
}
