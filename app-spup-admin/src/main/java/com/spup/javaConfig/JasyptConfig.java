package com.spup.javaConfig;

import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Jasypt Configuration for property encryption
 * 
 * Usage:
 * 1. Set environment variable: JASYPT_ENCRYPTOR_PASSWORD=your_secret_password
 * 2. Encrypt sensitive values using jasypt CLI or programmatically
 * 3. Use encrypted values in properties: password: ENC(encrypted_value)
 */
@Configuration
public class JasyptConfig {

    @Value("${jasypt.encryptor.password:}")
    private String encryptorPassword;

    @Bean("jasyptStringEncryptor")
    public StringEncryptor stringEncryptor() {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        
        // Use environment variable or default password
        String password = encryptorPassword.isEmpty() ? "defaultPassword" : encryptorPassword;
        config.setPassword(password);
        
        // Strong encryption algorithm
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        
        // Key obtention iterations (higher = more secure but slower)
        config.setKeyObtentionIterations("1000");
        
        // Pool size for better performance
        config.setPoolSize("1");
        
        // Salt generator
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        
        // IV generator for AES
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        
        // String output type
        config.setStringOutputType("base64");
        
        encryptor.setConfig(config);
        return encryptor;
    }
}
