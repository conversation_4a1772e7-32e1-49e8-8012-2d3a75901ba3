package com.spup.javaConfig;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@ConfigurationProperties(prefix = "appointment") // 配置 文件的前缀
public class AppointmentConfig {
    Map<String,AppointmentConfigDetail> config;

    public AppointmentConfigDetail getConfig(String exhibitionId) {
        if(config == null|| config.isEmpty()){
            return null;
        }
        return config.get(exhibitionId);
    }

    public void setConfig(Map<String, AppointmentConfigDetail> config) {
        this.config = config;
    }
}
