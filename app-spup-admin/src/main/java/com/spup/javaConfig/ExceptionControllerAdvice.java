package com.spup.javaConfig;

import com.huangdou.commons.api.CommonResult;
import com.spup.exception.ValidException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
@Slf4j
@RestControllerAdvice(basePackages="com.spup")
public class ExceptionControllerAdvice {

    @ExceptionHandler(value=MethodArgumentNotValidException.class)
    public CommonResult<?> handleValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        try {
            log.error("=== ORIGINAL EXCEPTION: MethodArgumentNotValidException ===");
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("数据校验出现问题：{}，异常类型：{}", e.getMessage(), e.getClass());
            log.error("Full exception details:", e);

            BindingResult bindingResult = e.getBindingResult();
            StringBuffer stringBuffer = new StringBuffer();
            bindingResult.getFieldErrors().forEach( item -> {
                String message = item.getDefaultMessage();
                stringBuffer.append(message + ";");
            });
            return CommonResult.failed(stringBuffer.toString());
        } catch (Exception handlerException) {
            log.error("=== EXCEPTION IN EXCEPTION HANDLER ===");
            log.error("Original exception was: MethodArgumentNotValidException - {}", e.getMessage());
            log.error("Handler exception:", handlerException);
            return createSafeErrorResponse("数据校验失败");
        }
    }

    @ExceptionHandler(value=ValidException.class)
    public CommonResult<?> handleValidException(ValidException e, HttpServletRequest request) {
        try {
            log.error("=== ORIGINAL EXCEPTION: ValidException ===");
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("请求数据验证失败:", e);
            return CommonResult.failed(e.getMessage());
        } catch (Exception handlerException) {
            log.error("=== EXCEPTION IN EXCEPTION HANDLER ===");
            log.error("Original exception was: ValidException - {}", e.getMessage());
            log.error("Handler exception:", handlerException);
            return createSafeErrorResponse("数据验证失败");
        }
    }

    @ExceptionHandler(value=Exception.class)
    public CommonResult<?> handleGeneralExceptions(Exception e, HttpServletRequest request) {
        try {
            log.error("=== ORIGINAL EXCEPTION: {} ===", e.getClass().getSimpleName());
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("Exception message: {}", e.getMessage());
            log.error("系统异常 - Full stack trace:", e);

            // Log the cause chain
            Throwable cause = e.getCause();
            int level = 1;
            while (cause != null && level <= 5) {
                log.error("Caused by (level {}): {} - {}", level, cause.getClass().getSimpleName(), cause.getMessage());
                cause = cause.getCause();
                level++;
            }

            return CommonResult.failed("系统异常");
        } catch (Exception handlerException) {
            log.error("=== EXCEPTION IN EXCEPTION HANDLER ===");
            log.error("Original exception was: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            log.error("Handler exception:", handlerException);
            return createSafeErrorResponse("系统异常");
        }
    }

    @ExceptionHandler(value=Throwable.class)
    public CommonResult<?> handleException(Throwable throwable, HttpServletRequest request) {
        try {
            log.error("=== ORIGINAL THROWABLE: {} ===", throwable.getClass().getSimpleName());
            log.error("Request URL: {}", request.getRequestURL());
            log.error("Request Method: {}", request.getMethod());
            log.error("系统错误 - Full details:", throwable);
            return CommonResult.failed("系统错误");
        } catch (Exception handlerException) {
            log.error("=== EXCEPTION IN EXCEPTION HANDLER ===");
            log.error("Original throwable was: {} - {}", throwable.getClass().getSimpleName(), throwable.getMessage());
            log.error("Handler exception:", handlerException);
            return createSafeErrorResponse("系统错误");
        }
    }

    /**
     * Creates a safe error response that doesn't trigger additional exceptions
     */
    private CommonResult<?> createSafeErrorResponse(String message) {
        try {
            return CommonResult.failed(message);
        } catch (Exception e) {
            log.error("Even safe error response failed:", e);
            // Return the most basic response possible using the protected constructor
            return CommonResult.failed();
        }
    }


}
