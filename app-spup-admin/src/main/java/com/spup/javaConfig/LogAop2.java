package com.spup.javaConfig;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.spup.service.IAppOperateLogService;
import org.apache.catalina.connector.RequestFacade;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Objects;

/**
 * 统一日志处理切面
 * Created by sj
 */
@Aspect
@Component
public class LogAop2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogAop2.class);

    @Resource
    private IAppOperateLogService iAppOperateLogService;

    @Resource
    private ObjectMapper objectMapper;

    @Pointcut("execution(public * com.spup.controller..*.*(..))")
    public void webLogForController() {
    }

    @Before("webLogForController() ")
    public void doBefore(JoinPoint joinPoint) {
        // 接收到请求，记录请求内容
        StringBuilder methodParams = new StringBuilder("");
        Arrays.stream(joinPoint.getArgs())
                .filter(o -> !(o instanceof RequestFacade))
                .forEach(o -> {
            try {
                // Safe serialization that won't interfere with response handling
                if (o != null) {
                    String serialized = objectMapper.writeValueAsString(o);
                    methodParams.append(serialized);
                }
            } catch (JsonProcessingException e) {
                // Log the error but don't let it break the request
                LOGGER.warn("Failed to serialize parameter of type {}: {}",
                           o != null ? o.getClass().getSimpleName() : "null", e.getMessage());
                methodParams.append("[Serialization failed: ").append(o != null ? o.getClass().getSimpleName() : "null").append("]");
            } catch (Exception e) {
                // Catch any other serialization issues
                LOGGER.warn("Unexpected error serializing parameter: {}", e.getMessage());
                methodParams.append("[Error: ").append(e.getMessage()).append("]");
            }
        });

        //获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        if (Objects.isNull(attributes)) {
            LOGGER.warn("RequestContextHolder.getRequestAttributes() returned null, skipping log processing");
            return; // Don't throw exception, just return
        } else {
            request = attributes.getRequest();
        }

        try {
            String urlStr = request.getRequestURL().toString();
            String openid = (String)request.getSession().getAttribute("openid");
            LOGGER.info("🔍 AOP LOG: {},{},{},{}",joinPoint.getTarget().getClass().getSimpleName(),urlStr,openid, methodParams.toString());

            // Save log asynchronously to avoid blocking the main request
            try {
                iAppOperateLogService.saveLog(request, methodParams.toString());
            } catch (Exception logSaveException) {
                LOGGER.warn("Failed to save operation log: {}", logSaveException.getMessage());
                // Don't let log saving failure affect the main request
            }
        } catch (Exception e){
            LOGGER.error("Error in LogAop2.doBefore: {}", e.getMessage());
            // Don't rethrow the exception to avoid interfering with the main request processing
        }
    }

    @AfterReturning(returning = "data", pointcut = "webLogForController()")
    public void doAfterReturning(JoinPoint joinPoint, Object data) {
        try {
            String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName();

            // Safe logging without serializing the response data to avoid conflicts
            String dataInfo = data != null ? data.getClass().getSimpleName() : "null";
            LOGGER.info("🔍 AOP COMPLETE: Method {} completed successfully, returned: {}", methodName, dataInfo);

            // Don't serialize the response data here as it can cause getWriter() conflicts
            // The response will be serialized by Spring's message converters

        } catch (Exception e) {
            LOGGER.error("Error in LogAop2.doAfterReturning: {}", e.getMessage());
            // Don't rethrow to avoid interfering with response handling
        }
    }
}



